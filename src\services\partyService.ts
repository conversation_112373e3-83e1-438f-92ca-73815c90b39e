import { api } from './api';

export interface Party {
  id: string;
  name: string;
  description?: string;
  picture?: string;
  leaderId: number;
  leader?: {
    id: number;
    username: string;
    level?: number;
  };
  regionId: string;
  region?: {
    id: string;
    name: string;
  };
  members: {
    id: number;
    username: string;
    level?: number;
    joinedAt: string;
  }[];
  memberCount: number;
  maxMembers: number;
  createdAt: string;
  updatedAt: string;
}

export interface PartyJoinRequest {
  id: string;
  userId: number;
  user?: {
    id: number;
    username: string;
    level?: number;
  };
  partyId: string;
  party?: {
    id: string;
    name: string;
  };
  message?: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
}

export const partyService = {
  // Party management
  createParty: async (partyData: {
    name: string;
    description?: string;
    regionId: string;
  }): Promise<Party> => {
    const response = await api.post('/party', partyData);
    return response.data;
  },

  getPartyById: async (partyId: string): Promise<Party> => {
    const response = await api.get(`/party/${partyId}`);
    return response.data;
  },

  getAllParties: async (): Promise<Party[]> => {
    const response = await api.get('/party');
    return response.data;
  },

  getPartiesByRegion: async (regionId: string): Promise<Party[]> => {
    const response = await api.get(`/party/region/${regionId}`);
    return response.data;
  },

  updateParty: async (partyId: string, updates: {
    name?: string;
    description?: string;
  }): Promise<Party> => {
    const response = await api.patch(`/party/${partyId}`, updates);
    return response.data;
  },

  // Membership
  joinParty: async (partyId: string): Promise<Party> => {
    const response = await api.post(`/party/${partyId}/join`);
    return response.data;
  },

  leaveParty: async (partyId: string): Promise<void> => {
    await api.post(`/party/${partyId}/leave`);
  },

  // Leadership
  transferLeadership: async (partyId: string, newLeaderId: number): Promise<Party> => {
    const response = await api.post(`/party/${partyId}/transfer-leadership/${newLeaderId}`);
    return response.data;
  },

  kickMember: async (partyId: string, memberId: number): Promise<Party> => {
    const response = await api.delete(`/party/${partyId}/kick/${memberId}`);
    return response.data;
  },

  // Party images
  uploadPartyPicture: async (partyId: string, imageFile: FormData): Promise<Party> => {
    const response = await api.post(`/party/${partyId}/picture`, imageFile, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Join requests
  createJoinRequest: async (partyId: string, message?: string): Promise<PartyJoinRequest> => {
    const response = await api.post('/party-requests', { partyId, message });
    return response.data;
  },

  getUserJoinRequests: async (): Promise<PartyJoinRequest[]> => {
    const response = await api.get('/party-requests/user');
    return response.data;
  },

  getPartyJoinRequests: async (partyId: string): Promise<PartyJoinRequest[]> => {
    const response = await api.get(`/party-requests/party/${partyId}`);
    return response.data;
  },

  acceptJoinRequest: async (requestId: string): Promise<PartyJoinRequest> => {
    const response = await api.patch(`/party-requests/${requestId}/accept`);
    return response.data;
  },

  rejectJoinRequest: async (requestId: string): Promise<PartyJoinRequest> => {
    const response = await api.patch(`/party-requests/${requestId}/reject`);
    return response.data;
  },

  cancelJoinRequest: async (requestId: string): Promise<void> => {
    await api.delete(`/party-requests/${requestId}`);
  },
};