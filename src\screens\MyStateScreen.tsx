import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Crown, MapPin, Users, DollarSign, Building } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { stateService } from '../services/stateService';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

interface UserState {
  id: string;
  name: string;
  description?: string;
  leader?: {
    id: number;
    username: string;
  };
  regions?: Array<{
    id: string;
    name: string;
  }>;
  governmentType?: string;
  resources?: {
    money: number;
    gold: number;
    energy: number;
  };
  createdAt: string;
}

export const MyStateScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuthStore();
  useAuthGuard({ navigation });

  const [state, setState] = useState<UserState | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadMyState();
  }, []);

  const loadMyState = async () => {
    try {
      const userState = await stateService.getMyState();
      setState(userState);
    } catch (error: any) {
      console.error('Error loading user state:', error);
      if (error.response?.status === 404) {
        // User is not in any state
        setState(null);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to load your state',
        });
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadMyState();
  };

  const handleViewStateDetails = () => {
    if (state) {
      navigation.navigate('StateBudget', { stateId: state.id });
    }
  };

  const handleViewAllStates = () => {
    navigation.navigate('StatesList');
  };

  const renderStateInfo = () => {
    if (!state) {
      return (
        <View style={{ alignItems: 'center', paddingVertical: 40 }}>
          <Crown width={48} height={48} color="#6B7280" />
          <Text style={{ color: '#9CA3AF', fontSize: 18, marginTop: 16, textAlign: 'center' }}>
            You're not a member of any state
          </Text>
          <Text style={{ color: '#6B7280', fontSize: 14, marginTop: 8, textAlign: 'center' }}>
            Join a state or create your own to participate in politics
          </Text>
          <TouchableOpacity
            onPress={handleViewAllStates}
            style={{
              backgroundColor: '#3f87ff',
              borderRadius: 8,
              paddingHorizontal: 16,
              paddingVertical: 12,
              marginTop: 16,
            }}
          >
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
              View All States
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View>
        {/* State Card */}
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 20,
          marginBottom: 16,
          borderWidth: 1,
          borderColor: '#374151',
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
            <View style={{
              width: 60,
              height: 60,
              backgroundColor: '#374151',
              borderRadius: 30,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 16,
            }}>
              <Text style={{ fontSize: 30 }}>🏛️</Text>
            </View>
            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 4 }}>
                {state.name}
              </Text>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Crown width={16} height={16} color="#F59E0B" />
                <Text style={{ fontSize: 16, color: '#9CA3AF', marginLeft: 6 }}>
                  {state.leader?.username || 'No Leader'}
                </Text>
              </View>
            </View>
          </View>

          {state.description && (
            <Text style={{ 
              fontSize: 16, 
              color: '#D1D5DB', 
              marginBottom: 16,
              fontStyle: 'italic',
              lineHeight: 22
            }}>
              {state.description}
            </Text>
          )}

          {/* State Stats */}
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 16 }}>
            <View style={{ alignItems: 'center' }}>
              <MapPin width={20} height={20} color="#6B7280" />
              <Text style={{ fontSize: 12, color: '#9CA3AF', marginTop: 4 }}>Regions</Text>
              <Text style={{ fontSize: 18, color: '#ffffff', fontWeight: '600' }}>
                {state.regions?.length || 0}
              </Text>
            </View>
            <View style={{ alignItems: 'center' }}>
              <Building width={20} height={20} color="#6B7280" />
              <Text style={{ fontSize: 12, color: '#9CA3AF', marginTop: 4 }}>Government</Text>
              <Text style={{ fontSize: 14, color: '#ffffff', fontWeight: '600' }}>
                {state.governmentType || 'Democracy'}
              </Text>
            </View>
          </View>

          {/* Resources */}
          {state.resources && (
            <View style={{
              backgroundColor: '#374151',
              borderRadius: 8,
              padding: 12,
              marginBottom: 16,
            }}>
              <Text style={{ fontSize: 16, color: '#ffffff', fontWeight: '600', marginBottom: 8 }}>
                State Resources
              </Text>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <View style={{ alignItems: 'center' }}>
                  <DollarSign width={16} height={16} color="#10B981" />
                  <Text style={{ fontSize: 12, color: '#9CA3AF', marginTop: 2 }}>Money</Text>
                  <Text style={{ fontSize: 16, color: '#10B981', fontWeight: '600' }}>
                    ${state.resources.money?.toLocaleString() || 0}
                  </Text>
                </View>
                <View style={{ alignItems: 'center' }}>
                  <Text style={{ fontSize: 16, color: '#F59E0B' }}>🥇</Text>
                  <Text style={{ fontSize: 12, color: '#9CA3AF', marginTop: 2 }}>Gold</Text>
                  <Text style={{ fontSize: 16, color: '#F59E0B', fontWeight: '600' }}>
                    {state.resources.gold?.toLocaleString() || 0}
                  </Text>
                </View>
                <View style={{ alignItems: 'center' }}>
                  <Text style={{ fontSize: 16, color: '#3B82F6' }}>⚡</Text>
                  <Text style={{ fontSize: 12, color: '#9CA3AF', marginTop: 2 }}>Energy</Text>
                  <Text style={{ fontSize: 16, color: '#3B82F6', fontWeight: '600' }}>
                    {state.resources.energy?.toLocaleString() || 0}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Actions */}
          <TouchableOpacity
            onPress={handleViewStateDetails}
            style={{
              backgroundColor: '#3f87ff',
              borderRadius: 8,
              paddingVertical: 12,
              alignItems: 'center',
            }}
          >
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
              View State Details
            </Text>
          </TouchableOpacity>
        </View>

        {/* Regions List */}
        {state.regions && state.regions.length > 0 && (
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
            borderWidth: 1,
            borderColor: '#374151',
          }}>
            <Text style={{ fontSize: 18, color: '#ffffff', fontWeight: '600', marginBottom: 12 }}>
              State Regions
            </Text>
            {state.regions.map((region, index) => (
              <View
                key={region.id}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 8,
                  borderBottomWidth: index < state.regions!.length - 1 ? 1 : 0,
                  borderBottomColor: '#374151',
                }}
              >
                <MapPin width={16} height={16} color="#6B7280" />
                <Text style={{ fontSize: 16, color: '#D1D5DB', marginLeft: 8 }}>
                  {region.name}
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={{ flex: 1, backgroundColor: '#111827', justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#3f87ff" />
        <Text style={{ color: '#3f87ff', fontSize: 18, marginTop: 16 }}>Loading your state...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#111827' }}>
      {/* Header */}
      <View style={{ padding: 16, paddingTop: 40 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <Crown width={32} height={32} color="#3f87ff" />
          <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#ffffff', marginLeft: 12 }}>
            My State
          </Text>
        </View>
        
        <Text style={{ fontSize: 16, color: '#9CA3AF', marginBottom: 16 }}>
          Your political state membership and information
        </Text>
      </View>

      {/* Content */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ padding: 16 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {renderStateInfo()}
      </ScrollView>
    </View>
  );
};