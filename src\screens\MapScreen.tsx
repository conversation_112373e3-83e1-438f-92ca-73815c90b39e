import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import { Map, Globe, Info, Filter, Layers, Flame, Target } from 'lucide-react-native';
import { GeographicalMap } from '../components/GeographicalMap';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { warService } from '../services/warService';
import { stateService } from '../services/stateService';
import Toast from 'react-native-toast-message';
import { showErrorToast, showSuccessToast } from '../utils/toastUtils';

interface Props {
  navigation: any;
}

interface MapState {
  id: string;
  name: string;
  regions: any[];
  leader?: {
    username: string;
  };
}

interface Region {
  id: string;
  name: string;
  population?: number;
  countryCode?: string;
  state?: {
    id: string;
    name: string;
  };
}

export const MapScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuthStore();
  useAuthGuard({ navigation });

  const [mapMode, setMapMode] = useState<'political' | 'wars' | 'targets'>('political');
  const [showInfo, setShowInfo] = useState(false);
  const [wars, setWars] = useState<any[]>([]);
  const [availableTargets, setAvailableTargets] = useState<any[]>([]);
  const [regionChecks, setRegionChecks] = useState<string | null>(null);
  const [states, setStates] = useState<any[]>([]);

  useEffect(() => {
    // Set the status bar for this screen
    StatusBar.setBarStyle('light-content', true);
    
    // Load wars data and states
    loadWarsData();
    loadStatesData();
  }, []);

  const loadStatesData = async () => {
    try {
      const statesData = await stateService.getAllStates();
      setStates(statesData || []);
    } catch (error) {
      console.error('Error loading states data:', error);
      showErrorToast('Failed to load states data');
    }
  };

  useEffect(() => {
    // Load targets when region is checked
    if (regionChecks && mapMode === 'targets') {
      loadAvailableTargets();
    }
  }, [regionChecks, mapMode]);

  const loadWarsData = async () => {
    try {
      const activeWars = await warService.getActiveWars();
      setWars(activeWars || []);
    } catch (error) {
      console.error('Error loading wars data:', error);
    }
  };

  const loadAvailableTargets = async () => {
    if (!regionChecks) return;
    
    try {
      const groundTargets = await warService.getAvailableTargets('ground', regionChecks);
      const seaTargets = await warService.getAvailableTargets('sea', regionChecks);
      setAvailableTargets([...groundTargets, ...seaTargets]);
    } catch (error) {
      console.error('Error loading targets:', error);
    }
  };

  const handleRegionCheck = (regionId: string) => {
    setRegionChecks(regionId);
  };

  const handleStateSelect = (state: MapState) => {
    Alert.alert(
      state.name,
      `Leader: ${state.leader?.username || 'None'}\nRegions: ${state.regions.length}`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'View Details',
          onPress: () => {
            navigation.navigate('StateBudget', { stateId: state.id });
          },
        },
      ]
    );
  };

  const handleRegionSelect = (region: Region) => {
    if (region.state) {
      navigation.navigate('RegionDetail', { 
        regionId: region.id,
        stateId: region.state.id,
      });
    } else {
      // Show info about unclaimed territory
      // Using Toast directly for info messages as they're informational
      Toast.show({
        type: 'info',
        text1: 'Unclaimed Territory',
        text2: `${region.name} is available for colonization`,
      });
    }
  };

  const toggleMapMode = () => {
    const modes = ['political', 'wars', 'targets'] as const;
    const currentIndex = modes.indexOf(mapMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setMapMode(modes[nextIndex]);
    
    // Reset targets when switching modes
    if (modes[nextIndex] !== 'targets') {
      setRegionChecks(null);
      setAvailableTargets([]);
    }
    
    // Show info about map mode change
    Toast.show({
      type: 'info',
      text1: 'Map Mode Changed',
      text2: `Switched to ${modes[nextIndex]} view`,
    });
  };

  const getMapModeInfo = () => {
    switch (mapMode) {
      case 'political':
        return {
          title: 'Political Map',
          description: 'Shows state territories and political control',
          icon: Globe,
        };
      case 'wars':
        return {
          title: 'Active Wars',
          description: 'Shows regions currently at war',
          icon: Flame,
        };
      case 'targets':
        return {
          title: 'War Targets',
          description: 'Click regions to check available targets',
          icon: Target,
        };
    }
  };

  const mapModeInfo = getMapModeInfo();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#111827' }}>
      <StatusBar backgroundColor="#111827" barStyle="light-content" />
      
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#374151',
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Map width={24} height={24} color="#3f87ff" />
          <Text style={{
            color: '#ffffff',
            fontSize: 20,
            fontWeight: 'bold',
            marginLeft: 8,
          }}>
            World Map
          </Text>
        </View>
        
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            onPress={() => setShowInfo(!showInfo)}
            style={{
              padding: 8,
              borderRadius: 8,
              backgroundColor: showInfo ? '#3f87ff' : 'transparent',
              marginRight: 8,
            }}
          >
            <Info width={20} height={20} color={showInfo ? '#ffffff' : '#9CA3AF'} />
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={toggleMapMode}
            style={{
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 8,
              backgroundColor: '#1F2937',
              borderWidth: 1,
              borderColor: '#3f87ff',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <mapModeInfo.icon width={16} height={16} color="#3f87ff" />
            <Text style={{
              color: '#3f87ff',
              fontSize: 12,
              fontWeight: '500',
              marginLeft: 4,
            }}>
              {mapModeInfo.title.split(' ')[0]}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Info Panel */}
      {showInfo && (
        <View style={{
          backgroundColor: '#1F2937',
          padding: 16,
          borderBottomWidth: 1,
          borderBottomColor: '#374151',
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <mapModeInfo.icon width={20} height={20} color="#3f87ff" />
            <Text style={{
              color: '#ffffff',
              fontSize: 16,
              fontWeight: '600',
              marginLeft: 8,
            }}>
              {mapModeInfo.title}
            </Text>
          </View>
          <Text style={{
            color: '#D1D5DB',
            fontSize: 14,
            lineHeight: 20,
            marginBottom: 12,
          }}>
            {mapModeInfo.description}
          </Text>
          
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 16 }}>
              <View style={{
                width: 12,
                height: 12,
                borderRadius: 6,
                backgroundColor: '#3f87ff',
                marginRight: 6,
              }} />
              <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Claimed</Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{
                width: 12,
                height: 12,
                borderRadius: 6,
                backgroundColor: '#2d3748',
                opacity: 0.5,
                marginRight: 6,
              }} />
              <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Unclaimed</Text>
            </View>
          </View>
        </View>
      )}

      {/* Geographical Map */}
      <View style={{ flex: 1 }}>
        <GeographicalMap
          onStateSelect={handleStateSelect}
          onRegionSelect={handleRegionSelect}
          onRegionCheck={handleRegionCheck}
          mapMode={mapMode}
          wars={wars}
          availableTargets={availableTargets}
          states={states}
          showControls={true}
          interactive={true}
          height={undefined} // Let it fill the container
        />
      </View>

      {/* Quick Actions */}
      <View style={{
        flexDirection: 'row',
        padding: 16,
        borderTopWidth: 1,
        borderTopColor: '#374151',
        backgroundColor: '#1F2937',
      }}>
        <TouchableOpacity
          onPress={() => navigation.navigate('MyState')}
          style={{
            flex: 1,
            backgroundColor: '#374151',
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 8,
            alignItems: 'center',
            marginRight: 8,
          }}
        >
          <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '500' }}>
            My Territory
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={() => navigation.navigate('Wars')}
          style={{
            flex: 1,
            backgroundColor: '#3f87ff',
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 8,
            alignItems: 'center',
            marginLeft: 8,
          }}
        >
          <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '600' }}>
            Active Wars
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};