import { Platform } from 'react-native';
import { analyticsService } from './analyticsService';
import { pushNotificationService } from './pushNotificationService';
import { offlineService } from './offlineService';
import { chatService } from './chatService';
import Constants from 'expo-constants';

class FirebaseInitializer {
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('🔥 Initializing Firebase services...');

      // Initialize analytics and crash reporting
      if (Constants.expoConfig?.extra?.enableAnalytics === 'true') {
        await analyticsService.initialize();
        console.log('✅ Firebase Analytics initialized');
      }

      // Initialize push notifications
      await pushNotificationService.initialize();
      console.log('✅ Push Notifications initialized');

      // Initialize offline service
      await offlineService.initialize();
      console.log('✅ Offline Service initialized');

      // Initialize chat WebSocket connection
      await chatService.connectWebSocket();
      console.log('✅ Chat WebSocket initialized');

      this.isInitialized = true;
      
      // Log successful initialization
      await analyticsService.logEvent('app_initialized', {
        platform: Platform.OS,
        build_type: Constants.expoConfig?.extra?.buildType || 'unknown',
        firebase_project: Constants.expoConfig?.extra?.firebaseProjectId || 'unknown',
      });

      console.log('🚀 All services initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Firebase services:', error);
      
      // Log initialization failure
      await analyticsService.recordError(error as Error, {
        context: 'firebase_initialization',
        platform: Platform.OS,
      });
    }
  }

  async cleanup(): Promise<void> {
    try {
      console.log('🧹 Cleaning up Firebase services...');
      
      chatService.disconnectWebSocket();
      await pushNotificationService.unregisterToken();
      await analyticsService.resetUserData();
      
      this.isInitialized = false;
      console.log('✅ Firebase services cleaned up');
    } catch (error) {
      console.error('❌ Error cleaning up Firebase services:', error);
    }
  }

  isServiceInitialized(): boolean {
    return this.isInitialized;
  }
}

export const firebaseInitializer = new FirebaseInitializer();