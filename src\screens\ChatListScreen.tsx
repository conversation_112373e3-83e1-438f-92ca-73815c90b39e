import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Chat, Message } from '../types/chat';
import { theme } from '../styles/theme';
import { useAuthStore } from '../store/useAuthStore';
import { chatService } from '../services/chatService';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

export const ChatListScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuthStore();
  const [chats, setChats] = useState<Chat[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadChats();
  }, []);

  const loadChats = async () => {
    try {
      const allChats = await chatService.getAllChats();
      if (Array.isArray(allChats)) {
        setChats(allChats.map(chat => ({
          ...chat,
          lastMessage: chat.lastMessage ? {
            ...chat.lastMessage,
            createdAt: chat.lastMessage.createdAt.toString()
          } : undefined
        })));
      } else {
        console.warn('getAllChats returned non-array:', allChats);
        setChats([]);
      }
    } catch (error) {
      console.error('Error loading chats:', error);
      setChats([]);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load chats',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadChats();
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return 'now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
    return `${Math.floor(diff / 86400000)}d`;
  };

  const getChatName = (chat: Chat) => {
    if (chat.type === 'group' && chat.name) {
      return chat.name;
    }
    
    // For direct chats, show the other participant's name
    const otherParticipant = chat.participants.find(p => p.id !== user?.id);
    return otherParticipant ? otherParticipant.username : 'Unknown';
  };

  const getChatIcon = (chat: Chat) => {
    return chat.type === 'group' ? '👥' : '💬';
  };

  const renderChatItem = (chat: Chat) => (
    <TouchableOpacity
      key={chat.id}
      style={styles.chatItem}
      onPress={() => navigation.navigate('ChatDetail', { chatId: chat.id })}
    >
      <View style={styles.chatHeader}>
        <View style={styles.chatTitleRow}>
          <Text style={styles.chatIcon}>{getChatIcon(chat)}</Text>
          <View style={styles.chatInfo}>
            <Text style={styles.chatName}>{getChatName(chat)}</Text>
            <Text style={styles.participantCount}>
              {chat.participants.length} participant{chat.participants.length !== 1 ? 's' : ''}
            </Text>
          </View>
          <View style={styles.chatMeta}>
            {chat.lastMessage && (
              <Text style={styles.timeText}>
                {formatTime(chat.lastMessage.createdAt)}
              </Text>
            )}
            {chat.unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadText}>{chat.unreadCount}</Text>
              </View>
            )}
          </View>
        </View>
      </View>

      {chat.lastMessage && (
        <View style={styles.lastMessageContainer}>
          <Text style={styles.senderName}>
            {chat.lastMessage.sender.username}:
          </Text>
          <Text style={[
            styles.lastMessage,
            !chat.lastMessage.isRead && styles.lastMessageUnread
          ]} numberOfLines={2}>
            {chat.lastMessage.content}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const getTotalUnreadCount = () => {
    return chats.reduce((total, chat) => total + chat.unreadCount, 0);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.centerContent}>
          <Text style={styles.loadingText}>Loading chats...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Messages</Text>
        <View style={styles.headerActions}>
          {getTotalUnreadCount() > 0 && (
            <View style={styles.totalUnreadBadge}>
              <Text style={styles.totalUnreadText}>{getTotalUnreadCount()}</Text>
            </View>
          )}
          <TouchableOpacity
            style={styles.createButton}
            onPress={() => navigation.navigate('CreateChat')}
          >
            <Text style={styles.createButtonText}>➕</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {chats.length > 0 ? (
          chats.map(renderChatItem)
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No chats yet</Text>
            <Text style={styles.emptySubtext}>Start a conversation!</Text>
            <TouchableOpacity
              style={styles.emptyActionButton}
              onPress={() => navigation.navigate('CreateChat')}
            >
              <Text style={styles.emptyActionText}>Create Chat</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: theme.fontSize.header,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalUnreadBadge: {
    backgroundColor: theme.colors.error,
    borderRadius: 12,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    marginRight: theme.spacing.md,
    minWidth: 24,
    alignItems: 'center',
  },
  totalUnreadText: {
    color: theme.colors.text,
    fontSize: theme.fontSize.small,
    fontWeight: theme.fontWeight.bold,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonText: {
    fontSize: theme.fontSize.large,
  },
  scrollView: {
    flex: 1,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: theme.colors.textSecondary,
    fontSize: theme.fontSize.large,
  },
  chatItem: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
    margin: theme.spacing.md,
    padding: theme.spacing.lg,
  },
  chatHeader: {
    marginBottom: theme.spacing.sm,
  },
  chatTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chatIcon: {
    fontSize: theme.fontSize.title,
    marginRight: theme.spacing.md,
  },
  chatInfo: {
    flex: 1,
  },
  chatName: {
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  participantCount: {
    fontSize: theme.fontSize.small,
    color: theme.colors.textMuted,
  },
  chatMeta: {
    alignItems: 'flex-end',
  },
  timeText: {
    fontSize: theme.fontSize.small,
    color: theme.colors.textMuted,
    marginBottom: theme.spacing.xs,
  },
  unreadBadge: {
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    minWidth: 20,
    alignItems: 'center',
  },
  unreadText: {
    color: theme.colors.text,
    fontSize: theme.fontSize.small,
    fontWeight: theme.fontWeight.bold,
  },
  lastMessageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  senderName: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.primary,
    fontWeight: theme.fontWeight.medium,
    marginRight: theme.spacing.sm,
  },
  lastMessage: {
    flex: 1,
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
    lineHeight: 18,
  },
  lastMessageUnread: {
    color: theme.colors.text,
    fontWeight: theme.fontWeight.medium,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xxl * 2,
  },
  emptyText: {
    color: theme.colors.textSecondary,
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.medium,
    marginBottom: theme.spacing.sm,
  },
  emptySubtext: {
    color: theme.colors.textMuted,
    fontSize: theme.fontSize.medium,
    marginBottom: theme.spacing.xl,
  },
  emptyActionButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.medium,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
  },
  emptyActionText: {
    color: theme.colors.text,
    fontSize: theme.fontSize.medium,
    fontWeight: theme.fontWeight.bold,
  },
});