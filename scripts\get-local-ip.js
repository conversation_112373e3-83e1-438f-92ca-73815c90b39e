const os = require('os');
const http = require('http');

function getLocalIP() {
  const interfaces = os.networkInterfaces();
  const candidates = [];

  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        candidates.push({
          name,
          address: interface.address,
          priority: getPriority(interface.address, name)
        });
      }
    }
  }

  // Sort by priority (higher is better)
  candidates.sort((a, b) => b.priority - a.priority);

  return candidates.length > 0 ? candidates[0].address : 'localhost';
}

function getPriority(address, interfaceName) {
  // Prefer 192.168.x.x addresses (common home networks)
  if (address.startsWith('192.168.')) return 100;

  // Then 10.x.x.x addresses (common corporate networks)
  if (address.startsWith('10.')) return 90;

  // Then 172.16-31.x.x addresses (less common private networks)
  if (address.startsWith('172.')) {
    const secondOctet = parseInt(address.split('.')[1]);
    if (secondOctet >= 16 && secondOctet <= 31) return 80;
  }

  // Prefer WiFi interfaces
  if (interfaceName.toLowerCase().includes('wi-fi') ||
      interfaceName.toLowerCase().includes('wifi') ||
      interfaceName.toLowerCase().includes('wlan')) {
    return 70;
  }

  // Default priority for other private addresses
  return 50;
}

function testBackendConnection(ip, port = 3000) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: ip,
      port: port,
      path: '/',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      resolve({ success: true, status: res.statusCode });
    });

    req.on('error', (err) => {
      resolve({ success: false, error: err.message });
    });

    req.on('timeout', () => {
      resolve({ success: false, error: 'Connection timeout' });
    });

    req.end();
  });
}

async function main() {
  const localIP = getLocalIP();
  console.log(`🌐 Local IP: ${localIP}`);
  console.log(`📱 For mobile development, update your .env file:`);
  console.log(`   API_BASE_URL=http://${localIP}:3000`);
  console.log('');

  // Test localhost connection
  console.log('🧪 Testing backend connections...');
  const localhostTest = await testBackendConnection('localhost');
  console.log(`   localhost:3000 - ${localhostTest.success ? '✅ WORKING' : '❌ FAILED'}`);
  if (!localhostTest.success) {
    console.log(`      Error: ${localhostTest.error}`);
  }

  // Test IP connection
  const ipTest = await testBackendConnection(localIP);
  console.log(`   ${localIP}:3000 - ${ipTest.success ? '✅ WORKING' : '❌ FAILED'}`);
  if (!ipTest.success) {
    console.log(`      Error: ${ipTest.error}`);
  }

  console.log('');
  if (!localhostTest.success && !ipTest.success) {
    console.log('❌ Backend server is not running or not accessible');
    console.log('   Make sure to start your backend server first!');
  } else if (localhostTest.success && !ipTest.success) {
    console.log('⚠️  Backend works on localhost but not on IP address');
    console.log('   This might cause issues with mobile development');
  } else {
    console.log('✅ Backend server is accessible!');
  }
}

main().catch(console.error);
