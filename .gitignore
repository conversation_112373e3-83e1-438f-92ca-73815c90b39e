# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Expo
.expo/
dist/
web-build/
.expo-shared/

# Environment variables (IMPORTANT - Contains sensitive data)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# React Native
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
*.keystore
!debug.keystore

# Metro
.metro-health-check*

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace
ios/*.xcuserdata
ios/Podfile.lock

# Android
*.apk
*.aab
android/app/build/
android/build/
android/.gradle/
android/captures/
android/gradlew
android/gradlew.bat
android/local.properties

# Firebase (IMPORTANT - Contains sensitive keys)
google-services.json
GoogleService-Info.plist

# macOS
.DS_Store
*.pem

# Windows
Thumbs.db
Desktop.ini

# IDE
.vscode/
.idea/
*.swp
*.swo

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Testing
coverage/
*.lcov

# Logs
logs
*.log

# Build outputs
build/
dist/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Database files
*.db
*.sqlite
*.sqlite3

# Certificates and keys (IMPORTANT - Security sensitive)
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# Package manager files (uncomment the one you don't use)
# yarn.lock
# package-lock.json

# EAS Build
.easignore

# Sentry
.sentryclirc

# Local configuration
config.local.js
config.local.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock
