import axios from 'axios';
import { getApiUrl, isWeb } from '../utils/platformUtils';
import { Platform } from 'react-native';
import { secureStorage } from '../utils/secureStorage';

const API_URL = getApiUrl();

console.log('🌐 Platform:', Platform.OS);
console.log('🔗 API URL:', API_URL);
console.log('📱 Is Web:', isWeb);

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 second timeout
});

// Add a request interceptor to add the auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await secureStorage.getItem('access_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting token from AsyncStorage:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.log('🚨 API Error:', error);
    console.log('🚨 Error config:', error.config);
    console.log('🚨 Error response:', error.response);

    // Handle network errors
    if (!error.response) {
      console.log('🚨 Network error detected - no response received');
      console.log('🚨 Error code:', error.code);
      console.log('🚨 Error message:', error.message);

      let errorMessage = 'Network error - please check your connection';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = `Cannot connect to server at ${API_URL}. Make sure your backend is running.`;
      } else if (error.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error. Check your internet connection and server URL.';
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = 'Server not found. Check your API URL configuration.';
      }

      return Promise.reject({ message: errorMessage });
    }

    const isLoginRequest = error.config?.url?.includes('/auth/login');

    switch (error.response.status) {
      case 401:
        // Only clear token if it's not already a login request
        if (!isLoginRequest) {
          try {
            await secureStorage.deleteItem('access_token');
            // In React Native, navigation should be handled at component level
            // You might want to emit an event or use a navigation service
          } catch (storageError) {
            console.error('Error removing token from AsyncStorage:', storageError);
          }
        }
        break;
      case 403:
        // Handle forbidden access
        break;
      case 404:
        return Promise.reject({ message: 'Resource not found' });
      case 429:
        return Promise.reject({ message: 'Too many requests - please try again later' });
      case 500:
      case 502:
      case 503:
      case 504:
        return Promise.reject({ message: 'Server error - please try again later' });
    }
    return Promise.reject(error);
  }
);

// Auth-related API methods
export const authAPI = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },
  register: async (username: string, email: string, password: string) => {
    const response = await api.post('/auth/register', { username, email, password });
    return response.data;
  },
  resendVerification: async (email: string) => {
    const response = await api.post('/auth/resend-verification', { email });
    return response.data;
  },
  forgotPassword: async (email: string) => {
    const response = await api.post('/auth/forgot-password', { email });
    return response.data;
  },
  resetPassword: async (token: string, password: string) => {
    const response = await api.post('/auth/reset-password', { token, password });
    return response.data;
  },
  verifyAccount: async (token: string) => {
    const response = await api.post('/auth/verify', { token });
    return response.data;
  }
};

export default api;