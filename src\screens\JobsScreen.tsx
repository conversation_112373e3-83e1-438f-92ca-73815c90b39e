import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import { Factory, Plus, X, Users, TrendingUp, DollarSign, UserX } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { factoryService } from '../services/factoryService';
import { FactoryType } from '../types/factory';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Toast from 'react-native-toast-message';
import { showErrorToast, showSuccessToast } from '../utils/toastUtils';

interface Props {
  navigation: any;
}

interface FactoryData {
  id: number;
  name: string;
  type: string;
  level: number;
  experience: number;
  resourceBonus: number;
  wage: number;
  maxWorkers: number;
  workers?: any[];
  ownerId: number | null;
  region?: {
    id: string;
    name: string;
  };
  isPrivate?: boolean;
}

export const JobsScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuthStore();
  useAuthGuard({ navigation });

  const [factories, setFactories] = useState<FactoryData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [workingFactories, setWorkingFactories] = useState(new Set<number>());
  const [selectedFactory, setSelectedFactory] = useState<FactoryData | null>(null);
  const [showFactoryDetails, setShowFactoryDetails] = useState(false);
  const [factoryWorkers, setFactoryWorkers] = useState<any[]>([]);
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [createForm, setCreateForm] = useState({
    name: '',
    type: FactoryType.GOLD,
    regionId: '',
    wage: 50,
    resourcePerWork: 100,
    energyCost: 10,
    maxWorkers: 10
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      if (!user?.region?.id) {
        console.warn('No region ID found for user');
        setFactories([]);
        return;
      }

      const factoriesData = await factoryService.getFactoriesInUserRegion();
      setFactories(factoriesData);
    } catch (error) {
      console.error('Error fetching factory data:', error);
      showErrorToast('Failed to fetch factory data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const loadFactoryDetails = async (factoryId: number) => {
    try {
      const workers = await factoryService.getFactoryWorkers(factoryId);
      setFactoryWorkers(workers);
    } catch (err) {
      showErrorToast('Failed to load factory details');
    }
  };

  const handleWork = async (factoryId: number) => {
    if (workingFactories.has(factoryId)) return;

    try {
      setWorkingFactories(prev => new Set(prev).add(factoryId));
      
      const currentEnergy = user?.energy || 0;
      const response = await factoryService.workAtFactory(factoryId, currentEnergy);
      
      showSuccessToast('Work session completed successfully');
      
      await fetchData();
    } catch (error: any) {
      showErrorToast(error || 'Failed to complete work session');
    } finally {
      setWorkingFactories(prev => {
        const newSet = new Set(prev);
        newSet.delete(factoryId);
        return newSet;
      });
    }
  };

  const handleCreateFactory = async () => {
    try {
      await factoryService.createFactory(createForm);
      showSuccessToast('Factory created successfully!');
      setCreateModalOpen(false);
      setCreateForm({
        name: '',
        type: FactoryType.GOLD,
        regionId: '',
        wage: 50,
        resourcePerWork: 100,
        energyCost: 10,
        maxWorkers: 10
      });
      await fetchData();
    } catch (error: any) {
      showErrorToast(error || 'Failed to create factory');
    }
  };

  const openFactoryDetails = async (factory: FactoryData) => {
    setSelectedFactory(factory);
    await loadFactoryDetails(factory.id);
    setShowFactoryDetails(true);
  };

  const isFactoryOwner = (factory: FactoryData) => {
    return user && factory.ownerId === user.id;
  };

  const getWageDisplay = (factory: FactoryData) => {
    return `${factory.wage}%`;
  };

  const openCreateModal = () => {
    if (user?.region?.id) {
      setCreateForm({
        name: '',
        type: FactoryType.GOLD,
        regionId: user.region.id.toString(),
        wage: 50,
        resourcePerWork: 100,
        energyCost: 10,
        maxWorkers: 10
      });
    }
    setCreateModalOpen(true);
  };

  if (loading) {
    return (
      <View style={{ flex: 1, backgroundColor: '#111827', justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#3f87ff" />
        <Text style={{ color: '#3f87ff', fontSize: 18, marginTop: 16 }}>Loading jobs...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#111827' }}>
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Header */}
        <View style={{ padding: 16, paddingTop: 40 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
            <View>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                <Factory width={32} height={32} color="#3f87ff" />
                <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#ffffff', marginLeft: 12 }}>
                  Jobs & Factories
                </Text>
              </View>
              <Text style={{ fontSize: 16, color: '#9CA3AF' }}>
                Work at factories or create your own
              </Text>
            </View>
            <TouchableOpacity
              onPress={openCreateModal}
              style={{
                backgroundColor: '#3f87ff',
                paddingHorizontal: 16,
                paddingVertical: 10,
                borderRadius: 8,
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Plus width={20} height={20} color="#ffffff" />
              <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '600', marginLeft: 4 }}>
                Create
              </Text>
            </TouchableOpacity>
          </View>

          {/* Energy Status */}
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 20,
            marginBottom: 20,
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
              <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff' }}>Your Energy</Text>
              <Text style={{ fontSize: 18, color: '#ffffff' }}>
                {user?.energy || 0}/{user?.isPremium ? 200 : 100}
              </Text>
            </View>
            <View style={{
              width: '100%',
              height: 8,
              backgroundColor: '#374151',
              borderRadius: 4,
            }}>
              <View style={{
                width: `${((user?.energy || 0) / (user?.isPremium ? 200 : 100)) * 100}%`,
                height: 8,
                backgroundColor: '#3f87ff',
                borderRadius: 4,
              }} />
            </View>
          </View>

          {/* Available Factories */}
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 20,
          }}>
            <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', marginBottom: 16 }}>
              Available Factories
            </Text>
            {factories.map((factory) => (
              <View
                key={factory.id}
                style={{
                  backgroundColor: factory.isPrivate ? '#4C1D95' : '#374151',
                  borderRadius: 8,
                  padding: 16,
                  marginBottom: 12,
                  borderWidth: factory.isPrivate ? 2 : 0,
                  borderColor: factory.isPrivate ? '#8B5CF6' : 'transparent',
                }}
              >
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 12 }}>
                  <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', flex: 1 }}>
                    {factory.name}
                  </Text>
                  <View style={{ alignItems: 'flex-end' }}>
                    <View style={{
                      backgroundColor: '#F59E0B',
                      paddingHorizontal: 8,
                      paddingVertical: 4,
                      borderRadius: 4,
                      marginBottom: 4,
                    }}>
                      <Text style={{ color: '#000000', fontSize: 12, fontWeight: '600' }}>GOLD</Text>
                    </View>
                    {factory.isPrivate && (
                      <View style={{
                        backgroundColor: '#8B5CF6',
                        paddingHorizontal: 8,
                        paddingVertical: 2,
                        borderRadius: 4,
                      }}>
                        <Text style={{ color: '#ffffff', fontSize: 10 }}>Private</Text>
                      </View>
                    )}
                  </View>
                </View>

                <View style={{ marginBottom: 16 }}>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}>
                    <Text style={{ color: '#D1D5DB' }}>Level:</Text>
                    <Text style={{ color: '#ffffff' }}>{factory.level}</Text>
                  </View>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}>
                    <Text style={{ color: '#D1D5DB' }}>Experience:</Text>
                    <Text style={{ color: '#ffffff' }}>{factory.experience}</Text>
                  </View>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}>
                    <Text style={{ color: '#D1D5DB' }}>Resource Bonus:</Text>
                    <Text style={{ color: '#ffffff' }}>{factory.resourceBonus}%</Text>
                  </View>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}>
                    <Text style={{ color: '#D1D5DB' }}>Default Wage:</Text>
                    <Text style={{ color: '#ffffff' }}>{getWageDisplay(factory)}</Text>
                  </View>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ color: '#D1D5DB' }}>Workers:</Text>
                    <Text style={{ color: '#ffffff' }}>{factory.workers?.length || 0}/{factory.maxWorkers}</Text>
                  </View>
                </View>

                <View style={{ gap: 8 }}>
                  <TouchableOpacity
                    onPress={() => handleWork(factory.id)}
                    disabled={!user || workingFactories.has(factory.id)}
                    style={{
                      backgroundColor: !user || workingFactories.has(factory.id) ? '#6B7280' : '#3f87ff',
                      paddingVertical: 12,
                      borderRadius: 8,
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
                      {workingFactories.has(factory.id) ? 'Working...' : 'Work Here'}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => openFactoryDetails(factory)}
                    style={{
                      backgroundColor: '#4B5563',
                      paddingVertical: 10,
                      borderRadius: 8,
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{ color: '#ffffff', fontSize: 14 }}>View Details</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Create Factory Modal */}
      <Modal visible={createModalOpen} transparent animationType="slide">
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 16,
        }}>
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 20,
            width: '100%',
            maxWidth: 400,
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
              <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff' }}>Create New Factory</Text>
              <TouchableOpacity onPress={() => setCreateModalOpen(false)}>
                <X width={24} height={24} color="#9CA3AF" />
              </TouchableOpacity>
            </View>

            <View style={{ marginBottom: 16 }}>
              <Text style={{ fontSize: 16, color: '#D1D5DB', marginBottom: 8 }}>Factory Name</Text>
              <TextInput
                value={createForm.name}
                onChangeText={(text) => setCreateForm({...createForm, name: text})}
                style={{
                  backgroundColor: '#374151',
                  borderWidth: 1,
                  borderColor: '#4B5563',
                  borderRadius: 8,
                  padding: 12,
                  color: '#ffffff',
                  fontSize: 16,
                }}
                placeholder="Enter factory name"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={{ marginBottom: 16 }}>
              <Text style={{ fontSize: 16, color: '#D1D5DB', marginBottom: 8 }}>Worker Wage (%)</Text>
              <TextInput
                value={createForm.wage.toString()}
                onChangeText={(text) => setCreateForm({...createForm, wage: parseFloat(text) || 0})}
                style={{
                  backgroundColor: '#374151',
                  borderWidth: 1,
                  borderColor: '#4B5563',
                  borderRadius: 8,
                  padding: 12,
                  color: '#ffffff',
                  fontSize: 16,
                }}
                placeholder="50"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
              />
            </View>

            <View style={{
              backgroundColor: '#F59E0B',
              padding: 12,
              borderRadius: 8,
              marginBottom: 20,
            }}>
              <Text style={{ color: '#000000', fontSize: 14, fontWeight: '600' }}>
                Creation Cost: 200 Gold
              </Text>
            </View>

            <View style={{ flexDirection: 'row', gap: 12 }}>
              <TouchableOpacity
                onPress={() => setCreateModalOpen(false)}
                style={{
                  flex: 1,
                  backgroundColor: '#4B5563',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                }}
              >
                <Text style={{ color: '#ffffff', fontSize: 16 }}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleCreateFactory}
                style={{
                  flex: 1,
                  backgroundColor: '#3f87ff',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                }}
              >
                <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>Create</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Factory Details Modal */}
      <Modal visible={showFactoryDetails} transparent animationType="slide">
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 16,
        }}>
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 20,
            width: '100%',
            maxWidth: 400,
            maxHeight: '80%',
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
              <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', flex: 1 }}>
                {selectedFactory?.name}
              </Text>
              <TouchableOpacity onPress={() => setShowFactoryDetails(false)}>
                <X width={24} height={24} color="#9CA3AF" />
              </TouchableOpacity>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
              {selectedFactory && (
                <>
                  <View style={{ marginBottom: 20 }}>
                    <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginBottom: 12 }}>
                      Factory Info
                    </Text>
                    <View style={{ backgroundColor: '#374151', borderRadius: 8, padding: 16 }}>
                      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                        <Text style={{ color: '#9CA3AF' }}>Type:</Text>
                        <Text style={{ color: '#ffffff' }}>{selectedFactory.type}</Text>
                      </View>
                      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                        <Text style={{ color: '#9CA3AF' }}>Level:</Text>
                        <Text style={{ color: '#ffffff' }}>{selectedFactory.level}</Text>
                      </View>
                      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                        <Text style={{ color: '#9CA3AF' }}>Experience:</Text>
                        <Text style={{ color: '#ffffff' }}>{selectedFactory.experience.toLocaleString()}</Text>
                      </View>
                      <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                        <Text style={{ color: '#9CA3AF' }}>Resource Bonus:</Text>
                        <Text style={{ color: '#10B981' }}>+{selectedFactory.resourceBonus}%</Text>
                      </View>
                    </View>
                  </View>

                  <View style={{ marginBottom: 20 }}>
                    <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginBottom: 12 }}>
                      Workers ({factoryWorkers.length})
                    </Text>
                    <View style={{ backgroundColor: '#374151', borderRadius: 8, padding: 16 }}>
                      {factoryWorkers.length > 0 ? (
                        factoryWorkers.map((worker, index) => (
                          <View key={worker.id} style={{
                            paddingVertical: 8,
                            borderBottomWidth: index < factoryWorkers.length - 1 ? 1 : 0,
                            borderBottomColor: '#4B5563',
                          }}>
                            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '500' }}>
                              {worker.worker?.username || `User ${worker.workerId}`}
                            </Text>
                            <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
                              Wage: {worker.wage}% • {worker.isActive ? 'Active' : 'Inactive'}
                            </Text>
                          </View>
                        ))
                      ) : (
                        <Text style={{ color: '#9CA3AF', textAlign: 'center', paddingVertical: 20 }}>
                          No workers yet
                        </Text>
                      )}
                    </View>
                  </View>
                </>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
};