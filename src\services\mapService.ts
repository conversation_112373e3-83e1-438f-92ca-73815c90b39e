import { Platform } from 'react-native';
import { api } from './api';

export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

export interface CountryPolygon {
  id: string;
  name: string;
  code: string;
  coordinates: number[][][]; // GeoJSON Polygon coordinates
  properties: {
    name: string;
    iso_a2: string;
    iso_a3: string;
    population?: number;
    stateId?: string;
    stateName?: string;
    stateFlag?: string;
    isOwned: boolean;
    ownerColor?: string;
  };
}

export interface WorldMapData {
  type: 'FeatureCollection';
  features: {
    type: 'Feature';
    properties: {
      name: string;
      iso_a2: string;
      iso_a3: string;
      [key: string]: any;
    };
    geometry: {
      type: 'Polygon' | 'MultiPolygon';
      coordinates: number[][][] | number[][][][];
    };
  }[];
}

export interface MapState {
  id: string;
  name: string;
  color: string;
  regions: string[]; // ISO country codes
  capital?: string;
  population: number;
  leader?: {
    id: number;
    username: string;
  };
}

class MapService {
  private worldMapData: WorldMapData | null = null;
  private mapStates: MapState[] = [];
  private countryPolygons: CountryPolygon[] = [];
  
  private readonly defaultRegion: MapRegion = {
    latitude: 20.0,
    longitude: 0.0,
    latitudeDelta: 100.0,
    longitudeDelta: 100.0,
  };

  async loadWorldMap(): Promise<WorldMapData> {
    if (this.worldMapData) {
      return this.worldMapData;
    }

    try {
      // First try to load from local bundle
      const localMapData = await this.loadLocalMapData();
      if (localMapData) {
        this.worldMapData = localMapData;
        return localMapData;
      }

      // Fallback to external source
      const response = await fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson');
      const mapData = await response.json();
      
      this.worldMapData = mapData;
      return mapData;
    } catch (error) {
      console.error('Failed to load world map data:', error);
      throw new Error('Failed to load map data');
    }
  }

  private async loadLocalMapData(): Promise<WorldMapData | null> {
    try {
      // In a real app, you would bundle the GeoJSON file with the app
      // For now, we'll use a minimal world map data structure
      return {
        type: 'FeatureCollection',
        features: [
          // This would be populated with actual GeoJSON data
          // For production, include the world.geojson file in the bundle
        ]
      };
    } catch (error) {
      console.error('Failed to load local map data:', error);
      return null;
    }
  }

  async loadMapStates(): Promise<MapState[]> {
    try {
      const response = await api.get('/states');
      const states = response.data;
      
      // Transform states for map display
      this.mapStates = states.map((state: any, index: number) => ({
        id: state.id,
        name: state.name,
        color: this.generateStateColor(state.id, index),
        regions: state.regions?.map((r: any) => r.countryCode || r.code).filter(Boolean) || [],
        capital: state.capital,
        population: state.population || 0,
        leader: state.leader,
      }));

      return this.mapStates;
    } catch (error) {
      console.error('Failed to load map states:', error);
      return [];
    }
  }

  async processMapData(): Promise<CountryPolygon[]> {
    try {
      const [worldData, states] = await Promise.all([
        this.loadWorldMap(),
        this.loadMapStates()
      ]);

      // Create a map of country code to state for quick lookup
      const countryToState = new Map<string, MapState>();
      states.forEach(state => {
        state.regions.forEach(countryCode => {
          countryToState.set(countryCode.toUpperCase(), state);
        });
      });

      // Process GeoJSON features into polygons
      this.countryPolygons = worldData.features.map(feature => {
        const countryCode = feature.properties.iso_a2 || feature.properties.iso_a3;
        const ownedByState = countryToState.get(countryCode?.toUpperCase());
        
        return {
          id: feature.properties.iso_a3 || feature.properties.iso_a2 || feature.properties.name,
          name: feature.properties.name,
          code: countryCode,
          coordinates: this.extractCoordinates(feature.geometry),
          properties: {
            ...feature.properties,
            stateId: ownedByState?.id,
            stateName: ownedByState?.name,
            stateFlag: ownedByState ? `/flags/${ownedByState.id}.png` : undefined,
            isOwned: !!ownedByState,
            ownerColor: ownedByState?.color || '#CCCCCC',
          },
        };
      });

      return this.countryPolygons;
    } catch (error) {
      console.error('Failed to process map data:', error);
      return [];
    }
  }

  private extractCoordinates(geometry: any): number[][][] {
    if (geometry.type === 'Polygon') {
      return geometry.coordinates;
    } else if (geometry.type === 'MultiPolygon') {
      // For MultiPolygon, return the largest polygon
      let largestPolygon = geometry.coordinates[0];
      let largestArea = 0;

      geometry.coordinates.forEach((polygon: number[][][]) => {
        const area = this.calculatePolygonArea(polygon[0]);
        if (area > largestArea) {
          largestArea = area;
          largestPolygon = polygon;
        }
      });

      return largestPolygon;
    }
    return [];
  }

  private calculatePolygonArea(coordinates: number[][]): number {
    // Simple area calculation for polygon comparison
    let area = 0;
    for (let i = 0; i < coordinates.length - 1; i++) {
      area += (coordinates[i][0] * coordinates[i + 1][1] - coordinates[i + 1][0] * coordinates[i][1]);
    }
    return Math.abs(area) / 2;
  }

  private generateStateColor(stateId: string, index: number): string {
    // Generate consistent colors for states
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57',
      '#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43',
      '#FF3838', '#2ED573', '#3742FA', '#A4B0BE', '#F8B500',
    ];
    
    // Use state ID hash for consistent color assignment
    let hash = 0;
    for (let i = 0; i < stateId.length; i++) {
      const char = stateId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    const colorIndex = Math.abs(hash) % colors.length;
    return colors[colorIndex];
  }

  getDefaultRegion(): MapRegion {
    return { ...this.defaultRegion };
  }

  getRegionForCountry(countryCode: string): MapRegion {
    // Country-specific regions for better map centering
    const countryRegions: Record<string, MapRegion> = {
      'US': {
        latitude: 39.8283,
        longitude: -98.5795,
        latitudeDelta: 20.0,
        longitudeDelta: 20.0,
      },
      'CN': {
        latitude: 35.8617,
        longitude: 104.1954,
        latitudeDelta: 15.0,
        longitudeDelta: 15.0,
      },
      'RU': {
        latitude: 61.5240,
        longitude: 105.3188,
        latitudeDelta: 30.0,
        longitudeDelta: 40.0,
      },
      'BR': {
        latitude: -14.2350,
        longitude: -51.9253,
        latitudeDelta: 20.0,
        longitudeDelta: 20.0,
      },
      // Add more countries as needed
    };

    return countryRegions[countryCode.toUpperCase()] || this.defaultRegion;
  }

  getStateRegion(stateId: string): MapRegion {
    const state = this.mapStates.find(s => s.id === stateId);
    if (!state || state.regions.length === 0) {
      return this.defaultRegion;
    }

    // Get region for the first country in the state
    return this.getRegionForCountry(state.regions[0]);
  }

  async searchCountries(query: string): Promise<CountryPolygon[]> {
    if (!this.countryPolygons.length) {
      await this.processMapData();
    }

    const lowercaseQuery = query.toLowerCase();
    return this.countryPolygons.filter(country =>
      country.name.toLowerCase().includes(lowercaseQuery) ||
      country.code?.toLowerCase().includes(lowercaseQuery) ||
      country.properties.stateName?.toLowerCase().includes(lowercaseQuery)
    );
  }

  async getCountryInfo(countryCode: string): Promise<CountryPolygon | null> {
    if (!this.countryPolygons.length) {
      await this.processMapData();
    }

    return this.countryPolygons.find(country => 
      country.code?.toUpperCase() === countryCode.toUpperCase()
    ) || null;
  }

  async getStateCountries(stateId: string): Promise<CountryPolygon[]> {
    if (!this.countryPolygons.length) {
      await this.processMapData();
    }

    return this.countryPolygons.filter(country => 
      country.properties.stateId === stateId
    );
  }

  // Get available countries for claiming
  async getAvailableCountries(): Promise<CountryPolygon[]> {
    if (!this.countryPolygons.length) {
      await this.processMapData();
    }

    return this.countryPolygons.filter(country => !country.properties.isOwned);
  }

  // Calculate distance between two points
  calculateDistance(
    lat1: number, 
    lon1: number, 
    lat2: number, 
    lon2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  // Get nearby countries for travel/war
  async getNearbyCountries(
    countryCode: string, 
    maxDistance: number = 2000
  ): Promise<CountryPolygon[]> {
    const targetCountry = await this.getCountryInfo(countryCode);
    if (!targetCountry) return [];

    if (!this.countryPolygons.length) {
      await this.processMapData();
    }

    // Get approximate center of target country
    const targetCenter = this.getPolygonCenter(targetCountry.coordinates);

    return this.countryPolygons.filter(country => {
      if (country.code === countryCode) return false;

      const countryCenter = this.getPolygonCenter(country.coordinates);
      const distance = this.calculateDistance(
        targetCenter.latitude,
        targetCenter.longitude,
        countryCenter.latitude,
        countryCenter.longitude
      );

      return distance <= maxDistance;
    });
  }

  private getPolygonCenter(coordinates: number[][][]): { latitude: number; longitude: number } {
    const ring = coordinates[0]; // Use outer ring
    let totalLat = 0;
    let totalLon = 0;
    let pointCount = 0;

    ring.forEach(point => {
      totalLon += point[0];
      totalLat += point[1];
      pointCount++;
    });

    return {
      longitude: totalLon / pointCount,
      latitude: totalLat / pointCount,
    };
  }

  // Clear cached data
  clearCache(): void {
    this.worldMapData = null;
    this.mapStates = [];
    this.countryPolygons = [];
  }

  // Get current map data
  getCurrentMapData(): {
    worldData: WorldMapData | null;
    states: MapState[];
    countries: CountryPolygon[];
  } {
    return {
      worldData: this.worldMapData,
      states: this.mapStates,
      countries: this.countryPolygons,
    };
  }
}

// Export singleton instance
export const mapService = new MapService();