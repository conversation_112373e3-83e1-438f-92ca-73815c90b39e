import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
} from 'react-native';
import { Eye, EyeOff } from 'lucide-react-native';
import { authAPI } from '../services/api';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
  route: any;
}

export const ResetPasswordScreen: React.FC<Props> = ({ navigation, route }) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [token, setToken] = useState('');

  useEffect(() => {
    // Get token from route params or URL
    const resetToken = route?.params?.token || '';
    setToken(resetToken);

    if (!resetToken) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Token',
        text2: 'Invalid or missing reset token.',
      });
    }
  }, [route]);

  const handleResetPassword = async () => {
    if (!token) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Invalid or missing reset token.',
      });
      return;
    }

    if (!password || !confirmPassword) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please fill in all fields',
      });
      return;
    }

    if (password !== confirmPassword) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Passwords do not match.',
      });
      return;
    }

    if (password.length < 6) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Password must be at least 6 characters long',
      });
      return;
    }

    try {
      setLoading(true);
      await authAPI.resetPassword(token, password);

      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Password reset successfully! Please log in.',
      });
      navigation.navigate('Login');
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.response?.data?.message || 'Failed to reset password.',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
        backgroundColor: '#0F0F10',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 16,
      }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
        showsVerticalScrollIndicator={false}
        style={{ width: '100%' }}
      >
        <View style={{
          width: '100%',
          maxWidth: 400,
          alignSelf: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: 16,
          padding: 32,
        }}>
          <View style={{ alignItems: 'center', marginBottom: 24 }}>
            <TouchableOpacity onPress={() => navigation.navigate('Landing')}>
              <Image
                source={require('../../assets/images/wn-logo.png')}
                style={{
                  width: 56,
                  height: 56,
                  marginBottom: 16,
                }}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#ffffff',
              textAlign: 'center',
            }}>
              Reset Password
            </Text>
          </View>

          <View style={{
            backgroundColor: '#1C1C1E',
            padding: 24,
            borderRadius: 12,
          }}>
            <View style={{ position: 'relative', marginBottom: 16 }}>
              <TextInput
                placeholder="New Password"
                placeholderTextColor="#777"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                style={{
                  width: '100%',
                  paddingHorizontal: 16,
                  paddingVertical: 12,
                  paddingRight: 48,
                  borderRadius: 12,
                  backgroundColor: '#1C1C1E',
                  color: '#ffffff',
                  borderWidth: 1,
                  borderColor: 'rgba(255, 255, 255, 0.1)',
                  fontSize: 16,
                }}
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                style={{
                  position: 'absolute',
                  right: 16,
                  top: 14,
                }}
              >
                {showPassword ? (
                  <EyeOff width={20} height={20} color="#888" />
                ) : (
                  <Eye width={20} height={20} color="#888" />
                )}
              </TouchableOpacity>
            </View>

            <View style={{ position: 'relative', marginBottom: 16 }}>
              <TextInput
                placeholder="Confirm New Password"
                placeholderTextColor="#777"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
                autoCorrect={false}
                style={{
                  width: '100%',
                  paddingHorizontal: 16,
                  paddingVertical: 12,
                  paddingRight: 48,
                  borderRadius: 12,
                  backgroundColor: '#1C1C1E',
                  color: '#ffffff',
                  borderWidth: 1,
                  borderColor: 'rgba(255, 255, 255, 0.1)',
                  fontSize: 16,
                }}
              />
              <TouchableOpacity
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                style={{
                  position: 'absolute',
                  right: 16,
                  top: 14,
                }}
              >
                {showConfirmPassword ? (
                  <EyeOff width={20} height={20} color="#888" />
                ) : (
                  <Eye width={20} height={20} color="#888" />
                )}
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              onPress={handleResetPassword}
              disabled={loading}
              style={{
                marginTop: 24,
                width: '100%',
                backgroundColor: '#1C1C1E',
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.2)',
                paddingVertical: 12,
                borderRadius: 12,
                opacity: loading ? 0.6 : 1,
              }}
            >
              <Text style={{
                color: '#ffffff',
                fontWeight: 'bold',
                textAlign: 'center',
                fontSize: 16,
              }}>
                {loading ? 'Resetting...' : 'Reset Password'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};