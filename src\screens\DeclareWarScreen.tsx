import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import {
  Sword,
  Ship,
  MapPin,
  Users,
  ChevronDown,
  X,
  AlertTriangle,
  Zap,
  Clock,
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { warService } from '../services/warService';
import { stateService } from '../services/stateService';
import { regionService } from '../services/regionService';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

enum WarType {
  GROUND = 'ground',
  SEA = 'sea',
}

enum WarTarget {
  CONQUEST = 'conquest',
}

interface Region {
  id: string;
  name: string;
  population?: number;
  seaAccess?: boolean;
  state?: {
    id: string;
    name: string;
  };
}

interface FormData {
  warType: WarType;
  attackerRegionId: string;
  defenderRegionId: string;
  declaration: string;
}

export const DeclareWarScreen: React.FC<Props> = ({ navigation }) => {
  useAuthGuard({ navigation });
  
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [regions, setRegions] = useState<Region[]>([]);
  const [availableTargets, setAvailableTargets] = useState<Region[]>([]);
  const [loadingTargets, setLoadingTargets] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isStateLeader, setIsStateLeader] = useState(false);
  const [stateId, setStateId] = useState<string | null>(null);
  const [showWarTypeModal, setShowWarTypeModal] = useState(false);
  const [showAttackerModal, setShowAttackerModal] = useState(false);
  const [showDefenderModal, setShowDefenderModal] = useState(false);
  
  const [formData, setFormData] = useState<FormData>({
    warType: WarType.GROUND,
    attackerRegionId: '',
    defenderRegionId: '',
    declaration: '',
  });

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Check if user is state leader
      try {
        const state = await stateService.getStateLedByUser();
        if (state) {
          setStateId(state.id);
          setRegions(state.regions || []);
          setIsStateLeader(true);
        } else {
          setIsStateLeader(false);
        }
      } catch (err) {
        setIsStateLeader(false);
      }

      // Load all regions if user is state leader
      if (isStateLeader) {
        try {
          const allRegions = await regionService.getAllRegions();
          setRegions(allRegions || []);
        } catch (error) {
          console.error('Error loading regions:', error);
        }
      }
    } catch (error) {
      setError('Failed to load data');
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load initial data',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadInitialData();
  };

  const fetchAvailableTargets = async (warType: WarType, attackerRegionId: string) => {
    if (!warType || !attackerRegionId) {
      setAvailableTargets([]);
      return;
    }

    setLoadingTargets(true);
    try {
      const targets = await warService.getAvailableTargets(warType, attackerRegionId);
      setAvailableTargets(targets || []);
    } catch (error) {
      console.error('Error fetching available targets:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to fetch available targets',
      });
      setAvailableTargets([]);
    } finally {
      setLoadingTargets(false);
    }
  };

  const handleWarTypeChange = (warType: WarType) => {
    setFormData(prev => ({
      ...prev,
      warType,
      attackerRegionId: '',
      defenderRegionId: '',
    }));
    setAvailableTargets([]);
    setShowWarTypeModal(false);
  };

  const handleAttackerRegionChange = (regionId: string) => {
    setFormData(prev => ({
      ...prev,
      attackerRegionId: regionId,
      defenderRegionId: '',
    }));
    setAvailableTargets([]);
    setShowAttackerModal(false);
    
    if (regionId && formData.warType) {
      fetchAvailableTargets(formData.warType, regionId);
    }
  };

  const handleDefenderRegionChange = (regionId: string) => {
    setFormData(prev => ({
      ...prev,
      defenderRegionId: regionId,
    }));
    setShowDefenderModal(false);
  };

  const handleDeclareWar = async () => {
    if (!formData.attackerRegionId) {
      Toast.show({
        type: 'error',
        text1: 'Missing Selection',
        text2: 'Please select an attacking region',
      });
      return;
    }

    if (!formData.defenderRegionId) {
      Toast.show({
        type: 'error',
        text1: 'Missing Selection',
        text2: 'Please select a target region',
      });
      return;
    }

    const defenderRegion = availableTargets.find(r => r.id === formData.defenderRegionId);
    
    Alert.alert(
      'Declare War',
      `Are you sure you want to declare ${formData.warType} war against ${defenderRegion?.name}?\n\nThis action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Declare War', style: 'destructive', onPress: confirmDeclareWar },
      ]
    );
  };

  const confirmDeclareWar = async () => {
    try {
      setLoading(true);
      setError(null);

      const warData = {
        warType: formData.warType,
        warTarget: WarTarget.CONQUEST,
        declaration: formData.declaration || 'War has been declared!',
        attackerRegionId: formData.attackerRegionId,
        defenderRegionId: formData.defenderRegionId,
      };

      const createdWar = await warService.declareWar(warData);
      
      const defenderRegion = availableTargets.find(r => r.id === formData.defenderRegionId);
      
      Toast.show({
        type: 'success',
        text1: 'War Declared!',
        text2: `War declared successfully against ${defenderRegion?.name}!`,
      });

      // Navigate to war detail or wars list
      if (createdWar && createdWar.id) {
        navigation.navigate('WarDetail', { warId: createdWar.id });
      } else {
        navigation.navigate('Wars');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to declare war');
      Toast.show({
        type: 'error',
        text1: 'Declaration Failed',
        text2: err.message || 'Failed to declare war',
      });
    } finally {
      setLoading(false);
    }
  };

  const getWarTypeInfo = (type: WarType) => {
    switch (type) {
      case WarType.GROUND:
        return {
          name: 'GROUND WAR',
          duration: '24 HOURS',
          description: 'Direct territorial assault with infantry and armored divisions',
          icon: Sword,
          color: '#10b981',
          requirement: 'Requires land border access',
        };
      case WarType.SEA:
        return {
          name: 'SEA WAR',
          duration: '48 HOURS',
          description: 'Naval engagement to establish maritime dominance',
          icon: Ship,
          color: '#3b82f6',
          requirement: 'Requires sea access',
        };
      default:
        return {
          name: 'WAR',
          duration: '72 HOURS',
          description: 'Military conflict',
          icon: Sword,
          color: '#8b5cf6',
          requirement: 'Standard requirements',
        };
    }
  };

  const getAttackerRegions = () => {
    return regions.filter(region => {
      if (region.state?.id !== stateId) return false;
      if (formData.warType === WarType.SEA && !region.seaAccess) return false;
      return true;
    });
  };

  const selectedWarTypeInfo = getWarTypeInfo(formData.warType);
  const selectedAttackerRegion = regions.find(r => r.id === formData.attackerRegionId);
  const selectedDefenderRegion = availableTargets.find(r => r.id === formData.defenderRegionId);
  const attackerRegions = getAttackerRegions();

  if (loading && !refreshing) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <ActivityIndicator size="large" color="#ef4444" />
        <Text className="text-red-400 text-xl mt-4">Loading war center...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-900">
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View className="p-4">
          {/* Header */}
          <View className="items-center mb-8">
            <Text className="text-4xl font-black text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-red-800 uppercase tracking-wide text-center">
              WAR DECLARATION CENTER
            </Text>
            <Text className="text-gray-400 mt-2 font-medium text-center">
              Initiate military operations against enemy territories
            </Text>
          </View>

          {!isStateLeader ? (
            <View className="bg-gradient-to-r from-red-900 to-red-800 border-l-4 border-red-500 p-6 rounded-xl mb-8">
              <View className="flex-row items-center">
                <AlertTriangle width={24} height={24} color="#fbbf24" />
                <View className="ml-3">
                  <Text className="text-yellow-100 font-bold text-lg">
                    COMMAND AUTHORIZATION REQUIRED
                  </Text>
                  <Text className="text-yellow-100">
                    You must be the leader of your state to declare war.
                  </Text>
                </View>
              </View>
            </View>
          ) : (
            <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-2xl overflow-hidden">
              {/* Header Bar */}
              <View className="bg-gradient-to-r from-red-700 to-red-800 py-5 px-6">
                <View className="flex-row items-center">
                  <Zap width={24} height={24} color="#ffffff" />
                  <Text className="text-xl font-bold text-white ml-3 uppercase tracking-wide">
                    War Declaration Protocol
                  </Text>
                </View>
              </View>

              <View className="p-6 space-y-8">
                {/* War Type Selection */}
                <View>
                  <View className="flex-row items-center mb-4">
                    <View className="w-8 h-8 bg-gradient-to-r from-red-700 to-red-800 rounded-full items-center justify-center mr-3">
                      <Zap width={16} height={16} color="#ffffff" />
                    </View>
                    <Text className="text-xl font-bold text-white">War Type</Text>
                  </View>

                  <TouchableOpacity
                    onPress={() => setShowWarTypeModal(true)}
                    className="bg-gray-800 border-2 border-gray-700 rounded-xl p-4"
                  >
                    <View className="flex-row items-center justify-between">
                      <View className="flex-row items-center">
                        <selectedWarTypeInfo.icon width={24} height={24} color={selectedWarTypeInfo.color} />
                        <View className="ml-3">
                          <Text className="text-white font-bold">{selectedWarTypeInfo.name}</Text>
                          <Text className="text-gray-400 text-sm">{selectedWarTypeInfo.duration}</Text>
                        </View>
                      </View>
                      <ChevronDown width={20} height={20} color="#9ca3af" />
                    </View>
                  </TouchableOpacity>
                </View>

                {/* Region Selection */}
                <View>
                  <View className="flex-row items-center mb-4">
                    <View className="w-8 h-8 bg-gradient-to-r from-red-700 to-red-800 rounded-full items-center justify-center mr-3">
                      <MapPin width={16} height={16} color="#ffffff" />
                    </View>
                    <Text className="text-xl font-bold text-white">Region Selection</Text>
                  </View>

                  <View className="space-y-4">
                    {/* Attacking Region */}
                    <View>
                      <View className="flex-row items-center mb-3">
                        <View className="w-3 h-3 bg-red-500 rounded-full mr-2" />
                        <Text className="font-bold text-gray-300">Attacking Region</Text>
                      </View>
                      <TouchableOpacity
                        onPress={() => setShowAttackerModal(true)}
                        className="bg-gray-800 border-2 border-gray-700 rounded-xl p-4"
                      >
                        <View className="flex-row items-center justify-between">
                          <Text className="text-white">
                            {selectedAttackerRegion 
                              ? `${selectedAttackerRegion.name}${formData.warType === WarType.SEA && selectedAttackerRegion.seaAccess ? ' (Sea Access)' : ''}`
                              : 'Select deployment region'
                            }
                          </Text>
                          <ChevronDown width={20} height={20} color="#9ca3af" />
                        </View>
                      </TouchableOpacity>
                      {formData.warType === WarType.SEA && attackerRegions.length === 0 && (
                        <View className="mt-3 flex-row items-center">
                          <AlertTriangle width={16} height={16} color="#fbbf24" />
                          <Text className="text-yellow-400 text-sm ml-2">
                            Your state has no regions with sea access
                          </Text>
                        </View>
                      )}
                    </View>

                    {/* Target Region */}
                    <View>
                      <View className="flex-row items-center mb-3">
                        <View className="w-3 h-3 bg-red-500 rounded-full mr-2" />
                        <Text className="font-bold text-gray-300">Target Region</Text>
                      </View>
                      <TouchableOpacity
                        onPress={() => setShowDefenderModal(true)}
                        disabled={!formData.attackerRegionId || loadingTargets}
                        className={`bg-gray-800 border-2 border-gray-700 rounded-xl p-4 ${
                          !formData.attackerRegionId || loadingTargets ? 'opacity-50' : ''
                        }`}
                      >
                        <View className="flex-row items-center justify-between">
                          <Text className="text-white">
                            {loadingTargets 
                              ? 'Loading targets...'
                              : selectedDefenderRegion 
                              ? `${selectedDefenderRegion.name}${selectedDefenderRegion.state?.name ? ` (${selectedDefenderRegion.state.name})` : ''}`
                              : 'Select target region'
                            }
                          </Text>
                          <ChevronDown width={20} height={20} color="#9ca3af" />
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>

                {/* War Summary */}
                {selectedAttackerRegion && selectedDefenderRegion && (
                  <View className="bg-gray-700 rounded-xl p-6">
                    <Text className="text-xl font-bold text-white mb-4">War Summary</Text>
                    <View className="space-y-3">
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Type:</Text>
                        <Text className="text-white font-bold">{selectedWarTypeInfo.name}</Text>
                      </View>
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Duration:</Text>
                        <Text className="text-white">{selectedWarTypeInfo.duration}</Text>
                      </View>
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Attacker:</Text>
                        <Text className="text-white">{selectedAttackerRegion.name}</Text>
                      </View>
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Target:</Text>
                        <Text className="text-white">{selectedDefenderRegion.name}</Text>
                      </View>
                      {selectedDefenderRegion.state && (
                        <View className="flex-row justify-between">
                          <Text className="text-gray-400">Defender State:</Text>
                          <Text className="text-white">{selectedDefenderRegion.state.name}</Text>
                        </View>
                      )}
                    </View>
                  </View>
                )}

                {/* Error Display */}
                {error && (
                  <View className="bg-gradient-to-r from-red-900 to-red-800 border-l-4 border-red-500 p-4 rounded-xl">
                    <View className="flex-row items-center">
                      <AlertTriangle width={20} height={20} color="#fbbf24" />
                      <View className="ml-3">
                        <Text className="text-yellow-100 font-bold">TACTICAL ERROR</Text>
                        <Text className="text-yellow-100 mt-1">{error}</Text>
                      </View>
                    </View>
                  </View>
                )}

                {/* Action Buttons */}
                <View className="flex-row space-x-4 pt-4">
                  <TouchableOpacity
                    onPress={() => navigation.goBack()}
                    className="flex-1 bg-gray-800 border-2 border-gray-700 py-3 px-6 rounded-xl"
                  >
                    <View className="flex-row items-center justify-center">
                      <X width={20} height={20} color="#ffffff" />
                      <Text className="text-white font-bold ml-2">Cancel Operation</Text>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={handleDeclareWar}
                    disabled={loading || !formData.attackerRegionId || !formData.defenderRegionId}
                    className={`flex-1 bg-gradient-to-r from-red-700 to-red-800 py-3 px-6 rounded-xl ${
                      loading || !formData.attackerRegionId || !formData.defenderRegionId ? 'opacity-50' : ''
                    }`}
                  >
                    <View className="flex-row items-center justify-center">
                      {loading ? (
                        <ActivityIndicator size="small" color="#ffffff" />
                      ) : (
                        <Sword width={20} height={20} color="#ffffff" />
                      )}
                      <Text className="text-white font-bold ml-2">
                        {loading ? 'Declaring War...' : 'Declare War'}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}

          {/* Status Footer */}
          <View className="bg-gradient-to-r from-gray-800 to-black border-t border-red-500/30 py-3 px-6 mt-8 rounded-lg">
            <View className="flex-row items-center justify-center">
              <View className="w-2 h-2 bg-green-500 rounded-full mr-2" />
              <Text className="text-sm text-white">
                COMMAND STATUS: <Text className="text-green-500 font-bold">OPERATIONAL</Text>
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* War Type Selection Modal */}
      <Modal
        visible={showWarTypeModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowWarTypeModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className="bg-gray-800 rounded-lg max-w-md w-full p-6">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-xl font-bold text-white">Select War Type</Text>
              <TouchableOpacity onPress={() => setShowWarTypeModal(false)}>
                <X width={24} height={24} color="#9ca3af" />
              </TouchableOpacity>
            </View>

            <View className="space-y-4">
              {[WarType.GROUND, WarType.SEA].map(type => {
                const typeInfo = getWarTypeInfo(type);
                const isSelected = formData.warType === type;

                return (
                  <TouchableOpacity
                    key={type}
                    onPress={() => handleWarTypeChange(type)}
                    className={`p-4 rounded-xl border-2 ${
                      isSelected ? 'border-red-500 bg-red-900/20' : 'border-gray-700 bg-gray-700'
                    }`}
                  >
                    <View className="flex-row items-center mb-2">
                      <typeInfo.icon width={24} height={24} color={typeInfo.color} />
                      <Text className="text-white font-bold ml-3 text-lg">{typeInfo.name}</Text>
                    </View>
                    <Text className="text-gray-300 text-sm mb-2">{typeInfo.description}</Text>
                    <View className="flex-row items-center">
                      <Clock width={16} height={16} color="#ef4444" />
                      <Text className="text-red-400 text-sm ml-2">Duration: {typeInfo.duration}</Text>
                    </View>
                    <Text className="text-gray-400 text-xs mt-2">{typeInfo.requirement}</Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        </View>
      </Modal>

      {/* Attacker Region Selection Modal */}
      <Modal
        visible={showAttackerModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAttackerModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className="bg-gray-800 rounded-lg max-w-md w-full max-h-[80%]">
            <View className="flex-row justify-between items-center p-6 border-b border-gray-700">
              <Text className="text-xl font-bold text-white">Select Attacking Region</Text>
              <TouchableOpacity onPress={() => setShowAttackerModal(false)}>
                <X width={24} height={24} color="#9ca3af" />
              </TouchableOpacity>
            </View>

            <ScrollView className="p-6" showsVerticalScrollIndicator={false}>
              <View className="space-y-4">
                {attackerRegions.map(region => (
                  <TouchableOpacity
                    key={region.id}
                    onPress={() => handleAttackerRegionChange(region.id)}
                    className={`p-4 rounded-xl border-2 ${
                      formData.attackerRegionId === region.id 
                        ? 'border-red-500 bg-red-900/20' 
                        : 'border-gray-700 bg-gray-700'
                    }`}
                  >
                    <Text className="text-white font-bold text-lg">{region.name}</Text>
                    {formData.warType === WarType.SEA && region.seaAccess && (
                      <Text className="text-blue-400 text-sm">(Sea Access)</Text>
                    )}
                    <Text className="text-gray-400 text-sm">
                      Population: {region.population?.toLocaleString() || 'Unknown'}
                    </Text>
                  </TouchableOpacity>
                ))}
                {attackerRegions.length === 0 && (
                  <Text className="text-gray-400 text-center py-8">
                    No suitable regions available for {formData.warType} war
                  </Text>
                )}
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Defender Region Selection Modal */}
      <Modal
        visible={showDefenderModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowDefenderModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className="bg-gray-800 rounded-lg max-w-md w-full max-h-[80%]">
            <View className="flex-row justify-between items-center p-6 border-b border-gray-700">
              <Text className="text-xl font-bold text-white">Select Target Region</Text>
              <TouchableOpacity onPress={() => setShowDefenderModal(false)}>
                <X width={24} height={24} color="#9ca3af" />
              </TouchableOpacity>
            </View>

            <ScrollView className="p-6" showsVerticalScrollIndicator={false}>
              <View className="space-y-4">
                {availableTargets.map(region => (
                  <TouchableOpacity
                    key={region.id}
                    onPress={() => handleDefenderRegionChange(region.id)}
                    className={`p-4 rounded-xl border-2 ${
                      formData.defenderRegionId === region.id 
                        ? 'border-red-500 bg-red-900/20' 
                        : 'border-gray-700 bg-gray-700'
                    }`}
                  >
                    <Text className="text-white font-bold text-lg">{region.name}</Text>
                    {region.state && (
                      <Text className="text-blue-400 text-sm">({region.state.name})</Text>
                    )}
                    <Text className="text-gray-400 text-sm">
                      Population: {region.population?.toLocaleString() || 'Unknown'}
                    </Text>
                  </TouchableOpacity>
                ))}
                {availableTargets.length === 0 && !loadingTargets && (
                  <Text className="text-gray-400 text-center py-8">
                    No available targets. Select an attacking region first.
                  </Text>
                )}
                {loadingTargets && (
                  <View className="items-center py-8">
                    <ActivityIndicator size="large" color="#ef4444" />
                    <Text className="text-gray-400 mt-2">Loading targets...</Text>
                  </View>
                )}
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
};