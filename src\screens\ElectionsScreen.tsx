import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import {
  Vote,
  Calendar,
  AlertTriangle,
  Users,
  TrendingUp,
  Info,
  RefreshCw,
  Clock,
  X,
  CheckCircle,
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { stateService } from '../services/stateService';
import { stateElectionService } from '../services/stateElectionService';
import { StateElection } from '../types/stateElection';

interface UserState {
  id: string;
  name: string;
  leader?: {
    id: string;
    username: string;
  };
  regions?: Array<{
    id: string;
    name: string;
  }>;
}

import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}


export const ElectionsScreen: React.FC<Props> = ({ navigation }) => {
  useAuthGuard({ navigation });
  
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userState, setUserState] = useState<UserState | null>(null);
  const [activeElection, setActiveElection] = useState<StateElection | null>(null);
  const [activeTab, setActiveTab] = useState<'current' | 'history'>('current');
  const [electionTab, setElectionTab] = useState<'vote' | 'results' | 'info'>('vote');
  const [selectedCandidate, setSelectedCandidate] = useState<string | null>(null);
  const [voting, setVoting] = useState(false);
  const [showCandidateModal, setShowCandidateModal] = useState(false);
  const [selectedCandidateDetails, setSelectedCandidateDetails] = useState<any>(null);

  useEffect(() => {
    loadUserStateAndElection();
  }, [user]);

  const loadUserStateAndElection = async () => {
    try {
      setLoading(true);
      
      // First try to get the state the user leads or belongs to
      let state = null;
      try {
        if (user?.region?.state) {
          state = user.region.state;
        } else {
          state = await stateService.getStateLedByUser();
        }
      } catch (error) {
        // User doesn't lead a state, try to get their region's state
        if (user?.region?.state) {
          state = user.region.state;
        }
      }
      
      if (state) {
        // Transform state to match UserState interface
        const userStateData: UserState = {
          id: state.id,
          name: state.name,
          leader: state.leader ? {
            id: state.leader.id?.toString() || '',
            username: state.leader.username || ''
          } : undefined,
          regions: state.regions?.map(region => ({
            id: region.id,
            name: region.name
          }))
        };
        setUserState(userStateData);
        // Fetch active election for this state
        await loadActiveElection(state.id);
      } else {
        setUserState(null);
        setActiveElection(null);
      }
    } catch (error) {
      console.error('Failed to load user state:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load state information',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadActiveElection = async (stateId: string) => {
    try {
      // Use real election service to get active election
      const election = await stateElectionService.getActiveElection(stateId);
      setActiveElection(election);
    } catch (error) {
      console.error('Failed to load active election:', error);
      setActiveElection(null);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadUserStateAndElection();
  };

  const handleVote = async () => {
    if (!selectedCandidate || !activeElection) return;
    
    if (activeElection.hasUserVoted) {
      Toast.show({
        type: 'info',
        text1: 'Already Voted',
        text2: 'You have already voted in this election',
      });
      return;
    }

    const candidate = activeElection.candidates.find(c => c.id === selectedCandidate);
    if (!candidate) return;

    Alert.alert(
      'Confirm Vote',
      `Vote for ${candidate.user.username}?\n\nThis action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Vote', onPress: confirmVote },
      ]
    );
  };

  const confirmVote = async () => {
    if (!selectedCandidate || !activeElection) return;
    
    try {
      setVoting(true);
      
      // Use real election service to cast vote
      await stateElectionService.submitVote(activeElection.id, { candidateId: selectedCandidate });
      
      Toast.show({
        type: 'success',
        text1: 'Vote Cast!',
        text2: 'Your vote has been recorded successfully',
      });

      // Reload election data to get updated results
      if (userState) {
        await loadActiveElection(userState.id);
      }
      
      setSelectedCandidate(null);
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Vote Failed',
        text2: error?.message || 'Failed to cast vote',
      });
    } finally {
      setVoting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getVotePercentage = (voteCount: number, totalVotes: number) => {
    if (totalVotes === 0) return 0;
    return Math.round((voteCount / totalVotes) * 100);
  };

  const getTimeRemaining = (endTime: string) => {
    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const diff = end - now;
    
    if (diff <= 0) return 'Election ended';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h remaining`;
    }
    
    return `${hours}h ${minutes}m remaining`;
  };

  const openCandidateModal = (candidate: any) => {
    setSelectedCandidateDetails(candidate);
    setShowCandidateModal(true);
  };

  const renderNoStateMessage = () => (
    <View className="max-w-2xl mx-auto">
      <View className="bg-yellow-900 border border-yellow-500 rounded-lg p-6">
        <View className="flex-row items-center mb-4">
          <AlertTriangle width={24} height={24} color="#fbbf24" />
          <Text className="text-lg font-semibold text-white ml-3">
            No State Affiliation
          </Text>
        </View>
        <Text className="text-gray-300 mb-4">
          You are not currently affiliated with any state. To participate in state elections, you need to:
        </Text>
        <View className="mb-4">
          <Text className="text-gray-300 mb-2">• Be a resident of a region that belongs to a state</Text>
          <Text className="text-gray-300 mb-2">• Or be the leader of a state</Text>
        </View>
        <Text className="text-gray-400 text-sm">
          Contact your region's leadership or consider creating your own state to participate in elections.
        </Text>
      </View>
    </View>
  );

  const renderActiveElection = () => {
    if (!activeElection) return null;
    
    return (
      <View className="space-y-6">
      {/* Election Header */}
      <View className="bg-gray-800 rounded-lg p-6">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-2xl font-bold text-white">
            {activeElection.state.name} State Election
          </Text>
          <TouchableOpacity
            onPress={onRefresh}
            disabled={refreshing}
            className="p-2 bg-gray-700 rounded-lg"
          >
            <RefreshCw 
              width={20} 
              height={20} 
              color="#9ca3af" 
              style={{ transform: [{ rotate: refreshing ? '180deg' : '0deg' }] }}
            />
          </TouchableOpacity>
        </View>

        {/* Election Stats */}
        <View className="flex-row justify-between mb-4">
          <View className="bg-gray-700 rounded-lg p-4 flex-1 mr-2">
            <View className="flex-row items-center mb-1">
              <Users width={16} height={16} color="#60a5fa" />
              <Text className="text-blue-400 text-sm ml-2">Total Votes</Text>
            </View>
            <Text className="text-2xl font-bold text-white">
              {activeElection.totalVotes}
            </Text>
          </View>
          <View className="bg-gray-700 rounded-lg p-4 flex-1 ml-2">
            <View className="flex-row items-center mb-1">
              <Users width={16} height={16} color="#a78bfa" />
              <Text className="text-purple-400 text-sm ml-2">Candidates</Text>
            </View>
            <Text className="text-2xl font-bold text-white">
              {activeElection.candidates.length}
            </Text>
          </View>
        </View>

        {/* Election Dates */}
        <View className="space-y-2">
          <View className="flex-row items-center">
            <Calendar width={16} height={16} color="#9ca3af" />
            <Text className="text-gray-400 text-sm ml-2">
              Started: {formatDate(activeElection.startTime?.toString() || '')}
            </Text>
          </View>
          {activeElection.endTime && (
            <View className="flex-row items-center">
              <Clock width={16} height={16} color="#f59e0b" />
              <Text className="text-yellow-400 text-sm ml-2">
                {getTimeRemaining(activeElection.endTime?.toString() || '')}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Tab Navigation */}
      <View className="bg-gray-800 rounded-lg">
        <View className="flex-row border-b border-gray-700">
          {[
            { id: 'vote', label: 'Vote', icon: Users },
            { id: 'results', label: 'Live Results', icon: TrendingUp },
            { id: 'info', label: 'Information', icon: Info },
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <TouchableOpacity
                key={tab.id}
                onPress={() => setElectionTab(tab.id as any)}
                className={`flex-1 flex-row items-center justify-center py-4 ${
                  electionTab === tab.id
                    ? 'border-b-2 border-blue-400 bg-blue-900'
                    : ''
                }`}
              >
                <Icon width={16} height={16} color={electionTab === tab.id ? '#60a5fa' : '#9ca3af'} />
                <Text className={`ml-2 text-sm font-medium ${
                  electionTab === tab.id ? 'text-blue-400' : 'text-gray-400'
                }`}>
                  {tab.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Tab Content */}
        <View className="p-6">
          {electionTab === 'vote' && (
            <View>
              <Text className="text-lg font-semibold text-white mb-4">
                Cast Your Vote
              </Text>
              {activeElection.hasUserVoted ? (
                <View className="bg-green-900 border border-green-500 rounded-lg p-4 items-center">
                  <CheckCircle width={48} height={48} color="#22c55e" />
                  <Text className="text-green-400 font-bold text-lg mt-2">Vote Cast!</Text>
                  <Text className="text-green-300 text-center mt-1">
                    Thank you for participating in the democratic process.
                  </Text>
                </View>
              ) : (
                <View>
                  <Text className="text-gray-300 mb-4">
                    Select a candidate to vote for:
                  </Text>
                  <View className="space-y-3 mb-6">
                    {activeElection.candidates
                      .sort((a, b) => b.voteCount - a.voteCount)
                      .map((candidate) => (
                        <TouchableOpacity
                          key={candidate.id}
                          onPress={() => setSelectedCandidate(selectedCandidate === candidate.id ? null : candidate.id)}
                          className={`bg-gray-700 rounded-lg p-4 border-2 ${
                            selectedCandidate === candidate.id
                              ? 'border-blue-500 bg-blue-900'
                              : 'border-gray-600'
                          }`}
                        >
                          <View className="flex-row items-center justify-between mb-2">
                            <View className="flex-row items-center">
                              <Text className="text-white font-bold text-lg">
                                {candidate.user.username}
                              </Text>
                              <Text className="text-gray-400 text-sm ml-2">
                                LV.{candidate.user.level || 0}
                              </Text>
                            </View>
                            <TouchableOpacity
                              onPress={() => openCandidateModal(candidate)}
                              className="bg-gray-600 px-3 py-1 rounded"
                            >
                              <Text className="text-gray-300 text-xs">Details</Text>
                            </TouchableOpacity>
                          </View>
                          {candidate.manifesto && (
                            <Text className="text-gray-300 text-sm italic" numberOfLines={2}>
                              "{candidate.manifesto}"
                            </Text>
                          )}
                        </TouchableOpacity>
                      ))}
                  </View>
                  
                  <TouchableOpacity
                    onPress={handleVote}
                    disabled={!selectedCandidate || voting}
                    className={`w-full py-3 rounded-xl flex-row items-center justify-center ${
                      !selectedCandidate || voting
                        ? 'bg-gray-600'
                        : 'bg-gradient-to-r from-blue-600 to-blue-800'
                    }`}
                  >
                    {voting ? (
                      <ActivityIndicator size="small" color="#ffffff" />
                    ) : (
                      <Vote width={20} height={20} color="#ffffff" />
                    )}
                    <Text className="text-white font-bold ml-2">
                      {voting ? 'Casting Vote...' : 'Cast Vote'}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          )}

          {electionTab === 'results' && (
            <View>
              <Text className="text-lg font-semibold text-white mb-4">
                Live Results
              </Text>
              <View className="space-y-4">
                {activeElection.candidates
                  .sort((a, b) => b.voteCount - a.voteCount)
                  .map((candidate, index) => (
                    <View key={candidate.id} className="bg-gray-700 rounded-lg p-4">
                      <View className="flex-row items-center justify-between mb-3">
                        <View className="flex-row items-center">
                          <Text className="text-blue-400 font-bold text-lg mr-3">
                            #{index + 1}
                          </Text>
                          <View>
                            <Text className="text-white font-bold text-lg">
                              {candidate.user.username}
                            </Text>
                            <Text className="text-gray-400 text-sm">
                              LV.{candidate.user.level || 0}
                            </Text>
                          </View>
                        </View>
                        <View className="items-end">
                          <Text className="text-white font-medium">
                            {candidate.voteCount.toLocaleString()} votes
                          </Text>
                          <Text className="text-blue-400 font-bold text-lg">
                            {getVotePercentage(candidate.voteCount, activeElection.totalVotes)}%
                          </Text>
                        </View>
                      </View>
                      
                      {/* Vote Bar */}
                      <View className="w-full bg-gray-600 h-2 rounded-full overflow-hidden">
                        <View 
                          className={`h-full ${
                            index === 0 ? 'bg-gradient-to-r from-green-500 to-green-600' : 'bg-gradient-to-r from-blue-500 to-blue-600'
                          }`}
                          style={{ 
                            width: `${getVotePercentage(candidate.voteCount, activeElection.totalVotes)}%` 
                          }}
                        />
                      </View>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {electionTab === 'info' && (
            <View>
              <Text className="text-lg font-semibold text-white mb-4">
                Election Information
              </Text>
              <View className="bg-gray-700 rounded-lg p-4 space-y-3">
                <View>
                  <Text className="font-medium text-white mb-1">Duration</Text>
                  <Text className="text-gray-300 text-sm">
                    This election runs for {activeElection.duration} hours ({Math.floor(activeElection.duration / 24)} day) from start to finish.
                  </Text>
                </View>
                <View>
                  <Text className="font-medium text-white mb-1">Eligibility</Text>
                  <Text className="text-gray-300 text-sm">
                    Only residents of {activeElection.state.name} are eligible to vote in this election.
                  </Text>
                </View>
                <View>
                  <Text className="font-medium text-white mb-1">Voting Rules</Text>
                  <Text className="text-gray-300 text-sm">
                    Each eligible voter can cast one vote for their preferred candidate. Votes cannot be changed once submitted.
                  </Text>
                </View>
                <View>
                  <Text className="font-medium text-white mb-1">Winner Determination</Text>
                  <Text className="text-gray-300 text-sm">
                    The candidate with the most votes at the end of the election period will be declared the winner.
                  </Text>
                </View>
              </View>
            </View>
          )}
        </View>
      </View>
      </View>
    );
  };

  if (loading && !refreshing) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <ActivityIndicator size="large" color="#60a5fa" />
        <Text className="text-blue-400 text-xl mt-4">Loading elections...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-900">
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View className="p-4">
          {/* Header */}
          <View className="mb-8">
            <Text className="text-3xl font-bold text-white mb-2">State Elections</Text>
            <Text className="text-gray-400">
              {userState ? `Participate in democratic elections for ${userState.name}` : 'Democratic participation in state governance'}
            </Text>
          </View>

          {/* State Info */}
          {userState && (
            <View className="bg-gray-800 rounded-lg p-4 mb-6">
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <View className="w-12 h-12 bg-gray-700 rounded-lg items-center justify-center mr-4">
                    <Users width={24} height={24} color="#9ca3af" />
                  </View>
                  <View>
                    <Text className="text-xl font-semibold text-white">{userState.name}</Text>
                    <Text className="text-gray-400 text-sm">
                      Led by {userState.leader?.username || 'Unknown'} • {userState.regions?.length || 0} regions
                    </Text>
                  </View>
                </View>
                <View className="items-end">
                  <Text className="text-sm text-gray-400">Your Role</Text>
                  <Text className="text-white font-medium">
                    {user?.id === userState.leader?.id ? 'State Leader' : 'Citizen'}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Tab Navigation */}
          <View className="bg-gray-800 rounded-lg mb-6">
            <View className="flex-row border-b border-gray-700">
              {[
                { id: 'current', label: 'Current Election', icon: Vote },
                { id: 'history', label: 'Election History', icon: Calendar },
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <TouchableOpacity
                    key={tab.id}
                    onPress={() => setActiveTab(tab.id as any)}
                    className={`flex-1 flex-row items-center justify-center py-4 ${
                      activeTab === tab.id
                        ? 'border-b-2 border-blue-400 bg-blue-900'
                        : ''
                    }`}
                  >
                    <Icon width={16} height={16} color={activeTab === tab.id ? '#60a5fa' : '#9ca3af'} />
                    <Text className={`ml-2 font-medium ${
                      activeTab === tab.id ? 'text-blue-400' : 'text-gray-400'
                    }`}>
                      {tab.label}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>

          {/* Tab Content */}
          {!userState ? (
            renderNoStateMessage()
          ) : activeTab === 'current' ? (
            activeElection ? (
              renderActiveElection()
            ) : (
              <View className="bg-gray-800 rounded-lg p-8 items-center">
                <Calendar width={64} height={64} color="#6b7280" />
                <Text className="text-xl font-semibold text-white mb-2 mt-4">
                  No Active Election
                </Text>
                <Text className="text-gray-400 mb-4 text-center">
                  There is currently no active election in {userState.name}.
                </Text>
                <Text className="text-gray-500 text-sm text-center">
                  Elections are typically held every few months. Check back later or view past elections in the history tab.
                </Text>
              </View>
            )
          ) : (
            <View className="bg-gray-800 rounded-lg p-8 items-center">
              <Calendar width={64} height={64} color="#6b7280" />
              <Text className="text-xl font-semibold text-white mb-2 mt-4">
                Election History
              </Text>
              <Text className="text-gray-400 text-center">
                View past elections and results for {userState.name}.
              </Text>
              <Text className="text-gray-500 text-sm text-center mt-2">
                (This feature will be available soon)
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Candidate Details Modal */}
      <Modal
        visible={showCandidateModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCandidateModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className="bg-gray-800 rounded-lg max-w-md w-full p-6">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-xl font-bold text-white">
                Candidate Details
              </Text>
              <TouchableOpacity onPress={() => setShowCandidateModal(false)}>
                <X width={24} height={24} color="#9ca3af" />
              </TouchableOpacity>
            </View>
            
            {selectedCandidateDetails && (
              <View>
                <View className="mb-4">
                  <Text className="text-lg font-bold text-white mb-1">
                    {selectedCandidateDetails.user.username}
                  </Text>
                  <Text className="text-gray-400">
                    Level {selectedCandidateDetails.user.level || 0}
                  </Text>
                </View>
                
                {selectedCandidateDetails.manifesto && (
                  <View className="mb-4">
                    <Text className="text-white font-medium mb-2">Manifesto:</Text>
                    <Text className="text-gray-300 text-sm italic">
                      "{selectedCandidateDetails.manifesto}"
                    </Text>
                  </View>
                )}
                
                <View className="mb-4">
                  <Text className="text-white font-medium mb-1">Current Votes:</Text>
                  <Text className="text-blue-400 text-lg font-bold">
                    {selectedCandidateDetails.voteCount.toLocaleString()} votes
                  </Text>
                </View>
              </View>
            )}
            
            <TouchableOpacity
              onPress={() => setShowCandidateModal(false)}
              className="w-full bg-gray-700 py-3 rounded-xl"
            >
              <Text className="text-white text-center font-medium">Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};