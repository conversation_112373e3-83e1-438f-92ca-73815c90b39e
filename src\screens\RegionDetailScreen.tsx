import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  MapPin,
  Users,
  DollarSign,
  Building,
  Shield,
  Sword,
  Crown,
  ArrowLeft,
  Activity,
  TrendingUp,
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { stateService } from '../services/stateService';
import { regionService } from '../services/regionService';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Toast from 'react-native-toast-message';
import { generateStateColor } from '../utils/colorGenerator';
import { ProgressBar } from '../components/ProgressIndicator';

interface Props {
  navigation: any;
  route: any;
}

interface RegionDetail {
  id: string;
  name: string;
  population: number;
  economics: {
    gdp: number;
    unemployment: number;
    factories: number;
    activeFactories: number;
  };
  military: {
    totalSoldiers: number;
    activeSoldiers: number;
    defenseLevel: number;
  };
  politics: {
    state: {
      id: string;
      name: string;
      leader: {
        username: string;
      };
    };
    governorshipCandidates?: any[];
  };
  activities: {
    recentEvents: Array<{
      id: string;
      type: string;
      description: string;
      timestamp: string;
    }>;
    playerActivity: number; // 0-100
  };
}

export const RegionDetailScreen: React.FC<Props> = ({ navigation, route }) => {
  const { regionId, stateId } = route.params;
  const { user } = useAuthStore();
  useAuthGuard({ navigation });

  const [region, setRegion] = useState<RegionDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'economy' | 'military' | 'politics'>('overview');

  useEffect(() => {
    loadRegionDetail();
  }, [regionId]);

  const loadRegionDetail = async () => {
    try {
      setLoading(true);
      // Load actual region data from API
      const regionData = await regionService.getRegionById(regionId);

      // Transform the API response to match our RegionDetail interface
      const regionDetail: RegionDetail = {
        id: regionData.id,
        name: regionData.name,
        population: regionData.population || 0,
        economics: {
          gdp: regionData.gdp || 0,
          unemployment: regionData.unemploymentRate || 0,
          factories: regionData.totalFactories || 0,
          activeFactories: regionData.activeFactories || 0,
        },
        military: {
          totalSoldiers: regionData.totalSoldiers || 0,
          activeSoldiers: regionData.activeSoldiers || 0,
          defenseLevel: regionData.defenseLevel || 0,
        },
        politics: {
          state: regionData.state || {
            id: '',
            name: 'Unclaimed',
            leader: { username: 'None' },
          },
        },
        activities: {
          recentEvents: regionData.recentEvents || [],
          playerActivity: regionData.playerActivity || 0,
        },
      };

      setRegion(regionDetail);
    } catch (error) {
      console.error('Error loading region detail:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load region details',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadRegionDetail();
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const renderTabButton = (tab: string, icon: any, label: string) => {
    const IconComponent = icon;
    const isActive = activeTab === tab;
    
    return (
      <TouchableOpacity
        key={tab}
        onPress={() => setActiveTab(tab as any)}
        style={{
          paddingVertical: 12,
          paddingHorizontal: 16,
          borderBottomWidth: isActive ? 2 : 0,
          borderBottomColor: '#3f87ff',
          flex: 1,
          alignItems: 'center',
        }}
      >
        <IconComponent width={20} height={20} color={isActive ? '#3f87ff' : '#9CA3AF'} />
        <Text style={{
          color: isActive ? '#3f87ff' : '#9CA3AF',
          fontSize: 12,
          fontWeight: isActive ? '600' : '400',
          marginTop: 4,
        }}>
          {label}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderOverviewTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        Region Overview
      </Text>
      
      <View style={{ gap: 16 }}>
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Users width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Population
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 24, fontWeight: 'bold' }}>
            {region?.population.toLocaleString()}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 4 }}>
            Total residents
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Player Activity
            </Text>
          </View>
          <ProgressBar
            progress={region?.activities.playerActivity || 0}
            progressColor="#10B981"
            showPercentage={true}
          />
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Shield width={20} height={20} color={generateStateColor(0)} />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              State Information
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 18, fontWeight: '600' }}>
            {region?.politics.state.name}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 2 }}>
            Leader: {region?.politics.state.leader.username}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderEconomyTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        Economic Status
      </Text>
      
      <View style={{ gap: 16 }}>
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <DollarSign width={20} height={20} color="#F59E0B" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Regional GDP
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 24, fontWeight: 'bold' }}>
            ${formatNumber(region?.economics.gdp || 0)}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 4 }}>
            Gross Domestic Product
          </Text>
        </View>

        <View style={{ flexDirection: 'row', gap: 12 }}>
          <View style={{
            flex: 1,
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <Building width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold', marginTop: 8 }}>
              {region?.economics.factories}
            </Text>
            <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Total Factories</Text>
          </View>
          
          <View style={{
            flex: 1,
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold', marginTop: 8 }}>
              {region?.economics.activeFactories}
            </Text>
            <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Active Factories</Text>
          </View>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <TrendingUp width={20} height={20} color="#EF4444" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Unemployment Rate
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 24, fontWeight: 'bold' }}>
            {region?.economics.unemployment}%
          </Text>
          <ProgressBar
            progress={region?.economics.unemployment || 0}
            progressColor="#EF4444"
            height={6}
          />
        </View>
      </View>
    </View>
  );

  const renderMilitaryTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        Military Status
      </Text>
      
      <View style={{ gap: 16 }}>
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Sword width={20} height={20} color="#EF4444" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Total Military Force
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 24, fontWeight: 'bold' }}>
            {formatNumber(region?.military.totalSoldiers || 0)}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 4 }}>
            Total soldiers
          </Text>
        </View>

        <View style={{ flexDirection: 'row', gap: 12 }}>
          <View style={{
            flex: 1,
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold', marginTop: 8 }}>
              {formatNumber(region?.military.activeSoldiers || 0)}
            </Text>
            <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Active Soldiers</Text>
          </View>
          
          <View style={{
            flex: 1,
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <Shield width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold', marginTop: 8 }}>
              {region?.military.defenseLevel}%
            </Text>
            <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Defense Level</Text>
          </View>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Shield width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Defense Readiness
            </Text>
          </View>
          <ProgressBar
            progress={region?.military.defenseLevel || 0}
            progressColor="#3f87ff"
            height={8}
            showPercentage={true}
          />
          <Text style={{ color: '#9CA3AF', fontSize: 12, marginTop: 8 }}>
            Military preparedness level
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Troop Deployment
            </Text>
          </View>
          <View style={{ marginBottom: 8 }}>
            <Text style={{ color: '#9CA3AF', fontSize: 12, marginBottom: 4 }}>
              Active: {((region?.military.activeSoldiers || 0) / (region?.military.totalSoldiers || 1) * 100).toFixed(1)}%
            </Text>
            <ProgressBar
              progress={(region?.military.activeSoldiers || 0) / (region?.military.totalSoldiers || 1) * 100}
              progressColor="#10B981"
              height={6}
            />
          </View>
        </View>
      </View>
    </View>
  );

  const renderPoliticsTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        Political Status
      </Text>
      
      <View style={{ gap: 16 }}>
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Crown width={20} height={20} color="#F59E0B" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Governing State
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold' }}>
            {region?.politics.state.name}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 4 }}>
            Current controlling state
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Users width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              State Leadership
            </Text>
          </View>
          <Text style={{ color: '#ffffff', fontSize: 18, fontWeight: '600' }}>
            {region?.politics.state.leader.username}
          </Text>
          <Text style={{ color: '#9CA3AF', fontSize: 14, marginTop: 2 }}>
            Current state leader
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Activity width={20} height={20} color="#10B981" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Political Stability
            </Text>
          </View>
          <ProgressBar
            progress={75}
            progressColor="#10B981"
            height={8}
            showPercentage={true}
          />
          <Text style={{ color: '#9CA3AF', fontSize: 12, marginTop: 8 }}>
            Regional political stability rating
          </Text>
        </View>

        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Shield width={20} height={20} color="#3f87ff" />
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
              Governance Information
            </Text>
          </View>
          <View style={{ gap: 8 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#9CA3AF', fontSize: 14 }}>Government Type:</Text>
              <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '500' }}>Republic</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#9CA3AF', fontSize: 14 }}>Tax Rate:</Text>
              <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '500' }}>15%</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#9CA3AF', fontSize: 14 }}>Public Support:</Text>
              <Text style={{ color: '#10B981', fontSize: 14, fontWeight: '500' }}>High</Text>
            </View>
          </View>
        </View>

        {region?.politics.governorshipCandidates && region.politics.governorshipCandidates.length > 0 && (
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <Crown width={20} height={20} color="#F59E0B" />
              <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginLeft: 8 }}>
                Governorship Candidates
              </Text>
            </View>
            <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
              {region.politics.governorshipCandidates.length} candidate(s) running for regional governor
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={{
        flex: 1,
        backgroundColor: '#111827',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <ActivityIndicator size="large" color="#3f87ff" />
        <Text style={{ color: '#9CA3AF', fontSize: 16, marginTop: 16 }}>
          Loading region details...
        </Text>
      </View>
    );
  }

  if (!region) {
    return (
      <View style={{
        flex: 1,
        backgroundColor: '#111827',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
      }}>
        <MapPin width={48} height={48} color="#6B7280" />
        <Text style={{ color: '#9CA3AF', fontSize: 18, marginTop: 16, textAlign: 'center' }}>
          Region not found
        </Text>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            backgroundColor: '#3f87ff',
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 6,
            marginTop: 16,
          }}
        >
          <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '500' }}>
            Go Back
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#111827' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#374151',
      }}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={{ marginRight: 12 }}>
          <ArrowLeft width={24} height={24} color="#3f87ff" />
        </TouchableOpacity>
        <MapPin width={24} height={24} color="#3f87ff" />
        <Text style={{
          color: '#ffffff',
          fontSize: 20,
          fontWeight: 'bold',
          marginLeft: 8,
        }}>
          {region.name}
        </Text>
      </View>

      {/* Tabs */}
      <View style={{ 
        flexDirection: 'row', 
        backgroundColor: '#1F2937', 
        borderBottomWidth: 1, 
        borderBottomColor: '#374151' 
      }}>
        {renderTabButton('overview', Activity, 'Overview')}
        {renderTabButton('economy', DollarSign, 'Economy')}
        {renderTabButton('military', Shield, 'Military')}
        {renderTabButton('politics', Crown, 'Politics')}
      </View>

      {/* Content */}
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'economy' && renderEconomyTab()}
        {activeTab === 'military' && renderMilitaryTab()}
        {activeTab === 'politics' && renderPoliticsTab()}
      </ScrollView>
    </View>
  );
};