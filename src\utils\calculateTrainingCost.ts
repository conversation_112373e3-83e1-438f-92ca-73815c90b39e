interface TrainingCostParams {
  perkLevel: number;
  durationMinutes: number;
}

interface TrainingCost {
  gold: number;
  money: number;
}

export const calculateTrainingCost = ({
  perkLevel,
  durationMinutes
}: TrainingCostParams): TrainingCost => {
  const newLevel = perkLevel + 1;
  const hours = durationMinutes / 60;
  
  // Base costs from frontend
  const baseGoldCost = 2 * newLevel;
  const baseMoneyCost = 200 * newLevel;
  
  // Per-hour costs
  const goldPerHour = 0.0735 * Math.pow(newLevel, 1.2);
  const moneyPerHour = 273.5 * Math.pow(newLevel, 1.2);
  
  // Total costs
  const totalCostGold = baseGoldCost + goldPerHour * hours;
  const totalCostMoney = baseMoneyCost + moneyPerHour * hours;
  
  return {
    gold: Math.round(totalCostGold),
    money: Math.round(totalCostMoney)
  };
};