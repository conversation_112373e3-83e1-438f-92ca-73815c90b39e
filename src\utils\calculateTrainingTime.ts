interface TrainingTimeParams {
  perkLevel: number;
  eduResidency: number;
  currency: 'gold' | 'money';
  isPremium: boolean;
}

export const calculateTrainingTime = ({
  perkLevel,
  eduResidency,
  currency,
  isPremium
}: TrainingTimeParams): number => {
  const EDU_INDEX_RESIDENCY = eduResidency || 0;
  
  // Base calculation from frontend
  const base = 1 - EDU_INDEX_RESIDENCY / 50;
  let T = base * Math.pow(perkLevel + 1, 2) * (currency === "gold" ? 0.72 : 9.6);

  // Premium users get 50% faster training
  if (isPremium) {
    T /= 2;
  }

  // Level bonuses for faster training
  if (perkLevel + 1 <= 100) {
    T /= 2;
  }
  if (perkLevel + 1 <= 50) {
    T /= 2;
  }

  // Convert hours to minutes and ensure minimum duration
  const minutes = Math.max(T * 60, 1);
  
  return Math.round(minutes);
};