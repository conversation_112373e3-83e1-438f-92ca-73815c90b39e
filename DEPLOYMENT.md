# Warfront Nations Mobile - Deployment Guide

This guide covers the complete deployment process for the Warfront Nations React Native mobile application to both Google Play Store and Apple App Store.

## 🏗️ Build Configurations

The app is configured with multiple build variants for different environments and monetization models:

### Build Types
- **Debug**: Development builds with debugging enabled
- **Staging**: Pre-production builds for testing
- **Release**: Production builds for app stores

### Product Flavors
- **Free**: Free version with ads and limited features
- **Premium**: Paid version with all features unlocked

### Environment URLs
- **Debug**: `http://localhost:3000`
- **Staging**: `https://staging-api.warfrontnations.com`
- **Release**: `https://api.warfrontnations.com`

## 📱 Android Deployment

### Prerequisites

1. **Android Studio** with latest SDK tools
2. **Java 11** or higher
3. **Release Keystore** for signing APKs/AABs
4. **Google Play Console** developer account

### 1. Generate Release Keystore

```bash
keytool -genkey -v -keystore warfront-release-key.keystore -alias warfront-release-alias -keyalg RSA -keysize 2048 -validity 10000
```

Place the keystore file in `android/app/` directory.

### 2. Configure Signing

Add to `android/gradle.properties`:

```properties
WARFRONT_UPLOAD_STORE_FILE=warfront-release-key.keystore
WARFRONT_UPLOAD_KEY_ALIAS=warfront-release-alias
WARFRONT_UPLOAD_STORE_PASSWORD=your-store-password
WARFRONT_UPLOAD_KEY_PASSWORD=your-key-password
```

### 3. Build Release APKs/AABs

Run the automated build script:

```bash
chmod +x scripts/build-release.sh
./scripts/build-release.sh
```

Or build manually:

```bash
cd android

# Free version
./gradlew assembleFreeRelease
./gradlew bundleFreeRelease

# Premium version  
./gradlew assemblePremiumRelease
./gradlew bundlePremiumRelease
```

### 4. Upload to Google Play Console

1. **Upload AAB files** (recommended) to Google Play Console
2. **Configure app details**:
   - App name: Warfront Nations (Free/Premium)
   - Package name: `com.warfrontnations.mobile.free` / `com.warfrontnations.mobile`
   - Category: Strategy Games
3. **Add store listing assets**:
   - Screenshots (phone, tablet, 10-inch tablet)
   - Icon (512x512)
   - Feature graphic (1024x500)
   - Description and release notes
4. **Set up pricing** (Free vs Premium)
5. **Configure content rating**
6. **Submit for review**

## 🍎 iOS Deployment

### Prerequisites

1. **macOS** with Xcode 15+
2. **Apple Developer Account** ($99/year)
3. **iOS Development/Distribution Certificates**
4. **Provisioning Profiles**

### 1. Configure iOS Build

Update `ios/WarfrontNationsMobile/Info.plist`:

```xml
<key>CFBundleDisplayName</key>
<string>Warfront Nations</string>
<key>CFBundleIdentifier</key>
<string>com.warfrontnations.mobile</string>
<key>CFBundleVersion</key>
<string>1</string>
<key>CFBundleShortVersionString</key>
<string>1.0.0</string>
```

### 2. Build iOS App

```bash
cd ios
pod install

# Build for device
xcodebuild -workspace WarfrontNationsMobile.xcworkspace \
           -scheme WarfrontNationsMobile \
           -configuration Release \
           -destination generic/platform=iOS \
           archive -archivePath WarfrontNations.xcarchive

# Export IPA
xcodebuild -exportArchive \
           -archivePath WarfrontNations.xcarchive \
           -exportPath ./export \
           -exportOptionsPlist ExportOptions.plist
```

### 3. Upload to App Store Connect

1. **Use Xcode or Application Loader** to upload IPA
2. **Configure app information**:
   - App name and description
   - Keywords and category
   - Screenshots for all device sizes
   - App preview videos (optional)
3. **Set pricing tier**
4. **Submit for review**

## 🔧 Environment Configuration

### 1. Backend Environment Variables

Update `.env` files for each environment:

```env
# Development
API_BASE_URL=http://localhost:3000
STRIPE_PUBLIC_KEY=pk_test_...
GOOGLE_CLIENT_ID=...

# Staging  
API_BASE_URL=https://staging-api.warfrontnations.com
STRIPE_PUBLIC_KEY=pk_test_...
GOOGLE_CLIENT_ID=...

# Production
API_BASE_URL=https://api.warfrontnations.com
STRIPE_PUBLIC_KEY=pk_live_...
GOOGLE_CLIENT_ID=...
```

### 2. Firebase Configuration

1. **Create Firebase projects** for each environment
2. **Download configuration files**:
   - Android: `google-services.json`
   - iOS: `GoogleService-Info.plist`
3. **Place in appropriate directories**:
   - Android: `android/app/`
   - iOS: `ios/WarfrontNationsMobile/`

### 3. Push Notification Setup

1. **Configure FCM** for Android notifications
2. **Set up APNs** for iOS notifications  
3. **Update server-side** notification endpoints

## 🚀 CI/CD Pipeline

### GitHub Actions Workflow

Create `.github/workflows/build-and-deploy.yml`:

```yaml
name: Build and Deploy

on:
  push:
    tags:
      - 'v*'

jobs:
  build-android:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: npm ci
      - name: Build Android Release
        run: |
          cd android
          ./gradlew bundleRelease
      - name: Upload to Play Console
        # Add automated upload steps

  build-ios:
    runs-on: macos-latest  
    steps:
      - uses: actions/checkout@v3
      - name: Build iOS
        run: |
          cd ios
          pod install
          xcodebuild archive ...
      - name: Upload to App Store
        # Add automated upload steps
```

## 📊 Release Checklist

### Pre-Release
- [ ] Code review completed
- [ ] All tests passing
- [ ] Performance testing done
- [ ] Security audit completed
- [ ] Backend APIs ready
- [ ] Database migrations applied
- [ ] CDN and static assets updated

### App Store Preparation
- [ ] Screenshots taken for all device sizes
- [ ] App icon finalized (1024x1024)
- [ ] Store descriptions written
- [ ] Release notes prepared
- [ ] Privacy policy updated
- [ ] Terms of service current
- [ ] Age rating configured

### Post-Release
- [ ] Monitor crash reports
- [ ] Check user reviews
- [ ] Monitor server performance
- [ ] Track key metrics (DAU, retention, etc.)
- [ ] Prepare hotfix process

## 🔐 Security Considerations

### Code Obfuscation
- **Android**: ProGuard/R8 enabled for release builds
- **iOS**: Bitcode enabled for additional optimization

### API Security
- All API calls use HTTPS
- JWT tokens for authentication
- Rate limiting implemented
- Input validation on all endpoints

### Data Protection
- User data encrypted at rest
- Secure storage for sensitive data
- GDPR compliance implemented
- Regular security updates

## 📈 Monitoring and Analytics

### Crash Reporting
- **Firebase Crashlytics** configured
- Automatic crash reporting enabled
- Custom error logging implemented

### Analytics
- **Firebase Analytics** for user behavior
- Custom events for game actions
- Performance monitoring enabled
- A/B testing framework ready

### Performance Monitoring
- App startup time tracking
- Network request monitoring
- Memory usage optimization
- Battery usage considerations

## 🆘 Troubleshooting

### Common Build Issues

1. **Keystore not found**
   - Ensure keystore file is in correct location
   - Check gradle.properties configuration

2. **Dependency conflicts**
   - Clear node_modules and reinstall
   - Update React Native to latest stable

3. **Android build fails**
   - Clean gradle cache: `./gradlew clean`
   - Update SDK tools and build tools

4. **iOS build fails**
   - Update Xcode to latest version
   - Clear derived data
   - Reinstall CocoaPods: `pod install --repo-update`

### Support Contacts

- **Technical Issues**: <EMAIL>
- **Store Submission**: <EMAIL>
- **Emergency**: <EMAIL>

---

## 🎯 Version History

### v1.0.0 (Current)
- Initial release with core game features
- Real-time chat system
- War mechanics
- State management
- Factory system
- Elections
- Premium features

### Planned Updates
- v1.1.0: Enhanced mapping features
- v1.2.0: Alliance system
- v1.3.0: Tournament mode
- v2.0.0: Major UI overhaul