import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { ChevronRight, ChevronLeft, X, Check, Play } from 'lucide-react-native';
import { StepIndicator } from './ProgressIndicator';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon?: React.ComponentType<{ width?: number; height?: number; color?: string }>;
  action?: {
    label: string;
    onPress: () => void;
  };
  highlightComponent?: React.ReactNode;
}

interface TooltipProps {
  visible: boolean;
  text: string;
  position: { x: number; y: number };
  onDismiss: () => void;
}

export const Tooltip: React.FC<TooltipProps> = ({ visible, text, position, onDismiss }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, fadeAnim]);

  if (!visible) return null;

  return (
    <Modal transparent visible={visible} onRequestClose={onDismiss}>
      <TouchableOpacity
        style={{ flex: 1 }}
        activeOpacity={1}
        onPress={onDismiss}
      >
        <Animated.View
          style={{
            position: 'absolute',
            left: position.x,
            top: position.y,
            backgroundColor: '#1F2937',
            padding: 12,
            borderRadius: 8,
            maxWidth: 200,
            borderWidth: 1,
            borderColor: '#3f87ff',
            opacity: fadeAnim,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 8,
          }}
        >
          <Text style={{
            color: '#ffffff',
            fontSize: 14,
            lineHeight: 20,
          }}>
            {text}
          </Text>
          {/* Arrow pointing down */}
          <View style={{
            position: 'absolute',
            top: -6,
            left: 20,
            width: 0,
            height: 0,
            borderLeftWidth: 6,
            borderRightWidth: 6,
            borderBottomWidth: 6,
            borderLeftColor: 'transparent',
            borderRightColor: 'transparent',
            borderBottomColor: '#3f87ff',
          }} />
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
};

interface UserOnboardingProps {
  visible: boolean;
  onComplete: () => void;
  onSkip: () => void;
  steps: OnboardingStep[];
}

export const UserOnboarding: React.FC<UserOnboardingProps> = ({
  visible,
  onComplete,
  onSkip,
  steps,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const slideAnim = useRef(new Animated.Value(0)).current;
  const { width } = Dimensions.get('window');

  const animateToStep = (stepIndex: number) => {
    Animated.timing(slideAnim, {
      toValue: -stepIndex * width,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      const nextIndex = currentStep + 1;
      setCurrentStep(nextIndex);
      animateToStep(nextIndex);
    } else {
      onComplete();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      const prevIndex = currentStep - 1;
      setCurrentStep(prevIndex);
      animateToStep(prevIndex);
    }
  };

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
    animateToStep(stepIndex);
  };

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onSkip}>
      <SafeAreaView style={{ flex: 1, backgroundColor: '#111827' }}>
        {/* Header */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingVertical: 16,
          borderBottomWidth: 1,
          borderBottomColor: '#374151',
        }}>
          <Text style={{
            color: '#ffffff',
            fontSize: 20,
            fontWeight: '600',
          }}>
            Welcome to Warfront Nations
          </Text>
          <TouchableOpacity onPress={onSkip}>
            <X width={24} height={24} color="#9CA3AF" />
          </TouchableOpacity>
        </View>

        {/* Progress Indicator */}
        <View style={{ paddingVertical: 20, alignItems: 'center' }}>
          <StepIndicator
            currentStep={currentStep + 1}
            totalSteps={steps.length}
          />
          <Text style={{
            color: '#9CA3AF',
            fontSize: 14,
            marginTop: 8,
          }}>
            Step {currentStep + 1} of {steps.length}
          </Text>
        </View>

        {/* Content */}
        <View style={{ flex: 1, overflow: 'hidden' }}>
          <Animated.View
            style={{
              flexDirection: 'row',
              width: width * steps.length,
              flex: 1,
              transform: [{ translateX: slideAnim }],
            }}
          >
            {steps.map((step, index) => (
              <View
                key={step.id}
                style={{
                  width,
                  flex: 1,
                  paddingHorizontal: 20,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                {/* Icon */}
                {step.icon && (
                  <View style={{
                    backgroundColor: '#1F2937',
                    borderRadius: 40,
                    padding: 20,
                    marginBottom: 32,
                    borderWidth: 2,
                    borderColor: '#3f87ff',
                  }}>
                    <step.icon width={40} height={40} color="#3f87ff" />
                  </View>
                )}

                {/* Title */}
                <Text style={{
                  color: '#ffffff',
                  fontSize: 24,
                  fontWeight: 'bold',
                  textAlign: 'center',
                  marginBottom: 16,
                }}>
                  {step.title}
                </Text>

                {/* Description */}
                <Text style={{
                  color: '#D1D5DB',
                  fontSize: 16,
                  textAlign: 'center',
                  lineHeight: 24,
                  marginBottom: 32,
                  paddingHorizontal: 20,
                }}>
                  {step.description}
                </Text>

                {/* Action Button */}
                {step.action && (
                  <TouchableOpacity
                    onPress={step.action.onPress}
                    style={{
                      backgroundColor: '#3f87ff',
                      paddingHorizontal: 24,
                      paddingVertical: 12,
                      borderRadius: 8,
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginBottom: 20,
                    }}
                  >
                    <Play width={16} height={16} color="#ffffff" style={{ marginRight: 8 }} />
                    <Text style={{
                      color: '#ffffff',
                      fontSize: 16,
                      fontWeight: '600',
                    }}>
                      {step.action.label}
                    </Text>
                  </TouchableOpacity>
                )}

                {/* Highlight Component */}
                {step.highlightComponent}
              </View>
            ))}
          </Animated.View>
        </View>

        {/* Navigation */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingVertical: 20,
          borderTopWidth: 1,
          borderTopColor: '#374151',
        }}>
          <TouchableOpacity
            onPress={prevStep}
            disabled={currentStep === 0}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 8,
              backgroundColor: currentStep === 0 ? 'transparent' : '#374151',
              opacity: currentStep === 0 ? 0.5 : 1,
            }}
          >
            <ChevronLeft width={20} height={20} color="#ffffff" />
            <Text style={{ color: '#ffffff', marginLeft: 4 }}>Back</Text>
          </TouchableOpacity>

          <TouchableOpacity onPress={onSkip}>
            <Text style={{
              color: '#9CA3AF',
              fontSize: 14,
              textDecorationLine: 'underline',
            }}>
              Skip Tutorial
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={nextStep}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 8,
              backgroundColor: '#3f87ff',
            }}
          >
            <Text style={{ color: '#ffffff', marginRight: 4 }}>
              {isLastStep ? 'Complete' : 'Next'}
            </Text>
            {isLastStep ? (
              <Check width={20} height={20} color="#ffffff" />
            ) : (
              <ChevronRight width={20} height={20} color="#ffffff" />
            )}
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

// Example usage with predefined steps
export const defaultOnboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Warfront Nations',
    description: 'Join millions of players in this strategic political simulation game where you can build your power, form alliances, and shape the world.',
    icon: ({ width, height, color }) => <Text style={{ fontSize: 40 }}>🌍</Text>,
  },
  {
    id: 'profile',
    title: 'Build Your Profile',
    description: 'Start by setting up your profile, choosing your region, and defining your political stance. Your choices will shape your journey.',
    icon: ({ width, height, color }) => <Text style={{ fontSize: 40 }}>👤</Text>,
    action: {
      label: 'View Profile',
      onPress: () => {
        // Navigate to profile
      },
    },
  },
  {
    id: 'training',
    title: 'Train Your Character',
    description: 'Improve your stats through training. Higher stats give you advantages in wars, politics, and economics.',
    icon: ({ width, height, color }) => <Text style={{ fontSize: 40 }}>💪</Text>,
    action: {
      label: 'Start Training',
      onPress: () => {
        // Navigate to training
      },
    },
  },
  {
    id: 'party',
    title: 'Join or Create a Party',
    description: 'Political parties are the backbone of the game. Join an existing party or create your own to build influence.',
    icon: ({ width, height, color }) => <Text style={{ fontSize: 40 }}>🏛️</Text>,
    action: {
      label: 'Explore Parties',
      onPress: () => {
        // Navigate to parties
      },
    },
  },
  {
    id: 'economy',
    title: 'Work & Economy',
    description: 'Work in factories to earn money and resources. The stronger the economy, the more powerful your region becomes.',
    icon: ({ width, height, color }) => <Text style={{ fontSize: 40 }}>🏭</Text>,
    action: {
      label: 'Find Work',
      onPress: () => {
        // Navigate to factories
      },
    },
  },
  {
    id: 'complete',
    title: 'You\'re Ready!',
    description: 'You now have everything you need to start your journey in Warfront Nations. Good luck, and may the best strategist win!',
    icon: ({ width, height, color }) => <Text style={{ fontSize: 40 }}>🎉</Text>,
  },
];

export default UserOnboarding;