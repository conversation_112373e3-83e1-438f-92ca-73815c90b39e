import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { theme } from '../styles/theme';
import { runNetworkDiagnostics, getNetworkInfo, NetworkTestResult } from '../utils/networkTest';
import { getApiUrl } from '../utils/platformUtils';

export const NetworkDebugScreen: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<{
    internet: NetworkTestResult;
    backend: NetworkTestResult;
    summary: string;
  } | null>(null);

  const runTests = async () => {
    setIsLoading(true);
    try {
      const diagnostics = await runNetworkDiagnostics();
      setResults(diagnostics);
    } catch (error) {
      Alert.alert('Error', 'Failed to run network tests');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = () => {
    const info = getNetworkInfo();
    const debugInfo = {
      ...info,
      results,
    };
    
    // In a real app, you'd use Clipboard API
    console.log('Debug Info:', JSON.stringify(debugInfo, null, 2));
    Alert.alert('Debug Info', 'Check console for detailed debug information');
  };

  const ResultCard: React.FC<{ title: string; result: NetworkTestResult }> = ({ title, result }) => (
    <View style={styles.resultCard}>
      <View style={styles.resultHeader}>
        <Text style={styles.resultTitle}>{title}</Text>
        <View style={[styles.statusBadge, result.success ? styles.successBadge : styles.errorBadge]}>
          <Text style={styles.statusText}>{result.success ? 'PASS' : 'FAIL'}</Text>
        </View>
      </View>
      <Text style={styles.resultMessage}>{result.message}</Text>
      {result.url && <Text style={styles.resultUrl}>URL: {result.url}</Text>}
      {result.details && <Text style={styles.resultDetails}>Details: {result.details}</Text>}
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Network Diagnostics</Text>
        <Text style={styles.subtitle}>Debug connectivity issues</Text>
      </View>

      <View style={styles.infoCard}>
        <Text style={styles.infoTitle}>Current Configuration</Text>
        <Text style={styles.infoText}>API URL: {getApiUrl()}</Text>
        <Text style={styles.infoText}>Platform: {require('react-native').Platform.OS}</Text>
      </View>

      <TouchableOpacity
        style={[styles.button, isLoading && styles.buttonDisabled]}
        onPress={runTests}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Run Network Tests</Text>
        )}
      </TouchableOpacity>

      {results && (
        <View style={styles.resultsContainer}>
          <Text style={styles.summaryText}>{results.summary}</Text>
          
          <ResultCard title="Internet Connection" result={results.internet} />
          <ResultCard title="Backend Connection" result={results.backend} />

          <TouchableOpacity style={styles.copyButton} onPress={copyToClipboard}>
            <Text style={styles.copyButtonText}>Copy Debug Info</Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.troubleshootingCard}>
        <Text style={styles.troubleshootingTitle}>Troubleshooting Tips</Text>
        <Text style={styles.troubleshootingText}>
          1. Make sure your backend server is running on port 3000{'\n'}
          2. Check that your phone and computer are on the same WiFi network{'\n'}
          3. Try running 'npm run get-ip' to get your computer's IP address{'\n'}
          4. Update the API_BASE_URL in your .env file with the correct IP{'\n'}
          5. Restart the Expo development server after changing .env
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  header: {
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSize.header,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
  },
  infoCard: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.medium,
    marginBottom: theme.spacing.md,
  },
  infoTitle: {
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  infoText: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  button: {
    backgroundColor: theme.colors.primary,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.medium,
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.bold,
  },
  resultsContainer: {
    marginBottom: theme.spacing.lg,
  },
  summaryText: {
    fontSize: theme.fontSize.large,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.medium,
  },
  resultCard: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.medium,
    marginBottom: theme.spacing.md,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  resultTitle: {
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.small,
  },
  successBadge: {
    backgroundColor: theme.colors.success,
  },
  errorBadge: {
    backgroundColor: theme.colors.error,
  },
  statusText: {
    color: '#fff',
    fontSize: theme.fontSize.small,
    fontWeight: theme.fontWeight.bold,
  },
  resultMessage: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  resultUrl: {
    fontSize: theme.fontSize.small,
    color: theme.colors.textMuted,
    fontFamily: 'monospace',
  },
  resultDetails: {
    fontSize: theme.fontSize.small,
    color: theme.colors.textMuted,
    fontStyle: 'italic',
  },
  copyButton: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.medium,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  copyButtonText: {
    color: theme.colors.text,
    fontSize: theme.fontSize.medium,
  },
  troubleshootingCard: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.medium,
    marginBottom: theme.spacing.xl,
  },
  troubleshootingTitle: {
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  troubleshootingText: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
};
