import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
} from 'react-native';
import { AlertTriangle, ArrowLeft, Shield, DollarSign, TrendingUp, TrendingDown } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { budgetService } from '../services/budgetService';
import { stateService } from '../services/stateService';
import { showErrorToast, showSuccessToast } from '../utils/toastUtils';

interface Props {
  navigation: any;
  route: any;
}

interface BudgetSummary {
  currentTreasury: number;
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  incomeTaxRate: number;
  borderCrossingTaxRate: number;
  lastUpdated: string;
}

interface State {
  id: string;
  name: string;
  leader: {
    id: number;
    username: string;
  };
  treasury: number;
}

export const StateBudgetDashboardScreen: React.FC<Props> = ({ navigation, route }) => {
  useAuthGuard({ navigation });
  
  const { user } = useAuthStore();
  const { stateId } = route.params || {};
  
  const [state, setState] = useState<State | null>(null);
  const [budgetSummary, setBudgetSummary] = useState<BudgetSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLeader, setIsLeader] = useState(false);

  // Load state data and check permissions
  useEffect(() => {
    const loadStateData = async () => {
      if (!stateId) {
        setError('State ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const stateData = await stateService.getState(stateId);
        setState(stateData);

        // Check if user is the state leader
        const userIsLeader = user?.id === stateData.leader.id;
        setIsLeader(userIsLeader);

        // If user is not the leader, show error and go back
        if (!userIsLeader) {
          showErrorToast('You must be the state leader to access the budget dashboard');
          navigation.goBack();
          return;
        }

        // Load budget summary
        const summary = await budgetService.getBudgetSummary(stateId);
        setBudgetSummary(summary);

        setError(null);
      } catch (err: any) {
        console.error('Failed to load state data:', err);
        const errorMessage = err?.response?.data?.message || 'Failed to load state data';
        setError(errorMessage);
        showErrorToast(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    loadStateData();
  }, [stateId, user?.id, navigation]);

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-gray-900">
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#3f87ff" />
          <Text className="text-white mt-4">Loading budget dashboard...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !state) {
    return (
      <SafeAreaView className="flex-1 bg-gray-900">
        <View className="flex-1 p-4">
          <View className="bg-red-900/20 border border-red-400/30 rounded-lg p-6">
            <View className="items-center">
              <AlertTriangle width={48} height={48} color="#ef4444" />
              <Text className="text-xl font-semibold text-white mt-4 mb-2">Access Denied</Text>
              <Text className="text-red-400 text-center mb-4">
                {error || 'You do not have permission to access this budget dashboard.'}
              </Text>
              <TouchableOpacity
                className="px-4 py-2 bg-neonBlue rounded-lg"
                onPress={() => navigation.goBack()}
              >
                <Text className="text-white font-semibold">Go Back</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-900">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-gray-700">
        <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4">
          <ArrowLeft width={24} height={24} color="#ffffff" />
        </TouchableOpacity>
        <Shield width={24} height={24} color="#3f87ff" />
        <Text className="text-xl font-bold text-white ml-2">Budget Dashboard</Text>
      </View>

      <ScrollView className="flex-1 p-4">
        {/* State Info */}
        <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
          <Text className="text-2xl font-bold text-white mb-2">{state.name}</Text>
          <Text className="text-gray-300">Leader: {state.leader.username}</Text>
          <View className="flex-row items-center mt-4">
            <DollarSign width={20} height={20} color="#10b981" />
            <Text className="text-lg font-semibold text-white ml-2">
              Treasury: {state.treasury.toLocaleString()} 💰
            </Text>
          </View>
        </View>

        {/* Budget Summary */}
        {budgetSummary && (
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <Text className="text-xl font-bold text-white mb-4">Budget Overview</Text>
            
            <View className="space-y-4">
              <View className="flex-row justify-between items-center">
                <Text className="text-gray-300">Current Treasury</Text>
                <Text className="text-white font-semibold">
                  {budgetSummary.currentTreasury.toLocaleString()} 💰
                </Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <View className="flex-row items-center">
                  <TrendingUp width={16} height={16} color="#10b981" />
                  <Text className="text-gray-300 ml-2">Total Income</Text>
                </View>
                <Text className="text-green-400 font-semibold">
                  +{budgetSummary.totalIncome.toLocaleString()}
                </Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <View className="flex-row items-center">
                  <TrendingDown width={16} height={16} color="#ef4444" />
                  <Text className="text-gray-300 ml-2">Total Expenses</Text>
                </View>
                <Text className="text-red-400 font-semibold">
                  -{budgetSummary.totalExpenses.toLocaleString()}
                </Text>
              </View>
              
              <View className="border-t border-gray-600 pt-4">
                <View className="flex-row justify-between items-center">
                  <Text className="text-white font-semibold">Net Income</Text>
                  <Text className={`font-bold ${budgetSummary.netIncome >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {budgetSummary.netIncome >= 0 ? '+' : ''}{budgetSummary.netIncome.toLocaleString()}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Tax Configuration */}
        {budgetSummary && (
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <Text className="text-xl font-bold text-white mb-4">Tax Configuration</Text>
            
            <View className="space-y-4">
              <View className="flex-row justify-between items-center">
                <Text className="text-gray-300">Income Tax Rate</Text>
                <Text className="text-white font-semibold">
                  {(budgetSummary.incomeTaxRate * 100).toFixed(1)}%
                </Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <Text className="text-gray-300">Border Crossing Tax</Text>
                <Text className="text-white font-semibold">
                  {(budgetSummary.borderCrossingTaxRate * 100).toFixed(1)}%
                </Text>
              </View>
            </View>

            <TouchableOpacity 
              className="mt-4 bg-neonBlue rounded-lg p-3"
              onPress={() => {
                // TODO: Navigate to tax configuration screen
                showErrorToast('Tax configuration coming soon');
              }}
            >
              <Text className="text-white text-center font-semibold">Configure Taxes</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Quick Actions */}
        <View className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <Text className="text-xl font-bold text-white mb-4">Quick Actions</Text>
          
          <View className="space-y-3">
            <TouchableOpacity 
              className="bg-gray-700 rounded-lg p-4 flex-row items-center justify-between"
              onPress={() => {
                // TODO: Navigate to transaction history
                showErrorToast('Transaction history coming soon');
              }}
            >
              <Text className="text-white font-medium">View Transaction History</Text>
              <ArrowLeft width={20} height={20} color="#6b7280" style={{ transform: [{ rotate: '180deg' }] }} />
            </TouchableOpacity>
            
            <TouchableOpacity 
              className="bg-gray-700 rounded-lg p-4 flex-row items-center justify-between"
              onPress={() => {
                // TODO: Navigate to budget analytics
                showErrorToast('Budget analytics coming soon');
              }}
            >
              <Text className="text-white font-medium">Budget Analytics</Text>
              <ArrowLeft width={20} height={20} color="#6b7280" style={{ transform: [{ rotate: '180deg' }] }} />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default StateBudgetDashboardScreen;
