import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import { MapPin, Lock, History, Crown, CheckCircle, XCircle, Clock } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { travelService } from '../services/travelService';
import { stateService } from '../services/stateService';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

interface TravelPermission {
  id: number;
  userId: number;
  stateId: number;
  destinationRegionId: number;
  status: 'pending' | 'approved' | 'denied';
  reason?: string;
  createdAt: string;
  user?: {
    id: number;
    username: string;
  };
  destinationRegion?: {
    id: number;
    name: string;
  };
  state?: {
    id: number;
    name: string;
  };
}

export const TravelPermissionsScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuthStore();
  useAuthGuard({ navigation });

  const [activeTab, setActiveTab] = useState<'incoming' | 'outgoing' | 'history'>('outgoing');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isStateLeader, setIsStateLeader] = useState(false);
  const [ledState, setLedState] = useState<any>(null);
  const [incomingRequests, setIncomingRequests] = useState<TravelPermission[]>([]);
  const [outgoingRequests, setOutgoingRequests] = useState<TravelPermission[]>([]);
  const [processingRequests, setProcessingRequests] = useState(new Set<number>());

  useEffect(() => {
    checkStateLeadership();
  }, []);

  useEffect(() => {
    if (!loading) {
      fetchData();
    }
  }, [activeTab, loading]);

  const checkStateLeadership = async () => {
    try {
      setLoading(true);
      const state = await stateService.getStateLedByUser();
      
      if (state) {
        setLedState(state);
        setIsStateLeader(true);
        setActiveTab('incoming'); // Default to incoming for state leaders
      } else {
        setIsStateLeader(false);
        setActiveTab('outgoing'); // Default to outgoing for regular users
      }
    } catch (err) {
      console.error('Error checking state leadership:', err);
      setIsStateLeader(false);
    } finally {
      setLoading(false);
    }
  };

  const fetchData = async () => {
    try {
      if (activeTab === 'incoming' && isStateLeader) {
        const data = await travelService.getIncomingPermissions();
        setIncomingRequests(data);
      } else if (activeTab === 'outgoing') {
        const data = await travelService.getMyPermissions();
        setOutgoingRequests(data);
      }
    } catch (error) {
      console.error('Error fetching travel permissions:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load travel permissions'
      });
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const handlePermissionAction = async (permissionId: number, action: 'approve' | 'deny') => {
    setProcessingRequests(prev => new Set(prev).add(permissionId));

    try {
      if (action === 'approve') {
        await travelService.approvePermission(permissionId);
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Permission approved'
        });
      } else {
        await travelService.denyPermission(permissionId);
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Permission denied'
        });
      }
      
      await fetchData();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || `Failed to ${action} permission`
      });
    } finally {
      setProcessingRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(permissionId);
        return newSet;
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#10B981';
      case 'denied':
        return '#EF4444';
      case 'pending':
        return '#F59E0B';
      default:
        return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle width={16} height={16} color="#10B981" />;
      case 'denied':
        return <XCircle width={16} height={16} color="#EF4444" />;
      case 'pending':
        return <Clock width={16} height={16} color="#F59E0B" />;
      default:
        return <Clock width={16} height={16} color="#6B7280" />;
    }
  };

  const renderPermissionCard = (permission: TravelPermission, isIncoming: boolean = false) => (
    <View
      key={permission.id}
      style={{
        backgroundColor: '#1F2937',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
      }}
    >
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 12 }}>
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 16, fontWeight: '600', color: '#ffffff', marginBottom: 4 }}>
            {isIncoming 
              ? `Request from ${permission.user?.username || 'Unknown User'}`
              : `Travel to ${permission.destinationRegion?.name || 'Unknown Region'}`
            }
          </Text>
          <Text style={{ fontSize: 14, color: '#9CA3AF' }}>
            {isIncoming 
              ? `Wants to travel to ${permission.destinationRegion?.name || 'Unknown Region'}`
              : `Requested from ${permission.state?.name || 'Unknown State'}`
            }
          </Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {getStatusIcon(permission.status)}
          <Text style={{ 
            color: getStatusColor(permission.status), 
            fontSize: 12, 
            fontWeight: '600', 
            marginLeft: 4,
            textTransform: 'uppercase'
          }}>
            {permission.status}
          </Text>
        </View>
      </View>

      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
        <Text style={{ fontSize: 12, color: '#6B7280' }}>
          {new Date(permission.createdAt).toLocaleDateString()}
        </Text>
      </View>

      {permission.reason && (
        <View style={{ 
          backgroundColor: '#374151', 
          borderRadius: 8, 
          padding: 12, 
          marginBottom: 12 
        }}>
          <Text style={{ fontSize: 14, color: '#D1D5DB' }}>
            Reason: {permission.reason}
          </Text>
        </View>
      )}

      {isIncoming && permission.status === 'pending' && (
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <TouchableOpacity
            onPress={() => handlePermissionAction(permission.id, 'approve')}
            disabled={processingRequests.has(permission.id)}
            style={{
              flex: 1,
              backgroundColor: processingRequests.has(permission.id) ? '#065F46' : '#10B981',
              paddingVertical: 10,
              borderRadius: 8,
              alignItems: 'center',
            }}
          >
            <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '600' }}>
              {processingRequests.has(permission.id) ? 'Approving...' : 'Approve'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={() => handlePermissionAction(permission.id, 'deny')}
            disabled={processingRequests.has(permission.id)}
            style={{
              flex: 1,
              backgroundColor: processingRequests.has(permission.id) ? '#7F1D1D' : '#EF4444',
              paddingVertical: 10,
              borderRadius: 8,
              alignItems: 'center',
            }}
          >
            <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '600' }}>
              {processingRequests.has(permission.id) ? 'Denying...' : 'Deny'}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={{ flex: 1, backgroundColor: '#111827', justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#3f87ff" />
        <Text style={{ color: '#3f87ff', fontSize: 18, marginTop: 16 }}>Loading permissions...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#111827' }}>
      {/* Header */}
      <View style={{ padding: 16, paddingTop: 40 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <MapPin width={32} height={32} color="#3f87ff" />
          <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#ffffff', marginLeft: 12 }}>
            Travel Permissions
          </Text>
        </View>
        
        <Text style={{ fontSize: 16, color: '#9CA3AF', marginBottom: 16 }}>
          Manage travel permissions for your state or view your own requests
        </Text>

        {isStateLeader && ledState && (
          <View style={{
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            borderWidth: 1,
            borderColor: 'rgba(245, 158, 11, 0.3)',
            borderRadius: 12,
            padding: 12,
            marginBottom: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
              <Crown width={20} height={20} color="#F59E0B" />
              <Text style={{ color: '#F59E0B', fontSize: 14, fontWeight: '600', marginLeft: 8 }}>
                You are the leader of:
              </Text>
            </View>
            <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '500' }}>
              {ledState.name}
            </Text>
          </View>
        )}
      </View>

      {/* Tabs */}
      <View style={{ flexDirection: 'row', paddingHorizontal: 16, marginBottom: 16, borderBottomWidth: 1, borderBottomColor: '#374151' }}>
        {isStateLeader && (
          <TouchableOpacity
            onPress={() => setActiveTab('incoming')}
            style={{
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderBottomWidth: activeTab === 'incoming' ? 2 : 0,
              borderBottomColor: '#3f87ff',
            }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Lock width={16} height={16} color={activeTab === 'incoming' ? '#3f87ff' : '#9CA3AF'} />
              <Text style={{ 
                color: activeTab === 'incoming' ? '#3f87ff' : '#9CA3AF',
                fontSize: 16,
                fontWeight: activeTab === 'incoming' ? '600' : '400',
                marginLeft: 8
              }}>
                Incoming
              </Text>
            </View>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          onPress={() => setActiveTab('outgoing')}
          style={{
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderBottomWidth: activeTab === 'outgoing' ? 2 : 0,
            borderBottomColor: '#3f87ff',
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <History width={16} height={16} color={activeTab === 'outgoing' ? '#3f87ff' : '#9CA3AF'} />
            <Text style={{ 
              color: activeTab === 'outgoing' ? '#3f87ff' : '#9CA3AF',
              fontSize: 16,
              fontWeight: activeTab === 'outgoing' ? '600' : '400',
              marginLeft: 8
            }}>
              My Requests
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ padding: 16 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {activeTab === 'incoming' && isStateLeader && (
          <View>
            {incomingRequests.length === 0 ? (
              <View style={{ alignItems: 'center', paddingVertical: 40 }}>
                <Lock width={48} height={48} color="#6B7280" />
                <Text style={{ color: '#9CA3AF', fontSize: 18, marginTop: 16, textAlign: 'center' }}>
                  No incoming requests
                </Text>
                <Text style={{ color: '#6B7280', fontSize: 14, marginTop: 8, textAlign: 'center' }}>
                  Travel permission requests will appear here
                </Text>
              </View>
            ) : (
              incomingRequests.map(permission => renderPermissionCard(permission, true))
            )}
          </View>
        )}

        {activeTab === 'outgoing' && (
          <View>
            {outgoingRequests.length === 0 ? (
              <View style={{ alignItems: 'center', paddingVertical: 40 }}>
                <History width={48} height={48} color="#6B7280" />
                <Text style={{ color: '#9CA3AF', fontSize: 18, marginTop: 16, textAlign: 'center' }}>
                  No travel requests
                </Text>
                <Text style={{ color: '#6B7280', fontSize: 14, marginTop: 8, textAlign: 'center' }}>
                  Your travel permission requests will appear here
                </Text>
              </View>
            ) : (
              outgoingRequests.map(permission => renderPermissionCard(permission, false))
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
};