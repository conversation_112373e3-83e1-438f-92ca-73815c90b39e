import * as Notifications from 'expo-notifications';
import { Platform, Alert, Linking } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { api } from './api';

export interface NotificationData {
  type: 'message' | 'war' | 'election' | 'factory' | 'system';
  title: string;
  body: string;
  data?: Record<string, any>;
}

export interface PushNotificationToken {
  token: string;
  platform: 'ios' | 'android';
  deviceId?: string;
}

class PushNotificationService {
  private isInitialized = false;
  private currentToken: string | null = null;
  
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Request permission
      await this.requestPermission();
      
      // Configure local notifications
      this.configureLocalNotifications();
      
      // Setup Firebase messaging
      await this.setupFirebaseMessaging();
      
      // Get and register FCM token
      await this.registerFCMToken();
      
      this.isInitialized = true;
      console.log('Push notification service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
    }
  }

  private async requestPermission(): Promise<boolean> {
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (!enabled) {
        Alert.alert(
          'Notification Permission',
          'Enable notifications to receive updates about wars, messages, and other important events.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => Linking.openSettings() }
          ]
        );
        return false;
      }

      // Request Android notification permission for API 33+
      if (Platform.OS === 'android' && Platform.Version >= 33) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
        );
        
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Android notification permission denied');
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  private configureLocalNotifications(): void {
    PushNotification.configure({
      onNotification: (notification) => {
        console.log('Local notification received:', notification);
        
        if (notification.userInteraction) {
          // User tapped on notification
          this.handleNotificationTap(notification);
        }
      },
      
      // Android specific
      requestPermissions: Platform.OS === 'ios',
      popInitialNotification: true,
    });

    // Create default notification channels for Android
    if (Platform.OS === 'android') {
      PushNotification.createChannel({
        channelId: 'default',
        channelName: 'Default',
        channelDescription: 'Default notification channel',
        importance: Importance.HIGH,
        vibrate: true,
      });

      PushNotification.createChannel({
        channelId: 'messages',
        channelName: 'Messages',
        channelDescription: 'Chat messages',
        importance: Importance.HIGH,
        vibrate: true,
      });

      PushNotification.createChannel({
        channelId: 'wars',
        channelName: 'Wars',
        channelDescription: 'War notifications',
        importance: Importance.HIGH,
        vibrate: true,
      });

      PushNotification.createChannel({
        channelId: 'elections',
        channelName: 'Elections',
        channelDescription: 'Election notifications',
        importance: Importance.DEFAULT,
        vibrate: true,
      });
    }
  }

  private async setupFirebaseMessaging(): Promise<void> {
    // Handle background/killed state messages
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('Message handled in the background:', remoteMessage);
      await this.handleBackgroundMessage(remoteMessage);
    });

    // Handle foreground messages
    messaging().onMessage(async (remoteMessage) => {
      console.log('Message received in foreground:', remoteMessage);
      await this.handleForegroundMessage(remoteMessage);
    });

    // Handle notification opened app
    messaging().onNotificationOpenedApp((remoteMessage) => {
      console.log('Notification caused app to open:', remoteMessage);
      this.handleNotificationOpen(remoteMessage);
    });

    // Check if app was opened by a notification
    const initialNotification = await messaging().getInitialNotification();
    if (initialNotification) {
      console.log('App opened by notification:', initialNotification);
      this.handleNotificationOpen(initialNotification);
    }

    // Handle token refresh
    messaging().onTokenRefresh((token) => {
      console.log('FCM token refreshed:', token);
      this.currentToken = token;
      this.sendTokenToServer(token);
    });
  }

  private async registerFCMToken(): Promise<void> {
    try {
      const token = await messaging().getToken();
      if (token) {
        this.currentToken = token;
        console.log('FCM Token:', token);
        
        // Store token locally
        await AsyncStorage.setItem('fcm_token', token);
        
        // Send to server
        await this.sendTokenToServer(token);
      }
    } catch (error) {
      console.error('Failed to get FCM token:', error);
    }
  }

  private async sendTokenToServer(token: string): Promise<void> {
    try {
      const tokenData: PushNotificationToken = {
        token,
        platform: Platform.OS as 'ios' | 'android',
        deviceId: await this.getDeviceId(),
      };

      await api.post('/notifications/register-token', tokenData);
      console.log('Token registered with server');
    } catch (error) {
      console.error('Failed to register token with server:', error);
    }
  }

  private async getDeviceId(): Promise<string> {
    // You might want to use a library like react-native-device-info
    // For now, generate a simple unique identifier
    let deviceId = await AsyncStorage.getItem('device_id');
    if (!deviceId) {
      deviceId = `${Platform.OS}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await AsyncStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  }

  private async handleForegroundMessage(remoteMessage: FirebaseMessagingTypes.RemoteMessage): Promise<void> {
    const { notification, data } = remoteMessage;
    
    if (notification) {
      // Show local notification for foreground messages
      this.showLocalNotification({
        type: (data?.type as any) || 'system',
        title: notification.title || 'New Notification',
        body: notification.body || '',
        data: data || {},
      });
    }
  }

  private async handleBackgroundMessage(remoteMessage: FirebaseMessagingTypes.RemoteMessage): Promise<void> {
    const { notification, data } = remoteMessage;
    console.log('Background message processing:', { notification, data });
    
    // Update badge count, sync data, etc.
    if (data?.type === 'message') {
      // Update unread message count
      try {
        // You could sync chat data here
      } catch (error) {
        console.error('Failed to handle background message:', error);
      }
    }
  }

  private handleNotificationOpen(remoteMessage: FirebaseMessagingTypes.RemoteMessage): void {
    const { data } = remoteMessage;
    
    // Navigate based on notification type
    if (data?.type === 'message' && data?.chatId) {
      // Navigate to chat
      console.log('Navigate to chat:', data.chatId);
    } else if (data?.type === 'war' && data?.warId) {
      // Navigate to war
      console.log('Navigate to war:', data.warId);
    } else if (data?.type === 'election' && data?.electionId) {
      // Navigate to election
      console.log('Navigate to election:', data.electionId);
    }
  }

  private handleNotificationTap(notification: any): void {
    console.log('Notification tapped:', notification);
    
    if (notification.data) {
      const { type, chatId, warId, electionId } = notification.data;
      
      if (type === 'message' && chatId) {
        console.log('Navigate to chat from local notification:', chatId);
      } else if (type === 'war' && warId) {
        console.log('Navigate to war from local notification:', warId);
      }
    }
  }

  showLocalNotification(notificationData: NotificationData): void {
    const { type, title, body, data } = notificationData;
    
    let channelId = 'default';
    switch (type) {
      case 'message':
        channelId = 'messages';
        break;
      case 'war':
        channelId = 'wars';
        break;
      case 'election':
        channelId = 'elections';
        break;
    }

    PushNotification.localNotification({
      channelId,
      title,
      message: body,
      playSound: true,
      soundName: 'default',
      userInfo: data,
      category: type,
    });
  }

  async scheduleLocalNotification(notificationData: NotificationData, date: Date): Promise<void> {
    const { type, title, body, data } = notificationData;
    
    let channelId = 'default';
    switch (type) {
      case 'message':
        channelId = 'messages';
        break;
      case 'war':
        channelId = 'wars';
        break;
      case 'election':
        channelId = 'elections';
        break;
    }

    PushNotification.localNotificationSchedule({
      channelId,
      title,
      message: body,
      date,
      playSound: true,
      soundName: 'default',
      userInfo: data,
      category: type,
    });
  }

  async clearAllNotifications(): Promise<void> {
    PushNotification.removeAllDeliveredNotifications();
    
    if (Platform.OS === 'ios') {
      await messaging().deleteToken();
    }
  }

  async updateNotificationSettings(settings: {
    messages?: boolean;
    wars?: boolean;
    elections?: boolean;
    system?: boolean;
  }): Promise<void> {
    try {
      await api.post('/notifications/settings', settings);
      await AsyncStorage.setItem('notification_settings', JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to update notification settings:', error);
    }
  }

  async getNotificationSettings(): Promise<any> {
    try {
      const stored = await AsyncStorage.getItem('notification_settings');
      return stored ? JSON.parse(stored) : {
        messages: true,
        wars: true,
        elections: true,
        system: true,
      };
    } catch (error) {
      console.error('Failed to get notification settings:', error);
      return {
        messages: true,
        wars: true,
        elections: true,
        system: true,
      };
    }
  }

  getCurrentToken(): string | null {
    return this.currentToken;
  }

  async unregisterToken(): Promise<void> {
    try {
      if (this.currentToken) {
        await api.delete('/notifications/unregister-token', {
          data: { token: this.currentToken }
        });
      }
      
      await AsyncStorage.removeItem('fcm_token');
      this.currentToken = null;
    } catch (error) {
      console.error('Failed to unregister token:', error);
    }
  }
}

// Export singleton instance
export const pushNotificationService = new PushNotificationService();