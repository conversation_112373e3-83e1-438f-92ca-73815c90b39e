{"name": "WarfrontNationsMobile", "version": "0.0.1", "private": true, "scripts": {"lint": "eslint .", "start": "expo start", "test": "jest", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "get-ip": "node scripts/get-local-ip.js", "start:tunnel": "expo start --tunnel"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.7", "axios": "^1.11.0", "babel-preset-expo": "~13.0.0", "expo": "^53.0.22", "expo-application": "~6.1.5", "expo-auth-session": "~6.2.1", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-crypto": "~14.1.5", "expo-device": "~7.1.4", "expo-image-picker": "~16.1.4", "expo-location": "~18.1.6", "expo-maps": "~0.11.0", "expo-network": "~7.1.5", "expo-notifications": "~0.31.4", "expo-secure-store": "^14.2.4", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.539.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-geojson": "^0.1.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.3.3", "react-native-web": "^0.20.0", "socket.io-client": "^4.8.1", "zustand": "^5.0.7", "react-native-maps": "1.20.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.13", "@types/react": "~19.0.10", "@types/react-test-renderer": "~19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "engines": {"node": ">=18"}}