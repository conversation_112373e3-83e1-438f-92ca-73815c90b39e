import { Platform } from 'react-native';

export const theme = {
  colors: {
    primary: '#3f87ff', // neonBlue from frontend
    secondary: '#00ff87', // neonGreen from frontend
    background: '#0a0c1b', // exact match from frontend
    surface: '#161b22', // darkCard from frontend
    border: '#0f3460',
    text: '#ffffff',
    textSecondary: '#cccccc',
    textMuted: '#666666',
    success: '#00ff87', // neonGreen
    warning: '#f39c12',
    error: '#e74c3c',
    premium: '#f39c12',
    gold: '#f1c40f',
    darkBg: '#0d1117', // from frontend
    neonBlue: '#3f87ff',
    neonGreen: '#00ff87',
  },
  spacing: {
    xs: 5,
    sm: 10,
    md: 15,
    lg: 20,
    xl: 25,
    xxl: 30,
  },
  borderRadius: {
    small: 4,
    medium: 8,
    large: 12,
    xlarge: 16,
  },
  fontSize: {
    small: 12,
    medium: 14,
    large: 16,
    xlarge: 18,
    xxlarge: 20,
    title: 24,
    header: 28,
    hero: 32,
  },
  fontWeight: {
    normal: '400' as const,
    medium: '500' as const,
    bold: '600' as const,
    heavy: '700' as const,
  },
  // Platform-specific shadow styles
  shadows: {
    small: Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
      },
    }),
    medium: Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15)',
      },
    }),
    large: Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
      web: {
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
      },
    }),
  },
} as const;

export type Theme = typeof theme;