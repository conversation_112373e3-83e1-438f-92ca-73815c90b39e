import { Platform, Dimensions } from 'react-native';
import Constants from 'expo-constants';

export const isWeb = Platform.OS === 'web';
export const isIOS = Platform.OS === 'ios';
export const isAndroid = Platform.OS === 'android';
export const isMobile = isIOS || isAndroid;

// Get screen dimensions
export const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window');
  return { width, height };
};

// Check if device is tablet
export const isTablet = () => {
  const { width, height } = getScreenDimensions();
  const aspectRatio = width / height;
  return Math.min(width, height) >= 600 && (aspectRatio > 1.2 || aspectRatio < 0.9);
};

// Get platform-specific API URL
export const getApiUrl = () => {
  const baseUrl = Constants.expoConfig?.extra?.apiBaseUrl;

  if (baseUrl) {
    console.log('Using API URL from config:', baseUrl);
    return baseUrl;
  }

  // Development URLs
  if (__DEV__) {
    if (isWeb) {
      console.log('Using localhost for web');
      return 'http://localhost:3000';
    } else {
      // For mobile development, try to detect local IP from Expo
      // First try hostUri from the manifest
      const hostUri = Constants.expoConfig?.hostUri;
      if (hostUri) {
        const debuggerHost = hostUri.split(':')[0];
        const mobileUrl = `http://${debuggerHost}:3000`;
        console.log('Using hostUri IP for mobile:', mobileUrl);
        return mobileUrl;
      }

      // Try to get IP from debuggerHost in expoGo config
      const debuggerHost = Constants.expoConfig?.debuggerHost?.split(':')[0];
      if (debuggerHost) {
        const mobileUrl = `http://${debuggerHost}:3000`;
        console.log('Using debuggerHost IP for mobile:', mobileUrl);
        return mobileUrl;
      }

      // Try to extract from manifest string if available
      try {
        const manifestString = Constants.expoConfig?.manifestString;
        if (manifestString) {
          const manifest = JSON.parse(manifestString);
          const expoGoHost = manifest.extra?.expoGo?.debuggerHost?.split(':')[0];
          if (expoGoHost) {
            const mobileUrl = `http://${expoGoHost}:3000`;
            console.log('Using manifest debuggerHost IP for mobile:', mobileUrl);
            return mobileUrl;
          }
        }
      } catch (e) {
        console.log('Could not parse manifest string');
      }

      // Fallback to .env configured IP
      console.log('Using fallback IP for mobile');
      return 'http://************:3000';
    }
  }

  // Production URL
  return 'https://your-production-api.com';
};

// Platform-specific styling helpers
export const platformStyles = {
  // Shadow styles that work across platforms
  shadow: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      },
    }),
  },
  
  // Border radius that works consistently
  borderRadius: {
    small: 4,
    medium: 8,
    large: 12,
  },
  
  // Font sizes that scale properly
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
  },
  
  // Spacing that works across platforms
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
};

// Get safe area insets for different platforms
export const getSafeAreaInsets = () => {
  if (isWeb) {
    return { top: 0, bottom: 0, left: 0, right: 0 };
  }
  
  // For mobile, this should be used with react-native-safe-area-context
  return { top: 44, bottom: 34, left: 0, right: 0 }; // Default iOS values
};

// Check if running in Expo Go
export const isExpoGo = () => {
  return Constants.appOwnership === 'expo';
};

// Get device info
export const getDeviceInfo = () => {
  return {
    platform: Platform.OS,
    version: Platform.Version,
    isTablet: isTablet(),
    isExpoGo: isExpoGo(),
    screenDimensions: getScreenDimensions(),
  };
};
