import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { AlertTriangle, Home, LogIn } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';

interface Props {
  navigation: any;
}

export const NotFoundScreen: React.FC<Props> = ({ navigation }) => {
  const { isAuthenticated } = useAuthStore();
  const { width } = Dimensions.get('window');

  const handleGoHome = () => {
    if (isAuthenticated) {
      navigation.navigate('Home');
    } else {
      navigation.navigate('Landing');
    }
  };

  const handleGoLogin = () => {
    navigation.navigate('Login');
  };

  return (
    <View style={{ 
      flex: 1, 
      backgroundColor: '#111827',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 24,
    }}>
      {/* Logo */}
      <View style={{ alignItems: 'center', marginBottom: 40 }}>
        <Image
          source={require('../../assets/images/wn-logo.png')}
          style={{ width: 80, height: 80, marginBottom: 16 }}
          resizeMode="contain"
        />
        <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#ffffff' }}>
          WARFRONT NATIONS
        </Text>
      </View>

      {/* Error Icon */}
      <View style={{ 
        alignItems: 'center', 
        marginBottom: 32,
        position: 'relative',
      }}>
        <View style={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          borderWidth: 1,
          borderColor: 'rgba(239, 68, 68, 0.3)',
          borderRadius: 50,
          padding: 16,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <AlertTriangle width={48} height={48} color="#EF4444" />
        </View>
      </View>

      {/* Error Text */}
      <View style={{ alignItems: 'center', marginBottom: 40 }}>
        <Text style={{ 
          fontSize: 72, 
          fontWeight: '900', 
          color: '#EF4444', 
          marginBottom: 16,
          letterSpacing: 4,
        }}>
          404
        </Text>
        <Text style={{ 
          fontSize: 24, 
          fontWeight: 'bold', 
          color: '#ffffff', 
          marginBottom: 12,
          textAlign: 'center',
        }}>
          MISSION ABORTED
        </Text>
        <Text style={{ 
          fontSize: 16, 
          color: '#9CA3AF', 
          textAlign: 'center', 
          lineHeight: 24,
          maxWidth: width - 80,
        }}>
          The coordinates you're seeking don't exist in our database. 
          The page has either been moved or never existed.
        </Text>
      </View>

      {/* Action Buttons */}
      <View style={{ width: '100%', maxWidth: 300, gap: 16 }}>
        <TouchableOpacity
          onPress={handleGoHome}
          style={{
            backgroundColor: '#3f87ff',
            paddingVertical: 16,
            paddingHorizontal: 24,
            borderRadius: 12,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            shadowColor: '#3f87ff',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 8,
          }}
        >
          <Home width={20} height={20} color="#ffffff" style={{ marginRight: 8 }} />
          <Text style={{ 
            color: '#ffffff', 
            fontSize: 18, 
            fontWeight: '600',
            letterSpacing: 1,
          }}>
            RETURN HOME
          </Text>
        </TouchableOpacity>
        
        {!isAuthenticated && (
          <TouchableOpacity
            onPress={handleGoLogin}
            style={{
              backgroundColor: 'transparent',
              borderWidth: 2,
              borderColor: '#3f87ff',
              paddingVertical: 16,
              paddingHorizontal: 24,
              borderRadius: 12,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <LogIn width={20} height={20} color="#3f87ff" style={{ marginRight: 8 }} />
            <Text style={{ 
              color: '#3f87ff', 
              fontSize: 18, 
              fontWeight: '600',
              letterSpacing: 1,
            }}>
              LOGIN
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Additional Info */}
      <View style={{ marginTop: 24, alignItems: 'center' }}>
        <Text style={{ 
          fontSize: 12, 
          color: '#6B7280',
          textAlign: 'center',
        }}>
          Error Code: 404 | Status: Page Not Found
        </Text>
      </View>
    </View>
  );
};