import React, { useState, useEffect } from 'react';
import { Text, View, ScrollView, TouchableOpacity, ActivityIndicator, Image } from 'react-native';
import { Users, MapPin, Flag, Sword, Clock, Crown } from 'lucide-react-native';
import { userService } from '../services/userService';
import { regionService } from '../services/regionService';
import { stateService } from '../services/stateService';
import { warService } from '../services/warService';

interface Props {
  navigation: any;
}

interface GameStats {
  totalPlayers: number;
  totalRegions: number;
  totalStates: number;
  activeWars: number;
  totalWars: number;
  ongoingElections: number;
  totalPopulation: number;
}

export const LandingScreen: React.FC<Props> = ({ navigation }) => {
  const [stats, setStats] = useState<GameStats>({
    totalPlayers: 0,
    totalRegions: 0,
    totalStates: 0,
    activeWars: 0,
    totalWars: 0,
    ongoingElections: 0,
    totalPopulation: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGameStats = async () => {
      try {
        setLoading(true);
        const [
          playersCount,
          regionsData,
          statesData,
          activeWarsData,
          globalWarStats,
        ] = await Promise.all([
          userService.getAllUsersCount().catch(() => 0),
          regionService.getAllRegionsCount().catch(() => 0),
          stateService.getAllStatesCount().catch(() => 0),
          warService.getActiveWars().catch(() => []),
          warService.getGlobalWarStats().catch(() => ({ totalWars: 0 })),
        ]);
console.log(playersCount,'playersCount')
        setStats({
          totalPlayers: playersCount,
          totalRegions: regionsData,
          totalStates: statesData,
          activeWars: activeWarsData.length,
          totalWars: globalWarStats.totalWars || 0,
          ongoingElections: 0, // TODO: Add elections API when available
          totalPopulation: playersCount,
        });
      } catch (error) {
        console.error("Error fetching game statistics:", error);
        // Fallback to placeholder data on error
        setStats({
          totalPlayers: 15429,
          totalRegions: 247,
          totalStates: 89,
          activeWars: 12,
          totalWars: 1847,
          ongoingElections: 3,
          totalPopulation: 15429,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchGameStats();
  }, []);

  return (
    <View className="flex-1 bg-[#0a0c1b] relative">
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1, minHeight: '100%' }}
        style={{ flex: 1 }}
        nestedScrollEnabled={true}
        keyboardShouldPersistTaps="handled"
      >
        {/* Hexagon Background Pattern - Using View overlay */}
        <View className="absolute inset-0 overflow-hidden opacity-10" style={{ pointerEvents: 'none' }}>
          <View className="absolute inset-0 bg-neonBlue/5" />
        </View>

        {/* Hero Section */}
        <View className="relative">
          <View className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 sm:pt-16 md:pt-20 pb-20 sm:pb-24 md:pb-32">
            <View className="text-center relative items-center">
              {/* Targeting Reticle Animation */}
              <View className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-48 sm:w-64 h-48 sm:h-64" style={{ pointerEvents: 'none' }}>
                <View className="absolute inset-0 border-2 border-neonBlue/30 rounded-full animate-ping" />
                <View className="absolute inset-0 border border-neonBlue/20 rounded-full animate-pulse" />
              </View>

              {/* Logo with Sci-fi Frame */}
              <View className="relative inline-block mb-8 sm:mb-12 items-center">
                <View className="absolute -inset-4 border-2 border-neonBlue/30 rounded-lg transform -skew-x-12" />
                <View className="absolute -inset-4 bg-neonBlue/5 rounded-lg transform -skew-x-12" />
                <Image 
                  source={require('../../assets/images/wn-logo.png')}
                  style={{ width: 80, height: 80 }}
                  resizeMode="contain"
                />
                <View className="absolute top-0 left-0 w-2 h-2 bg-neonBlue animate-pulse" />
                <View className="absolute top-0 right-0 w-2 h-2 bg-neonBlue animate-pulse" />
                <View className="absolute bottom-0 left-0 w-2 h-2 bg-neonBlue animate-pulse" />
                <View className="absolute bottom-0 right-0 w-2 h-2 bg-neonBlue animate-pulse" />
              </View>

              {/* Title with Tech Effect */}
              <View className="mb-4 sm:mb-6 relative">
                <View className="relative">
                  <View className="absolute -inset-1 bg-neonBlue/20 blur-xl" />
                  <Text className="text-4xl sm:text-5xl md:text-6xl font-black text-white relative">
                    WARFRONT NATIONS
                  </Text>
                </View>
                <View className="absolute top-0 left-0 w-full h-full flex items-center justify-center opacity-50">
                  <View className="w-full h-1 bg-gradient-to-r from-transparent via-neonBlue to-transparent" />
                </View>
              </View>

              <Text className="text-lg text-gray-300 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed relative px-4">
                Enter a world of strategic warfare, political intrigue, and
                economic dominance. Build your empire, forge alliances, and
                conquer territories in this immersive multiplayer strategy
                game.
              </Text>

              {/* Game Features Tags */}
              <View className="flex flex-wrap justify-center gap-3 sm:gap-6 mb-8 sm:mb-12 px-4">
                <View className="relative group">
                  <View className="absolute -inset-1 bg-gradient-to-r from-neonBlue to-neonGreen blur-sm opacity-50 group-hover:opacity-100 transition-opacity" />
                  <View className="relative px-3 sm:px-6 py-2 bg-gray-900/90 flex-row items-center gap-2 rounded">
                    <Clock width={20} height={20} color="#3f87ff" />
                    <Text className="text-gray-300 text-sm sm:text-base">
                      REAL-TIME COMBAT
                    </Text>
                  </View>
                </View>
                <View className="relative group">
                  <View className="absolute -inset-1 bg-gradient-to-r from-red-500 to-orange-500 blur-sm opacity-50 group-hover:opacity-100 transition-opacity" />
                  <View className="relative px-6 py-2 bg-gray-900/90 flex-row items-center gap-2 rounded">
                    <Users width={20} height={20} color="#f87171" />
                    <Text className="text-gray-300">MULTIPLAYER WAR</Text>
                  </View>
                </View>
                <View className="relative group">
                  <View className="absolute -inset-1 bg-gradient-to-r from-yellow-500 to-amber-500 blur-sm opacity-50 group-hover:opacity-100 transition-opacity" />
                  <View className="relative px-6 py-2 bg-gray-900/90 flex-row items-center gap-2 rounded">
                    <Crown width={20} height={20} color="#facc15" />
                    <Text className="text-gray-300">POLITICAL POWER</Text>
                  </View>
                </View>
                <View className="relative group">
                  <View className="absolute -inset-1 bg-gradient-to-r from-purple-500 to-pink-500 blur-sm opacity-50 group-hover:opacity-100 transition-opacity" />
                  <View className="relative px-6 py-2 bg-gray-900/90 flex-row items-center gap-2 rounded">
                    <Sword width={20} height={20} color="#c084fc" />
                    <Text className="text-gray-300">EPIC BATTLES</Text>
                  </View>
                </View>
              </View>

              {/* CTA Buttons */}
              <View className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center px-4">
                <TouchableOpacity
                  className="w-full sm:w-auto group relative px-6 sm:px-8 py-3 sm:py-4 bg-neonBlue rounded min-w-[200px] overflow-hidden"
                  onPress={() => navigation.navigate('Register')}
                >
                  <View className="absolute inset-0 bg-gradient-to-r from-neonBlue via-white to-neonBlue opacity-50 blur-xl group-hover:animate-pulse" />
                  <Text className="relative font-bold tracking-wider text-base sm:text-lg text-white text-center">
                    ENLIST NOW
                  </Text>
                  <View className="absolute bottom-0 left-0 w-full h-1 bg-white/30 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform" />
                </TouchableOpacity>
                
                <TouchableOpacity
                  className="w-full sm:w-auto group relative px-6 sm:px-8 py-3 sm:py-4 bg-transparent border-2 border-neonBlue rounded min-w-[200px]"
                  onPress={() => navigation.navigate('Login')}
                >
                  <Text className="relative font-bold tracking-wider text-base sm:text-lg text-neonBlue group-hover:text-white transition-colors text-center">
                    RETURN TO BATTLE
                  </Text>
                  <View className="absolute inset-0 bg-neonBlue transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform -z-10" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        {/* Game Statistics Section */}
        <View className="relative py-12 sm:py-16 md:py-20 overflow-hidden">
          <View className="absolute inset-0 bg-gradient-to-b from-transparent via-neonBlue/5 to-transparent" />
          <View className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <View className="text-center mb-10 sm:mb-16 items-center">
              <Text className="text-3xl sm:text-4xl font-black text-white mb-4 tracking-wider">
                GLOBAL STATISTICS
              </Text>
              <View className="w-32 sm:w-40 h-1 bg-gradient-to-r from-transparent via-neonBlue to-transparent mx-auto" />
            </View>

            {loading ? (
              <View className="flex-1 justify-center items-center py-16">
                <ActivityIndicator size="large" color="#3f87ff" />
                <Text className="text-gray-300 mt-4">Loading battlefield statistics...</Text>
              </View>
            ) : (
              <View className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
                <View className="group relative">
                  <View className="absolute -inset-1 bg-neonBlue/20 blur-sm group-hover:bg-neonBlue/40 transition-colors" />
                  <View className="relative bg-gray-900/90 p-6 sm:p-8 rounded-lg transform group-hover:-translate-y-1 transition-transform items-center">
                    <Users width={48} height={48} color="#3f87ff" />
                    <Text className="text-2xl sm:text-3xl font-bold text-white mb-2 font-mono">
                      {(stats.totalPlayers || 0).toLocaleString()}
                    </Text>
                    <Text className="text-neonBlue font-bold tracking-wider text-sm sm:text-base">
                      ACTIVE PLAYERS
                    </Text>
                  </View>
                </View>

                <View className="group relative">
                  <View className="absolute -inset-1 bg-neonGreen/20 blur-sm group-hover:bg-neonGreen/40 transition-colors" />
                  <View className="relative bg-gray-900/90 p-6 sm:p-8 rounded-lg transform group-hover:-translate-y-1 transition-transform items-center">
                    <MapPin size={48} color="#00ff87" className="mb-4" />
                    <Text className="text-3xl font-bold text-white mb-2 font-mono">
                      {(stats.totalRegions || 0).toLocaleString()}
                    </Text>
                    <Text className="text-neonGreen font-bold tracking-wider">
                      REGIONS
                    </Text>
                  </View>
                </View>

                <View className="group relative">
                  <View className="absolute -inset-1 bg-yellow-400/20 blur-sm group-hover:bg-yellow-400/40 transition-colors" />
                  <View className="relative bg-gray-900/90 p-6 sm:p-8 rounded-lg transform group-hover:-translate-y-1 transition-transform items-center">
                    <Flag size={48} color="#facc15" className="mb-4" />
                    <Text className="text-3xl font-bold text-white mb-2 font-mono">
                      {(stats.totalStates || 0).toLocaleString()}
                    </Text>
                    <Text className="text-yellow-400 font-bold tracking-wider">
                      STATES
                    </Text>
                  </View>
                </View>

                <View className="group relative">
                  <View className="absolute -inset-1 bg-red-500/20 blur-sm group-hover:bg-red-500/40 transition-colors" />
                  <View className="relative bg-gray-900/90 p-6 sm:p-8 rounded-lg transform group-hover:-translate-y-1 transition-transform items-center">
                    <Sword size={48} color="#ef4444" className="mb-4" />
                    <Text className="text-3xl font-bold text-white mb-2 font-mono">
                      {(stats.activeWars || 0).toLocaleString()}
                    </Text>
                    <Text className="text-red-500 font-bold tracking-wider">
                      ACTIVE WARS
                    </Text>
                  </View>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Final CTA Section */}
        <View className="relative py-12 sm:py-16 md:py-20 overflow-hidden">
          <View className="absolute inset-0">
            <View className="absolute inset-0 bg-gradient-to-b from-transparent to-neonBlue/10" />
          </View>

          <View className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center items-center">
            <Text className="text-4xl sm:text-5xl font-black text-white mb-6 sm:mb-8 tracking-wider">
              YOUR LEGEND AWAITS
            </Text>
            <Text className="text-lg sm:text-xl text-gray-300 mb-8 sm:mb-12 leading-relaxed">
              Join the ranks of thousands of commanders in the ultimate battle
              for supremacy. Your path to glory begins now.
            </Text>
            <View className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center">
              <TouchableOpacity
                className="w-full sm:w-auto group relative px-8 sm:px-12 py-4 sm:py-5 bg-gradient-to-r from-neonBlue to-neonGreen rounded min-w-[200px] sm:min-w-[250px]"
                onPress={() => navigation.navigate('Register')}
              >
                <Text className="relative text-lg sm:text-xl font-black tracking-widest text-white text-center">
                  BEGIN CONQUEST
                </Text>
                <View className="absolute bottom-0 left-0 w-full h-1 bg-white/30 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform" />
              </TouchableOpacity>
              
              <TouchableOpacity
                className="w-full sm:w-auto group relative px-8 sm:px-12 py-4 sm:py-5 bg-transparent border-2 border-neonBlue rounded min-w-[200px] sm:min-w-[250px]"
                onPress={() => navigation.navigate('Login')}
              >
                <Text className="relative text-lg sm:text-xl font-black tracking-widest text-white text-center">
                  REJOIN BATTLE
                </Text>
                <View className="absolute inset-0 bg-neonBlue/20 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform -z-10" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Footer */}
        <View className="text-center py-8 border-t border-gray-800 items-center">
          <Text className="text-gray-400 mb-2">© 2024 Warfront Nations</Text>
          <Text className="text-gray-600 text-sm">Strategic warfare redefined</Text>
        </View>
      </ScrollView>
    </View>
  );
};

