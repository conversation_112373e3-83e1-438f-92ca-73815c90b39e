import { api } from './api';
import { Chat, Message } from '../types/chat';
import { chatWebSocketService } from './chatWebSocketService';

export interface ChatPaginationParams {
  limit?: number;
  cursor?: string;
}

export interface UserSearchResult {
  id: number;
  username: string;
  level?: number;
  avatar?: string;
}

export const chatService = {
  // Chat management
  getAllChats: async (params?: ChatPaginationParams): Promise<Chat[]> => {
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.cursor) queryParams.append('cursor', params.cursor);
    
    const response = await api.get(`/chats${queryParams.toString() ? '?' + queryParams.toString() : ''}`);
    return response.data;
  },

  createChat: async (participantIds: number[], name?: string, type: 'direct' | 'group' = 'direct'): Promise<Chat> => {
    const response = await api.post('/chats', { participantIds, name, type });
    return response.data;
  },

  getChat: async (chatId: string): Promise<Chat> => {
    const response = await api.get(`/chats/${chatId}`);
    return response.data;
  },

  updateChat: async (chatId: string, updates: { name?: string; description?: string }): Promise<Chat> => {
    const response = await api.patch(`/chats/${chatId}`, updates);
    return response.data;
  },

  leaveChat: async (chatId: string): Promise<void> => {
    await api.delete(`/chats/${chatId}/leave`);
  },

  // Message management
  getChatMessages: async (chatId: string, params?: ChatPaginationParams): Promise<Message[]> => {
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.cursor) queryParams.append('cursor', params.cursor);
    
    const response = await api.get(`/chats/${chatId}/messages${queryParams.toString() ? '?' + queryParams.toString() : ''}`);
    return response.data;
  },

  sendMessage: async (chatId: string, content: string): Promise<Message> => {
    // Try WebSocket first, fallback to HTTP
    const wsSuccess = chatWebSocketService.sendMessage(chatId, content);
    if (wsSuccess) {
      // Return optimistic message - actual message will come through WebSocket
      return {
        id: `temp-${Date.now()}`,
        content,
        chatId,
        sender: { id: 0, username: 'You' }, // Will be replaced by real message
        createdAt: new Date().toISOString(),
        isRead: false
      } as Message;
    }
    
    // Fallback to HTTP
    const response = await api.post(`/chats/${chatId}/messages`, { content });
    return response.data;
  },

  markAsRead: async (chatId: string): Promise<void> => {
    // Try WebSocket first, fallback to HTTP
    const wsSuccess = chatWebSocketService.markAsRead(chatId);
    if (!wsSuccess) {
      await api.put(`/chats/${chatId}/read`);
    }
  },

  // Participant management
  addParticipants: async (chatId: string, participantIds: number[]): Promise<Chat> => {
    const response = await api.post(`/chats/${chatId}/participants`, { participantIds });
    return response.data;
  },

  removeParticipants: async (chatId: string, participantIds: number[]): Promise<Chat> => {
    const response = await api.delete(`/chats/${chatId}/participants`, { data: { participantIds } });
    return response.data;
  },

  // Unread counts
  getUserUnreadCount: async (): Promise<number> => {
    const response = await api.get('/chats/user-unread-count');
    return response.data.count || 0;
  },

  // General chat endpoints
  getGeneralChatInfo: async () => {
    const response = await api.get('/chats/general/info');
    return response.data;
  },

  getGeneralChatMessages: async (params?: ChatPaginationParams): Promise<Message[]> => {
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.cursor) queryParams.append('cursor', params.cursor);
    
    const response = await api.get(`/chats/general/messages${queryParams.toString() ? '?' + queryParams.toString() : ''}`);
    return response.data;
  },

  sendGeneralChatMessage: async (content: string): Promise<Message> => {
    const response = await api.post('/chats/general/messages', { content });
    return response.data;
  },

  markGeneralChatAsRead: async (): Promise<void> => {
    await api.put('/chats/general/read');
  },

  getGeneralChatUnreadCount: async (): Promise<number> => {
    const response = await api.get('/chats/general/unread-count');
    return response.data.count || 0;
  },

  getGeneralChatUnreadCountAlt: async (): Promise<number> => {
    const response = await api.get('/chats/general-chat-unread-count');
    return response.data.count || 0;
  },

  // User search for chat creation
  searchUsers: async (query: string, limit = 20): Promise<UserSearchResult[]> => {
    const response = await api.get(`/api/users/search?q=${encodeURIComponent(query)}&limit=${limit}`);
    return response.data;
  },

  // WebSocket connection management
  connectWebSocket: () => chatWebSocketService.connect(),
  disconnectWebSocket: () => chatWebSocketService.disconnect(),
  isWebSocketConnected: () => chatWebSocketService.isSocketConnected(),
  setWebSocketCallbacks: (callbacks: any) => chatWebSocketService.setCallbacks(callbacks),
  
  // Typing indicators
  sendTypingStart: (chatId: string) => chatWebSocketService.sendTypingStart(chatId),
  sendTypingStop: (chatId: string) => chatWebSocketService.sendTypingStop(chatId),
  getTypingUsers: (chatId: string) => chatWebSocketService.getTypingUsers(chatId),
};