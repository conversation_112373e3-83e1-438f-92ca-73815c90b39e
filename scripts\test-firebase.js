#!/usr/bin/env node

/**
 * Firebase Test Script
 * Tests Firebase integration before deployment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔥 Testing Firebase Integration...\n');

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description} - Found`, 'green');
    return true;
  } else {
    log(`❌ ${description} - Missing: ${filePath}`, 'red');
    return false;
  }
}

function checkPackage(packageName) {
  try {
    require.resolve(packageName);
    log(`✅ Package ${packageName} - Installed`, 'green');
    return true;
  } catch (e) {
    log(`❌ Package ${packageName} - Not installed`, 'red');
    return false;
  }
}

// Test 1: Check required files
log('📁 Checking required files...', 'blue');
const projectRoot = path.join(__dirname, '..');

const requiredFiles = [
  { path: path.join(projectRoot, 'android/app/build.gradle'), desc: 'Android build.gradle' },
  { path: path.join(projectRoot, 'android/build.gradle'), desc: 'Root build.gradle' },
  { path: path.join(projectRoot, '.env'), desc: 'Environment file' },
  { path: path.join(projectRoot, 'src/services/analyticsService.ts'), desc: 'Analytics service' },
  { path: path.join(projectRoot, 'src/services/pushNotificationService.ts'), desc: 'Push notification service' },
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  if (!checkFile(file.path, file.desc)) {
    allFilesExist = false;
  }
});

console.log('');

// Test 2: Check Firebase packages
log('📦 Checking Firebase packages...', 'blue');
const firebasePackages = [
  '@react-native-firebase/app',
  '@react-native-firebase/messaging',
  '@react-native-firebase/analytics',
  '@react-native-firebase/crashlytics',
];

let allPackagesInstalled = true;
firebasePackages.forEach(pkg => {
  if (!checkPackage(pkg)) {
    allPackagesInstalled = false;
  }
});

console.log('');

// Test 3: Check build.gradle configuration
log('🔧 Checking Android configuration...', 'blue');
const buildGradlePath = path.join(projectRoot, 'android/app/build.gradle');
if (fs.existsSync(buildGradlePath)) {
  const buildGradleContent = fs.readFileSync(buildGradlePath, 'utf8');
  
  const checks = [
    { pattern: 'com.google.gms.google-services', desc: 'Google Services plugin' },
    { pattern: 'com.google.firebase.crashlytics', desc: 'Crashlytics plugin' },
    { pattern: 'firebase-analytics', desc: 'Analytics dependency' },
    { pattern: 'firebase-messaging', desc: 'Messaging dependency' },
  ];
  
  checks.forEach(check => {
    if (buildGradleContent.includes(check.pattern)) {
      log(`✅ ${check.desc} - Configured`, 'green');
    } else {
      log(`❌ ${check.desc} - Missing`, 'red');
      allFilesExist = false;
    }
  });
} else {
  log('❌ Cannot check build.gradle - file not found', 'red');
  allFilesExist = false;
}

console.log('');

// Test 4: Check environment configuration
log('🌍 Checking environment configuration...', 'blue');
const envPath = path.join(projectRoot, '.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  const envChecks = [
    'API_BASE_URL',
    'FIREBASE_PROJECT_ID',
    'ENABLE_CRASHLYTICS',
    'ENABLE_ANALYTICS',
  ];
  
  envChecks.forEach(envVar => {
    if (envContent.includes(envVar)) {
      log(`✅ ${envVar} - Configured`, 'green');
    } else {
      log(`❌ ${envVar} - Missing from .env`, 'red');
    }
  });
} else {
  log('❌ .env file not found', 'red');
}

console.log('');

// Test 5: Check for google-services.json placeholder
log('📱 Checking Firebase configuration files...', 'blue');
const googleServicesPath = path.join(projectRoot, 'android/app/google-services.json');
if (fs.existsSync(googleServicesPath)) {
  log('✅ google-services.json found', 'green');
} else {
  log('⚠️  google-services.json not found - You need to download this from Firebase Console', 'yellow');
  log('   1. Go to Firebase Console → Project Settings', 'yellow');
  log('   2. Download google-services.json for your Android app', 'yellow');
  log('   3. Place it in android/app/google-services.json', 'yellow');
}

console.log('');

// Test 6: Try building the project
log('🔨 Testing build configuration...', 'blue');
try {
  execSync('cd android && ./gradlew tasks --quiet', { stdio: 'pipe' });
  log('✅ Android Gradle build system - Working', 'green');
} catch (error) {
  log('❌ Android Gradle build system - Error', 'red');
  log('   Run: cd android && ./gradlew clean', 'yellow');
}

// Final summary
console.log('');
log('📋 Firebase Integration Test Summary', 'blue');
console.log(''.padEnd(50, '='));

if (allFilesExist && allPackagesInstalled) {
  log('🎉 All checks passed! Firebase is properly configured.', 'green');
  console.log('');
  log('Next steps:', 'blue');
  log('1. Create Firebase projects (dev, staging, prod)', 'yellow');
  log('2. Download google-services.json files', 'yellow');
  log('3. Update .env files with actual values', 'yellow');
  log('4. Test push notifications', 'yellow');
  log('5. Build and deploy!', 'yellow');
} else {
  log('❌ Some issues found. Please fix them before proceeding.', 'red');
  console.log('');
  log('Run this script again after fixing the issues.', 'yellow');
}

console.log('');