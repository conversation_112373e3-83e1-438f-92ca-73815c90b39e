import React, { useState, useEffect } from 'react';
import { View, Text, ActivityIndicator, TouchableOpacity, ScrollView } from 'react-native';
import { Clock, Sword, Trophy, X, Plus, Package, Bell } from 'lucide-react-native';
import { warService } from '../services/warService';
import { showErrorToast } from '../utils/toastUtils';

interface WarTimelineEvent {
  warId: string;
  warName: string;
  eventType: string;
  description: string;
  timestamp: string;
  participants?: any[];
}

interface Props {
  limit?: number;
  onWarPress?: (warId: string) => void;
}

export const WarTimeline: React.FC<Props> = ({ limit = 10, onWarPress }) => {
  const [events, setEvents] = useState<WarTimelineEvent[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTimeline = async () => {
      try {
        setLoading(true);
        const data = await warService.getWarTimeline(limit);
        setEvents(data);
      } catch (error) {
        console.error('Failed to fetch war timeline:', error);
        showErrorToast('Failed to load war timeline');
      } finally {
        setLoading(false);
      }
    };

    fetchTimeline();
  }, [limit]);

  const getEventIcon = (eventType: string) => {
    switch (eventType.toLowerCase()) {
      case 'declaration':
        return <Bell width={16} height={16} color="#3f87ff" />;
      case 'battle':
        return <Sword width={16} height={16} color="#ef4444" />;
      case 'victory':
        return <Trophy width={16} height={16} color="#10b981" />;
      case 'defeat':
        return <X width={16} height={16} color="#ef4444" />;
      case 'peace':
        return <Clock width={16} height={16} color="#6b7280" />;
      case 'join':
        return <Plus width={16} height={16} color="#3f87ff" />;
      case 'supply':
        return <Package width={16} height={16} color="#f59e0b" />;
      default:
        return <Bell width={16} height={16} color="#6b7280" />;
    }
  };

  const getEventEmoji = (eventType: string) => {
    switch (eventType.toLowerCase()) {
      case 'declaration': return '📜';
      case 'battle': return '⚔️';
      case 'victory': return '🏆';
      case 'defeat': return '❌';
      case 'peace': return '🕊️';
      case 'join': return '➕';
      case 'supply': return '📦';
      default: return '🔔';
    }
  };

  if (loading) {
    return (
      <View className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <View className="flex-row items-center justify-center">
          <ActivityIndicator size="large" color="#3f87ff" />
          <Text className="text-gray-300 ml-3">Loading war timeline...</Text>
        </View>
      </View>
    );
  }

  if (events.length === 0) {
    return (
      <View className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <Text className="text-xl font-bold text-white mb-4">War Timeline</Text>
        <Text className="text-gray-400 text-center">No recent war events</Text>
      </View>
    );
  }

  return (
    <View className="bg-gray-800 rounded-xl p-6 border border-gray-700">
      <Text className="text-xl font-bold text-white mb-4">War Timeline</Text>
      
      <ScrollView className="max-h-96" showsVerticalScrollIndicator={false}>
        <View className="relative">
          {/* Timeline line */}
          <View className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-600" />
          
          {/* Timeline events */}
          <View className="space-y-6">
            {events.map((event, index) => (
              <View key={index} className="relative pl-14">
                {/* Event icon */}
                <View className="absolute left-0 w-12 h-12 flex items-center justify-center bg-gray-700 rounded-full border-4 border-gray-800 z-10">
                  <Text className="text-xl">{getEventEmoji(event.eventType)}</Text>
                </View>
                
                {/* Event content */}
                <TouchableOpacity
                  className="bg-gray-700 p-4 rounded-lg"
                  onPress={() => onWarPress?.(event.warId)}
                >
                  <View className="flex-row justify-between items-start mb-2">
                    <Text className="text-lg font-medium text-neonBlue flex-1">
                      {event.warName}
                    </Text>
                    <Text className="text-xs text-gray-400 ml-2">
                      {new Date(event.timestamp).toLocaleString()}
                    </Text>
                  </View>
                  <Text className="text-gray-300">{event.description}</Text>
                  
                  {event.participants && event.participants.length > 0 && (
                    <View className="mt-2 flex-row flex-wrap">
                      {event.participants.slice(0, 3).map((participant, pIndex) => (
                        <Text key={pIndex} className="text-xs text-gray-400 mr-2">
                          {participant.username}
                        </Text>
                      ))}
                      {event.participants.length > 3 && (
                        <Text className="text-xs text-gray-400">
                          +{event.participants.length - 3} more
                        </Text>
                      )}
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};
