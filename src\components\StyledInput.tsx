import React from 'react';
import { TextInput, TextInputProps, ViewStyle, TextStyle, TouchableOpacity, Text } from 'react-native';
import { theme } from '../styles/theme';

interface StyledInputProps extends TextInputProps {
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
}

export const StyledInput: React.FC<StyledInputProps> = ({
  containerStyle,
  inputStyle,
  placeholderTextColor = '#777',
  ...props
}) => {
  const defaultStyle: TextStyle = {
    width: '100%',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: '#1C1C1E',
    color: '#ffffff',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    fontSize: 16,
    ...inputStyle,
  };

  return (
    <TextInput
      placeholderTextColor={placeholderTextColor}
      style={defaultStyle}
      {...props}
    />
  );
};

interface StyledButtonProps {
  onPress: () => void;
  title: string;
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const StyledButton: React.FC<StyledButtonProps> = ({
  onPress,
  title,
  disabled = false,
  loading = false,
  style,
  textStyle,
}) => {
  const defaultButtonStyle: ViewStyle = {
    width: '100%',
    backgroundColor: '#1C1C1E',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 12,
    borderRadius: 12,
    opacity: disabled || loading ? 0.6 : 1,
    ...style,
  };

  const defaultTextStyle: TextStyle = {
    color: '#ffffff',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
    ...textStyle,
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      style={defaultButtonStyle}
    >
      <Text style={defaultTextStyle}>
        {loading ? 'Loading...' : title}
      </Text>
    </TouchableOpacity>
  );
};
