import { api } from './api';

export interface Region {
  id: string;
  name: string;
  code?: string;
  countryCode?: string;
  population?: number;
  stateId?: string;
  state?: {
    id: string;
    name: string;
    flag?: string;
  };
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  createdAt: string;
  updatedAt: string;
}

export const regionService = {
  // Region management
  getAllRegions: async (): Promise<Region[]> => {
    const response = await api.get('/regions');
    return response.data;
  },

  getAllRegionsCount: async (): Promise<number> => {
    const response = await api.get('/regions/count');
    return response.data;
  },

  getRegionById: async (regionId: string): Promise<Region> => {
    const response = await api.get(`/regions/${regionId}`);
    return response.data;
  },

  searchRegions: async (name: string): Promise<Region[]> => {
    const response = await api.get(`/regions/search?name=${encodeURIComponent(name)}`);
    return response.data;
  },
};