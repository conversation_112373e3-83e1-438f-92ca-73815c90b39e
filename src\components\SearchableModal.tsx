import React, { useState, useMemo } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { X, Search, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react-native';
import { SearchableModalProps, SearchableItem, SortConfig } from '../types/searchableModal';

const SearchableModal: React.FC<SearchableModalProps> = ({
  isOpen,
  onClose,
  title,
  icon: Icon,
  data = [],
  loading = false,
  onItemClick,
  renderItem,
  searchPlaceholder = "Search...",
  searchFilter,
  sortOptions = [],
  defaultSort,
  sortFunction
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [sortConfig, setSortConfig] = useState<SortConfig>(
    defaultSort || { field: '', direction: 'asc' }
  );

  const defaultSearchFilter = (items: SearchableItem[], term: string): SearchableItem[] => {
    if (!term.trim()) return items;
    
    const searchLower = term.toLowerCase();
    return items.filter(item => {
      const searchableFields = [
        item.name,
        item.username,
        item.title,
        (item as any).state?.name,
        (item as any).region?.name,
        item.location,
        item.description
      ];
      
      return searchableFields.some(field => 
        field && field.toString().toLowerCase().includes(searchLower)
      );
    });
  };

  const defaultSortFunction = (items: SearchableItem[], config: SortConfig): SearchableItem[] => {
    if (!config.field) return items;

    return [...items].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      // Get values based on the field
      switch (config.field) {
        case 'name':
          aValue = a.name || a.username || a.title || '';
          bValue = b.name || b.username || b.title || '';
          break;
        case 'username':
          aValue = a.username || '';
          bValue = b.username || '';
          break;
        case 'level':
          aValue = (a as any).level || 0;
          bValue = (b as any).level || 0;
          break;
        case 'population':
          aValue = (a as any).population || 0;
          bValue = (b as any).population || 0;
          break;
        case 'location':
          aValue = a.location || '';
          bValue = b.location || '';
          break;
        default:
          // Use custom getValue function if available
          const sortOption = sortOptions.find(opt => opt.key === config.field);
          if (sortOption?.getValue) {
            aValue = sortOption.getValue(a);
            bValue = sortOption.getValue(b);
          } else {
            aValue = (a as any)[config.field] || '';
            bValue = (b as any)[config.field] || '';
          }
      }

      // Handle different data types
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
        return config.direction === 'asc' ? comparison : -comparison;
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        return config.direction === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        // Convert to string for comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        const comparison = aStr.localeCompare(bStr);
        return config.direction === 'asc' ? comparison : -comparison;
      }
    });
  };

  const filteredData = useMemo(() => {
    const filterFunction = searchFilter || defaultSearchFilter;
    const sortingFunction = sortFunction || defaultSortFunction;
    
    // First filter the data
    const filtered = filterFunction(data, searchTerm);
    
    // Then sort the filtered data
    return sortingFunction(filtered, sortConfig);
  }, [data, searchTerm, sortConfig, searchFilter, sortFunction, sortOptions]);

  const handleItemClick = (item: SearchableItem) => {
    if (onItemClick) {
      onItemClick(item);
    }
  };

  const handleClose = () => {
    setSearchTerm('');
    setSortConfig(defaultSort || { field: '', direction: 'asc' });
    onClose();
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Get sort icon for a field
  const getSortIcon = (field: string) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown width={16} height={16} />;
    }
    return sortConfig.direction === 'asc' ? 
      <ArrowUp width={16} height={16} /> : 
      <ArrowDown width={16} height={16} />;
  };

  const renderDefaultItem = (item: SearchableItem) => (
    <View>
      <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginBottom: 4 }}>
        {item.name || item.username || item.title || 'Unknown'}
      </Text>
      <Text style={{ fontSize: 14, color: '#9CA3AF' }}>
        {item.description || item.location || 'No description available'}
      </Text>
    </View>
  );

  const renderFlatListItem = ({ item }: { item: SearchableItem }) => (
    <TouchableOpacity
      style={{
        backgroundColor: '#1F2937',
        borderRadius: 8,
        padding: 16,
        marginBottom: 12,
        borderWidth: 2,
        borderColor: 'transparent',
      }}
      onPress={() => handleItemClick(item)}
      activeOpacity={0.7}
    >
      {renderItem ? renderItem(item) : renderDefaultItem(item)}
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={isOpen}
      animationType="slide"
      transparent={true}
      onRequestClose={handleClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.75)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
      }}>
        <View style={{
          backgroundColor: '#111827',
          borderRadius: 12,
          width: '100%',
          maxWidth: 500,
          height: '90%',
          overflow: 'hidden',
          flex: 0,
        }}>
          {/* Header */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: 24,
            borderBottomWidth: 1,
            borderBottomColor: '#374151',
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
              {Icon && <Icon width={28} height={28} color="#3f87ff" />}
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#ffffff',
                marginLeft: Icon ? 12 : 0,
              }}>
                {title}
              </Text>
            </View>
            <TouchableOpacity
              onPress={handleClose}
              style={{
                padding: 8,
                borderRadius: 8,
                backgroundColor: '#374151',
              }}
            >
              <X width={24} height={24} color="#9CA3AF" />
            </TouchableOpacity>
          </View>

          {/* Search Bar */}
          <View style={{
            padding: 24,
            borderBottomWidth: 1,
            borderBottomColor: '#374151',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#1F2937',
              borderRadius: 8,
              borderWidth: 1,
              borderColor: '#4B5563',
              paddingHorizontal: 12,
            }}>
              <Search width={20} height={20} color="#9CA3AF" />
              <TextInput
                style={{
                  flex: 1,
                  paddingVertical: 12,
                  paddingHorizontal: 12,
                  color: '#ffffff',
                  fontSize: 16,
                }}
                placeholder={searchPlaceholder}
                placeholderTextColor="#9CA3AF"
                value={searchTerm}
                onChangeText={setSearchTerm}
              />
            </View>
            {searchTerm && (
              <Text style={{
                marginTop: 8,
                fontSize: 14,
                color: '#9CA3AF',
              }}>
                {filteredData.length} result{filteredData.length !== 1 ? 's' : ''} found
              </Text>
            )}

            {/* Sort Controls */}
            {sortOptions.length > 0 && (
              <View style={{
                marginTop: 12,
              }}>
                <Text style={{ color: '#9CA3AF', fontSize: 14, fontWeight: '500', marginBottom: 8 }}>
                  Sort by:
                </Text>
                <View style={{ flexDirection: 'row', flexWrap: 'wrap', alignItems: 'center' }}>
                  {sortOptions.map((option) => (
                    <TouchableOpacity
                      key={option.key}
                      onPress={() => handleSortChange(option.key)}
                      style={{
                        paddingHorizontal: 12,
                        paddingVertical: 6,
                        marginRight: 8,
                        marginBottom: 4,
                        borderRadius: 6,
                        backgroundColor: sortConfig.field === option.key ? '#3f87ff' : '#374151',
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderWidth: 1,
                        borderColor: sortConfig.field === option.key ? '#3f87ff' : 'transparent',
                      }}
                    >
                      <Text style={{
                        color: sortConfig.field === option.key ? '#ffffff' : '#9CA3AF',
                        fontSize: 13,
                        fontWeight: '500',
                        marginRight: 4,
                      }}>
                        {option.label}
                      </Text>
                      {getSortIcon(option.key)}
                    </TouchableOpacity>
                  ))}
                  {sortConfig.field && (
                    <TouchableOpacity
                      onPress={() => setSortConfig({ field: '', direction: 'asc' })}
                      style={{
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                        marginRight: 8,
                        marginBottom: 4,
                      }}
                    >
                      <Text style={{
                        color: '#9CA3AF',
                        fontSize: 12,
                        textDecorationLine: 'underline',
                      }}>
                        Clear sort
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            )}
          </View>

          {/* Content */}
          <View style={{ flex: 1, padding: 24 }}>
            {loading ? (
              <View style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 48,
              }}>
                <ActivityIndicator size="large" color="#3f87ff" />
                <Text style={{
                  color: '#3f87ff',
                  fontSize: 18,
                  marginTop: 16,
                }}>
                  Loading...
                </Text>
              </View>
            ) : filteredData.length > 0 ? (
              <FlatList
                data={filteredData}
                renderItem={renderFlatListItem}
                keyExtractor={(item) => item.id.toString()}
                showsVerticalScrollIndicator={false}
                style={{ flex: 1 }}
                contentContainerStyle={{ flexGrow: 1 }}
              />
            ) : (
              <View style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 48,
              }}>
                <Text style={{
                  color: '#9CA3AF',
                  fontSize: 18,
                  marginBottom: 8,
                  textAlign: 'center',
                }}>
                  {searchTerm ? 'No results found' : 'No data available'}
                </Text>
                {searchTerm && (
                  <Text style={{
                    color: '#6B7280',
                    fontSize: 14,
                    textAlign: 'center',
                  }}>
                    Try adjusting your search terms
                  </Text>
                )}
              </View>
            )}
          </View>

          {/* Footer */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 24,
            paddingVertical: 16,
            borderTopWidth: 1,
            borderTopColor: '#374151',
            backgroundColor: '#0F172A',
          }}>
            <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
              Showing {filteredData.length} of {data.length} items
              {searchTerm ? ` for "${searchTerm}"` : ''}
              {sortConfig.field ? ` • Sorted by ${sortOptions.find(opt => opt.key === sortConfig.field)?.label || sortConfig.field} (${sortConfig.direction})` : ''}
            </Text>
            <TouchableOpacity
              onPress={handleClose}
              style={{
                backgroundColor: '#374151',
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 6,
              }}
            >
              <Text style={{ color: '#ffffff', fontSize: 14 }}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default SearchableModal;