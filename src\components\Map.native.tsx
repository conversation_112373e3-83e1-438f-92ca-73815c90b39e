import React from 'react';
import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';
import MapView, { PROVIDER_GOOGLE, Marker, Polygon } from 'react-native-maps';

interface MapProps {
  onRegionSelect?: (region: any) => void;
  onStateSelect?: (state: any) => void;
  onRegionCheck?: (regionId: string) => void;
  mapMode?: 'political' | 'wars' | 'targets';
  wars?: any[];
  availableTargets?: any[];
  showControls?: boolean;
  interactive?: boolean;
  height?: number;
  states?: any[];
  geoJsonData?: any;
  loading?: boolean;
  mapReady?: boolean;
  mapRegion?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  onMapReady?: () => void;
  onRegionChangeComplete?: (region: any) => void;
  convertGeoJSONToPolygons?: () => React.ReactNode[];
}

export const Map: React.FC<MapProps> = ({
  onRegionSelect,
  onStateSelect,
  onRegionCheck,
  mapMode = 'political',
  wars = [],
  availableTargets = [],
  showControls = true,
  interactive = true,
  height = 300,
  states = [],
  geoJsonData,
  loading = false,
  mapReady = false,
  mapRegion = {
    latitude: 20,
    longitude: 0,
    latitudeDelta: 100,
    longitudeDelta: 100,
  },
  onMapReady,
  onRegionChangeComplete,
  convertGeoJSONToPolygons,
}) => {
  if (loading) {
    return (
      <View style={[styles.container, { height }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3f87ff" />
          <Text style={styles.loadingText}>Loading world map...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { height }]}>
      <MapView
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        region={mapRegion}
        onRegionChangeComplete={onRegionChangeComplete}
        onMapReady={onMapReady}
        showsUserLocation={false}
        showsMyLocationButton={false}
        showsCompass={false}
        scrollEnabled={interactive}
        zoomEnabled={interactive}
        pitchEnabled={false}
        rotateEnabled={false}
        mapType="none" // Use none to show our custom styling
      >
        {convertGeoJSONToPolygons && convertGeoJSONToPolygons()}
        
        {/* War markers */}
        {mapMode === 'wars' && wars.map((war, index) => {
          const attacker = states.find(s => s.id === war.attackerId)?.regions?.[0];
          const defender = states.find(s => s.id === war.defenderId)?.regions?.[0];
          
          if (attacker?.coordinates && defender?.coordinates) {
            return (
              <Marker
                key={`war-${index}`}
                coordinate={{
                  latitude: (attacker.coordinates.lat + defender.coordinates.lat) / 2,
                  longitude: (attacker.coordinates.lng + defender.coordinates.lng) / 2,
                }}
                title={`War: ${war.name || 'Unnamed War'}`}
                description={`${attacker.name} vs ${defender.name}`}
              />
            );
          }
          return null;
        })}
      </MapView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1f2937',
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1f2937',
  },
  loadingText: {
    color: '#9ca3af',
    marginTop: 10,
    fontSize: 16,
  },
});

// Export Polygon for use in GeographicalMap
export { Polygon };
