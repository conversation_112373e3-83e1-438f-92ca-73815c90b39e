/**
 * Token validation utilities for React Native
 */

/**
 * Safely decode base64 string, handling special characters
 */
const safeBase64Decode = (str: string): string => {
  // Try multiple approaches for robust decoding
  const attempts = [
    // 1. Try original string without padding
    () => atob(str.replace(/=+$/, '')),

    // 2. Try with proper JWT padding
    () => {
      const base = str.replace(/=+$/, '');
      const padded = base + '='.repeat((4 - base.length % 4) % 4);
      return atob(padded);
    },

    // 3. Try original string as-is
    () => atob(str),

    // 4. Try with URL-safe base64 conversion
    () => {
      const urlSafeConverted = str.replace(/-/g, '+').replace(/_/g, '/');
      const base = urlSafeConverted.replace(/=+$/, '');
      const padded = base + '='.repeat((4 - base.length % 4) % 4);
      return atob(padded);
    }
  ];

  for (const attempt of attempts) {
    try {
      return attempt();
    } catch (error) {
      // Continue to next attempt
      continue;
    }
  }

  // If all attempts failed, throw error
  throw new Error('Base64 decode failed');
};

/**
 * Decode JWT payload with proper error handling
 */
export const decodeJWTPayload = (token: string): any => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error(`Invalid JWT structure: expected 3 parts, got ${parts.length}`);
    }

    const [, payloadBase64] = parts;
    const decodedPayload = safeBase64Decode(payloadBase64);
    const payload = JSON.parse(decodedPayload);

    return payload;
  } catch (error) {
    return null;
  }
};

/**
 * Validate JWT token expiration
 */
export const isTokenNotExpired = (payload: any): boolean => {
  if (!payload || !payload.exp) {
    return false;
  }
  
  const now = Math.floor(Date.now() / 1000);
  return payload.exp > now;
};

/**
 * Check if token is valid
 */
export const isTokenValid = (token: string | null): boolean => {
  if (!token) {
    return false;
  }

  try {
    // Use the robust decoder that handles special characters
    const payload = decodeJWTPayload(token);

    if (!payload) {
      return false;
    }

    // Check if token is not expired
    return isTokenNotExpired(payload);
  } catch (error) {
    return false;
  }
};