import { api } from './api';
import {
  TaxConfiguration,
  BudgetSummaryResponse,
  BudgetTransactionsPaginatedResponse,
  UpdateTaxConfigurationDto,
  GetBudgetTransactionsDto,
  TaxCalculationResult
} from '../types/budget';

export const budgetService = {
  // Tax Configuration Management
  getTaxConfiguration: async (stateId: string): Promise<TaxConfiguration> => {
    const response = await api.get(`/state-budget/${stateId}/config`);
    return response.data;
  },

  updateTaxConfiguration: async (
    stateId: string,
    config: UpdateTaxConfigurationDto
  ): Promise<TaxConfiguration> => {
    const response = await api.put(`/state-budget/${stateId}/config`, config);
    return response.data;
  },

  // Budget Summary and Overview
  getBudgetSummary: async (stateId: string): Promise<BudgetSummaryResponse> => {
    const response = await api.get(`/state-budget/${stateId}/summary`);
    return response.data;
  },

  // Budget Transactions
  getBudgetTransactions: async (
    stateId: string,
    params?: GetBudgetTransactionsDto
  ): Promise<BudgetTransactionsPaginatedResponse> => {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.category) queryParams.append('category', params.category);
    if (params?.type) queryParams.append('type', params.type);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.relatedUserId) queryParams.append('relatedUserId', params.relatedUserId.toString());

    const response = await api.get(
      `/state-budget/${stateId}/transactions?${queryParams.toString()}`
    );
    return response.data;
  },

  // Tax Calculations (for preview and validation)
  calculateIncomeTax: async (
    stateId: string,
    income: number
  ): Promise<TaxCalculationResult> => {
    const response = await api.post(`/state-budget/${stateId}/calculate-income-tax`, { income });
    return response.data;
  },

  calculateTravelTax: async (
    stateId: string,
    distance: number,
    travelMode: 'regular' | 'speed'
  ): Promise<TaxCalculationResult> => {
    const response = await api.post(`/state-budget/${stateId}/calculate-travel-tax`, {
      distance,
      travelMode
    });
    return response.data;
  },

  // Public tax information (no authentication required)
  getPublicTaxRates: async (stateId: string): Promise<{
    incomeTaxRate: number;
    borderCrossingTaxRate: number;
    lastUpdated: Date;
  }> => {
    const response = await api.get(`/public/state-tax/${stateId}/rates`);
    return response.data;
  },

  // Donation to state budget
  donateToState: async (stateId: string, amount: number): Promise<{
    success: boolean;
    message: string;
    newTreasury: number;
  }> => {
    const response = await api.post(`/state-budget/${stateId}/donate`, { amount });
    return response.data;
  }
};

export default budgetService;