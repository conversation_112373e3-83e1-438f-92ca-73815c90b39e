# Warfront Nations Mobile

React Native mobile application for the Warfront Nations game.

## Project Status

✅ **Phase 1 - Foundation (Completed)**
- React Native project initialization
- Basic project structure and navigation
- Shared types and utilities migration
- API services and state management setup
- Authentication screens (Login, Register)
- Core screens (Home, Profile)
- Basic styling system

## Setup Instructions

### Prerequisites
- Node.js 18+
- React Native development environment
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. Navigate to the mobile project directory:
```bash
cd warfront-nations-mobile
```

2. Install dependencies:
```bash
npm install
```

3. For iOS (macOS only):
```bash
cd ios && pod install && cd ..
```

### Running the Application

#### Android
```bash
npm run android
```

#### iOS (macOS only)
```bash
npm run ios
```

#### Start Metro Bundler
```bash
npm start
```

## Project Structure

```
src/
├── navigation/          # Navigation configuration
├── screens/            # Screen components
├── components/         # Reusable components
├── services/           # API services
├── store/              # State management (Zustand)
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── styles/             # Styling and themes
```

## Features Implemented

### 🔐 Authentication
- Login screen with email/password
- Registration with validation
- Token-based authentication with AsyncStorage
- Auto-logout on token expiration

### 📱 Navigation
- Stack navigation for auth flow
- Bottom tab navigation for main app
- Proper screen transitions and headers

### 🏠 Core Screens
- **Home**: Dashboard with user stats and quick actions
- **Profile**: Detailed user information and character stats
- **Map Placeholder**: Placeholder for future map implementation

### 🎨 UI/UX
- Dark theme consistent with web version
- Responsive design for various screen sizes
- Toast notifications for user feedback
- Loading states and error handling

### 📊 State Management
- Zustand for global state
- Authentication state management
- AsyncStorage for token persistence

## Next Steps - Phase 2

### High Priority
- [ ] **Map Implementation**
  - Install react-native-maps or Mapbox
  - Create interactive world map
  - Implement GeoJSON rendering
  - Add state territory visualization

- [ ] **Chat System**
  - Real-time messaging
  - WebSocket integration
  - Chat rooms and private messages

- [ ] **War System**
  - War participation interface
  - Battle screens
  - War statistics and history

### Medium Priority
- [ ] **Travel System**
  - Travel planning interface
  - Travel status tracking
  - Permission requests

- [ ] **Factory Management**
  - Factory listing and details
  - Work session interface
  - Resource management

- [ ] **Elections**
  - Voting interface
  - Candidate information
  - Election results

### Low Priority
- [ ] **Push Notifications**
- [ ] **Offline Mode**
- [ ] **Performance Optimization**
- [ ] **Advanced Animations**

## API Integration

The mobile app connects to the same backend API as the web application. API configuration is in `src/services/api.ts`.

### Environment Configuration
- Development: `http://localhost:3000`
- Production: Update `API_URL` in `src/services/api.ts`

## Dependencies

### Core
- React Native 0.81
- React 19.1
- TypeScript

### Navigation
- @react-navigation/native
- @react-navigation/stack
- @react-navigation/bottom-tabs

### State & API
- Zustand (state management)
- Axios (HTTP client)
- AsyncStorage (local storage)

### UI
- react-native-toast-message
- react-native-gesture-handler
- react-native-screens
- react-native-safe-area-context

## Troubleshooting

### Common Issues

1. **Metro bundler cache issues**:
```bash
npx react-native start --reset-cache
```

2. **Android build issues**:
```bash
cd android && ./gradlew clean && cd ..
```

3. **iOS build issues**:
```bash
cd ios && pod install && cd ..
```

4. **Node modules issues**:
```bash
rm -rf node_modules && npm install
```