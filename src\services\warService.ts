import { api } from './api';
import { War, CreateWarDto, ParticipateInWarDto } from '../types/war';

export interface WarPaginationParams {
  page?: number;
  limit?: number;
  stateId?: string;
}

export interface WarAnalytics {
  totalWars: number;
  activeWars: number;
  userParticipation: number;
  damageDealt: number;
  warsWon: number;
  warsLost: number;
}

export interface WarEvent {
  id: string;
  warId: string;
  type: string;
  description: string;
  participants: any[];
  timestamp: string;
}

export interface WarSupply {
  id: string;
  warId: string;
  resourceType: string;
  amount: number;
  contributedBy: number;
  createdAt: string;
}

export interface WarMorale {
  warId: string;
  attackerMorale: number;
  defenderMorale: number;
  factors: string[];
  lastUpdated: string;
}

export interface WarCosts {
  warId: string;
  preparationCost: number;
  maintenanceCost: number;
  supplyCost: number;
  totalCost: number;
}

export const warService = {
  // War management
  getAllWars: async (params?: WarPaginationParams): Promise<War[]> => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    
    const response = await api.get(`/wars${queryParams.toString() ? '?' + queryParams.toString() : ''}`);
    return response.data;
  },

  getWarsByState: async (stateId: string): Promise<War[]> => {
    const response = await api.get(`/wars/state/${stateId}`);
    return response.data;
  },

  getActiveWars: async (): Promise<War[]> => {
    const response = await api.get('/wars/active');
    return response.data;
  },

  getMyWars: async (): Promise<War[]> => {
    const response = await api.get('/wars/my-wars');
    return response.data;
  },

  getWar: async (id: string): Promise<War> => {
    const response = await api.get(`/wars/${id}`);
    return response.data;
  },

  declareWar: async (warData: CreateWarDto): Promise<War> => {
    const response = await api.post('/wars/declare', warData);
    return response.data;
  },

  // War participation
  joinWar: async (warId: string, side: 'attacker' | 'defender'): Promise<War> => {
    const response = await api.post(`/wars/${warId}/join`, { side });
    return response.data;
  },

  participateInWar: async (warId: string, participateData: ParticipateInWarDto): Promise<War> => {
    const response = await api.post(`/wars/${warId}/participate`, participateData);
    return response.data;
  },

  stopAutoAttack: async (warId: string): Promise<void> => {
    await api.post(`/wars/${warId}/stop-auto-attack`);
  },

  getUserSideInWar: async (warId: string): Promise<{ side: 'attacker' | 'defender' | 'neutral' }> => {
    const response = await api.get(`/wars/${warId}/user-side`);
    return response.data;
  },

  // War preparation and resources
  prepareForWar: async (warId: string, preparationData: any): Promise<War> => {
    const response = await api.post(`/wars/${warId}/prepare`, preparationData);
    return response.data;
  },

  getWarSupplies: async (warId: string): Promise<WarSupply[]> => {
    const response = await api.get(`/wars/${warId}/supplies`);
    return response.data;
  },

  addWarSupplies: async (warId: string, supplies: {
    resourceType: string;
    amount: number;
  }[]): Promise<WarSupply[]> => {
    const response = await api.post(`/wars/${warId}/supplies`, { supplies });
    return response.data;
  },

  getWarMorale: async (warId: string): Promise<WarMorale> => {
    const response = await api.get(`/wars/${warId}/morale`);
    return response.data;
  },

  getWarCosts: async (warId: string): Promise<WarCosts> => {
    const response = await api.get(`/wars/${warId}/costs`);
    return response.data;
  },

  getWarEvents: async (warId: string, params?: { page?: number; limit?: number }): Promise<WarEvent[]> => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    
    const response = await api.get(`/wars/${warId}/events${queryParams.toString() ? '?' + queryParams.toString() : ''}`);
    return response.data;
  },

  // War targets
  getAvailableTargets: async (type?: 'ground' | 'sea', attackingRegionId?: string): Promise<any[]> => {
    const params = new URLSearchParams();
    if (type) params.append('type', type);
    if (attackingRegionId) params.append('attackingRegionId', attackingRegionId);
    
    const response = await api.get(`/wars/available-targets${params.toString() ? '?' + params.toString() : ''}`);
    return response.data;
  },

  // War analytics
  getGlobalWarAnalytics: async (): Promise<any> => {
    const response = await api.get('/wars/analytics/global');
    return response.data;
  },

  getUserWarAnalytics: async (userId?: number): Promise<WarAnalytics> => {
    const url = userId ? `/wars/analytics/user/${userId}` : '/wars/analytics/user';
    const response = await api.get(url);
    return response.data;
  },

  getWarTimeline: async (limit?: number): Promise<any[]> => {
    const params = limit ? `?limit=${limit}` : '';
    const response = await api.get(`/wars/analytics/timeline${params}`);
    return response.data;
  },

  getGlobalWarStats: async (): Promise<{ totalWars: number }> => {
    const response = await api.get('/wars/analytics/global');
    return response.data;
  },

  getRegionWarHistory: async (regionId: string): Promise<any[]> => {
    const response = await api.get(`/wars/analytics/region/${regionId}`);
    return response.data;
  },

  // Advanced analytics
  getDamageLeaderboard: async (): Promise<any[]> => {
    const response = await api.get('/wars/analytics/advanced/damage-leaderboard');
    return response.data;
  },

  getEfficiencyMetrics: async (): Promise<any> => {
    const response = await api.get('/wars/analytics/advanced/efficiency-metrics');
    return response.data;
  },

  getRegionalPerformance: async (regionId: string): Promise<any> => {
    const response = await api.get(`/wars/analytics/advanced/regional-performance/${regionId}`);
    return response.data;
  },

  getWarTrends: async (): Promise<any> => {
    const response = await api.get('/wars/analytics/advanced/trends');
    return response.data;
  },
};

export default warService;