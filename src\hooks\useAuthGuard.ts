import { useEffect } from 'react';
import { useAuthStore } from '../store/useAuthStore';
import { isTokenValid } from '../utils/tokenUtils';

interface Props {
  navigation?: any;
}

export function useAuthGuard({ navigation }: Props = {}) {
  const { user, token, logout, isAuthenticated } = useAuthStore();

  useEffect(() => {
    const checkAuth = async () => {
      const isValid = isTokenValid(token);
      
      if (!user || !isAuthenticated || !isValid) {
        console.log('Auth guard: Invalid token or user, logging out');
        await logout();
        
        // If navigation is provided, navigate to login
        if (navigation) {
          navigation.navigate('Login');
        }
      }
    };

    // Only run auth check if we have a token to check
    if (token) {
      checkAuth();
    }
  }, [user, token, isAuthenticated, logout, navigation]);
}

export default useAuthGuard;