import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Users } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { partyService } from '../services/partyService';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

export const CreatePartyScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuthStore();
  useAuthGuard({ navigation });

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    regionId: user?.region?.id || '',
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!user) {
      navigation.navigate('Login');
    }
  }, [user, navigation]);

  const handleChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Party name is required',
      });
      return;
    }

    if (!formData.regionId) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Region is required',
      });
      return;
    }

    setLoading(true);

    try {
      await partyService.createParty(formData);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Party created successfully!',
      });
      navigation.navigate('Profile');
    } catch (err: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: err.response?.data?.message || err.message || 'Failed to create party',
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, backgroundColor: '#111827' }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
      >
        <View style={{ flex: 1, padding: 16, paddingTop: 40 }}>
          {/* Header */}
          <View style={{ marginBottom: 32 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <Users width={32} height={32} color="#3f87ff" />
              <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#ffffff', marginLeft: 12 }}>
                Create a New Party
              </Text>
            </View>
            <Text style={{ fontSize: 16, color: '#9CA3AF' }}>
              Build your political alliance
            </Text>
          </View>

          {/* Requirements Card */}
          <View style={{
            backgroundColor: '#374151',
            borderRadius: 12,
            padding: 20,
            marginBottom: 24,
          }}>
            <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginBottom: 12 }}>
              Requirements:
            </Text>
            <View style={{ marginBottom: 8 }}>
              <Text style={{ color: '#D1D5DB', fontSize: 16, marginBottom: 4 }}>
                • Cost: 200 gold
              </Text>
              <Text style={{ color: '#D1D5DB', fontSize: 16 }}>
                • You must be in a region
              </Text>
            </View>
            <Text style={{ color: '#D1D5DB', fontSize: 14, marginTop: 8 }}>
              Your current gold: {user?.gold || 0}
            </Text>
          </View>

          {/* Form */}
          <View style={{ flex: 1 }}>
            <View style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '500', color: '#D1D5DB', marginBottom: 8 }}>
                Party Name
              </Text>
              <TextInput
                value={formData.name}
                onChangeText={(text) => handleChange('name', text)}
                style={{
                  backgroundColor: '#374151',
                  borderWidth: 1,
                  borderColor: '#4B5563',
                  borderRadius: 8,
                  padding: 12,
                  color: '#ffffff',
                  fontSize: 16,
                }}
                placeholder="Enter party name"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '500', color: '#D1D5DB', marginBottom: 8 }}>
                Description
              </Text>
              <TextInput
                value={formData.description}
                onChangeText={(text) => handleChange('description', text)}
                style={{
                  backgroundColor: '#374151',
                  borderWidth: 1,
                  borderColor: '#4B5563',
                  borderRadius: 8,
                  padding: 12,
                  color: '#ffffff',
                  fontSize: 16,
                  minHeight: 100,
                  textAlignVertical: 'top',
                }}
                placeholder="Enter party description"
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={{ marginBottom: 32 }}>
              <Text style={{ fontSize: 16, fontWeight: '500', color: '#D1D5DB', marginBottom: 8 }}>
                Region
              </Text>
              <TextInput
                value={user?.region?.name || 'No region selected'}
                style={{
                  backgroundColor: '#374151',
                  borderWidth: 1,
                  borderColor: '#4B5563',
                  borderRadius: 8,
                  padding: 12,
                  color: '#9CA3AF',
                  fontSize: 16,
                }}
                editable={false}
              />
            </View>

            {/* Buttons */}
            <View style={{ flexDirection: 'row', gap: 12 }}>
              <TouchableOpacity
                onPress={() => navigation.goBack()}
                style={{
                  flex: 1,
                  backgroundColor: '#4B5563',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                }}
              >
                <Text style={{ color: '#D1D5DB', fontSize: 16, fontWeight: '500' }}>
                  Cancel
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                onPress={handleSubmit}
                disabled={loading}
                style={{
                  flex: 1,
                  backgroundColor: loading ? '#1E3A8A' : '#3f87ff',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                }}
              >
                {loading && <ActivityIndicator size="small" color="#ffffff" style={{ marginRight: 8 }} />}
                <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
                  {loading ? 'Creating...' : 'Create Party'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};