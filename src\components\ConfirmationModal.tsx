import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { AlertTriangle, X } from 'lucide-react-native';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonStyle?: 'default' | 'danger' | 'success';
  isLoading?: boolean;
  loadingText?: string;
  showWarning?: boolean;
  children?: React.ReactNode;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  confirmButtonStyle = 'default',
  isLoading = false,
  loadingText = "Processing...",
  showWarning = false,
  children
}) => {
  const getConfirmButtonColors = () => {
    switch (confirmButtonStyle) {
      case 'danger':
        return {
          backgroundColor: isLoading ? '#DC2626' : '#EF4444',
          disabledColor: '#7F1D1D'
        };
      case 'success':
        return {
          backgroundColor: isLoading ? '#059669' : '#10B981',
          disabledColor: '#064E3B'
        };
      default:
        return {
          backgroundColor: isLoading ? '#1E40AF' : '#3f87ff',
          disabledColor: '#1E3A8A'
        };
    }
  };

  const confirmButtonColors = getConfirmButtonColors();

  return (
    <Modal
      visible={isOpen}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
      }}>
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          padding: 24,
          maxWidth: 400,
          width: '100%',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.25,
          shadowRadius: 10,
          elevation: 10,
        }}>
          {/* Header */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            {showWarning && (
              <AlertTriangle
                width={24}
                height={24}
                color="#F59E0B"
                style={{ marginRight: 12 }}
              />
            )}
            <Text style={{
              fontSize: 20,
              fontWeight: '600',
              color: '#ffffff',
              flex: 1,
            }}>
              {title}
            </Text>
            <TouchableOpacity
              onPress={onClose}
              disabled={isLoading}
              style={{
                padding: 4,
                borderRadius: 4,
                opacity: isLoading ? 0.5 : 1,
              }}
            >
              <X width={20} height={20} color="#9CA3AF" />
            </TouchableOpacity>
          </View>

          {/* Message */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{
              fontSize: 16,
              color: '#D1D5DB',
              lineHeight: 24,
              marginBottom: children ? 12 : 0,
            }}>
              {message}
            </Text>
            {children}
          </View>

          {/* Action Buttons */}
          <View style={{
            flexDirection: 'row',
            gap: 12,
          }}>
            <TouchableOpacity
              onPress={onClose}
              disabled={isLoading}
              style={{
                flex: 1,
                paddingVertical: 12,
                paddingHorizontal: 16,
                backgroundColor: isLoading ? '#374151' : '#4B5563',
                borderRadius: 8,
                alignItems: 'center',
                opacity: isLoading ? 0.7 : 1,
              }}
            >
              <Text style={{
                color: '#ffffff',
                fontSize: 16,
                fontWeight: '500',
              }}>
                {cancelText}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={onConfirm}
              disabled={isLoading}
              style={{
                flex: 1,
                paddingVertical: 12,
                paddingHorizontal: 16,
                backgroundColor: isLoading ? confirmButtonColors.disabledColor : confirmButtonColors.backgroundColor,
                borderRadius: 8,
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'row',
                opacity: isLoading ? 0.7 : 1,
              }}
            >
              {isLoading && (
                <ActivityIndicator
                  size="small"
                  color="#ffffff"
                  style={{ marginRight: 8 }}
                />
              )}
              <Text style={{
                color: '#ffffff',
                fontSize: 16,
                fontWeight: '600',
              }}>
                {isLoading ? loadingText : confirmText}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default ConfirmationModal;