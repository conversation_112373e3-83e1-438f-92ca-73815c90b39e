import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { 
  Sword, 
  Shield, 
  Plane, 
  ChevronDown, 
  Plus, 
  RefreshCw,
  Clock,
  Users,
  MapPin,
  ArrowRight
} from 'lucide-react-native';
import { warService } from '../services/warService';
import { stateService } from '../services/stateService';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

interface War {
  id: string;
  warType: string;
  status: string;
  declaredAt: string;
  attackerRegion?: any;
  defenderRegion?: any;
  attackerState?: any;
  defenderState?: any;
  attackerGroundDamage?: number;
  attackerSeaDamage?: number;
  defenderGroundDamage?: number;
  defenderSeaDamage?: number;
  revolutionSupportPercentage?: number;
  warTarget?: string;
}

interface FilterCounts {
  total: number;
  active: number;
  pending: number;
  ended: number;
  sea: number;
  ground: number;
  revolution: number;
}

export const WarsScreen: React.FC<Props> = ({ navigation }) => {
  useAuthGuard({ navigation });

  const [stateWars, setStateWars] = useState<War[]>([]);
  const [allWars, setAllWars] = useState<War[]>([]);
  const [myState, setMyState] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'state' | 'all'>('state');
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [loadedTabs, setLoadedTabs] = useState({ state: false, all: false });
  const [warCounts, setWarCounts] = useState<{
    state: FilterCounts;
    all: FilterCounts;
  }>({
    state: { total: 0, active: 0, pending: 0, ended: 0, sea: 0, ground: 0, revolution: 0 },
    all: { total: 0, active: 0, pending: 0, ended: 0, sea: 0, ground: 0, revolution: 0 }
  });

  // Fetch user state
  const fetchStateData = async () => {
    try {
      const stateResponse = await stateService.getMyState();
      setMyState(stateResponse);
      return stateResponse;
    } catch (error) {
      console.error('Error fetching user state:', error);
      setMyState(null);
      return null;
    }
  };

  // Fetch wars for a specific tab
  const fetchWarsForTab = async (tab: 'state' | 'all') => {
    setLoading(true);
    setError(null);
    
    try {
      let warData: any;

      if (tab === 'state') {
        const state = myState || await fetchStateData();
        if (state && state.id) {
          warData = await warService.getWarsByState(state.id);
          setStateWars(Array.isArray(warData) ? warData : []);
        } else {
          setStateWars([]);
          warData = [];
        }
      } else {
        warData = await warService.getAllWars();
        setAllWars(Array.isArray(warData) ? warData : []);
      }

      setError(null);
      setLastUpdated(new Date());
      setLoadedTabs(prev => ({ ...prev, [tab]: true }));
    } catch (err) {
      console.error(`Error fetching wars for ${tab} tab:`, err);
      setError('Failed to load wars. Please try again later.');
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load wars'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Get current wars based on active tab
  const currentWars = useMemo(() => {
    let wars = activeTab === 'state' ? stateWars : allWars;
    
    // Ensure wars is always an array
    if (!Array.isArray(wars)) {
      wars = [];
    }
    
    // Apply filters
    if (statusFilter !== 'all') {
      wars = wars.filter(war => war.status === statusFilter);
    }
    if (typeFilter !== 'all') {
      wars = wars.filter(war => war.warType === typeFilter);
    }
    
    return wars;
  }, [activeTab, stateWars, allWars, statusFilter, typeFilter]);

  useEffect(() => {
    const fetchInitialData = async () => {
      const state = await fetchStateData();
      if (state && state.id) {
        fetchWarsForTab('state');
      }
    };

    fetchInitialData();
  }, []);

  // Handle tab change
  const handleTabChange = (tab: 'state' | 'all') => {
    setActiveTab(tab);
    if (!loadedTabs[tab]) {
      fetchWarsForTab(tab);
    }
  };

  // Refresh current tab
  const handleRefresh = () => {
    setRefreshing(true);
    fetchWarsForTab(activeTab);
  };

  // Get war type color scheme
  const getWarTypeColor = (warType: string) => {
    switch (warType) {
      case 'sea':
        return {
          border: '#3b82f6',
          background: '#1e40af',
          text: '#93c5fd',
          displayName: 'SEA WAR'
        };
      case 'ground':
        return {
          border: '#10b981',
          background: '#047857',
          text: '#6ee7b7',
          displayName: 'GROUND WAR'
        };
      case 'revolution':
        return {
          border: '#f59e0b',
          background: '#d97706',
          text: '#fbbf24',
          displayName: 'REVOLUTION'
        };
      default:
        return {
          border: '#8b5cf6',
          background: '#7c3aed',
          text: '#c4b5fd',
          displayName: 'WAR'
        };
    }
  };

  // Get status color
  const getWarStatusClass = (status: string) => {
    switch (status) {
      case 'pending':
        return { background: '#1e40af', text: '#ffffff' };
      case 'active':
        return { background: '#dc2626', text: '#ffffff' };
      case 'ended':
        return { background: '#374151', text: '#d1d5db' };
      default:
        return { background: '#374151', text: '#d1d5db' };
    }
  };

  // Render damage indicator
  const renderDamageIndicator = (attackerDamage = 0, defenderDamage = 0) => {
    const totalDamage = attackerDamage + defenderDamage || 1;
    const attackerPercentage = Math.round((attackerDamage / totalDamage) * 100);

    return (
      <View className="mt-3">
        <View className="flex-row justify-between mb-2">
          <Text className="text-xs font-bold text-gray-400">
            ATTACKER DMG: {attackerDamage}
          </Text>
          <Text className="text-xs font-bold text-gray-400">
            DEFENDER DMG: {defenderDamage}
          </Text>
        </View>
        <View className="w-full bg-gray-700 h-2 rounded-full border border-gray-600">
          <View 
            className="bg-red-700 h-full rounded-full"
            style={{ width: `${attackerPercentage}%` }}
          />
        </View>
      </View>
    );
  };

  // Render wars list
  const renderWarsList = () => {
    if (currentWars.length === 0) {
      return (
        <View className="bg-gray-800 rounded-xl p-8 text-center mt-6 border-2 border-gray-700">
          <Text className="text-xl font-bold text-gray-300 text-center">
            {statusFilter !== 'all' || typeFilter !== 'all' 
              ? 'No wars match the selected filters' 
              : 'No wars found in this category.'
            }
          </Text>
          {activeTab === 'state' && (!myState || !myState.id) && (
            <TouchableOpacity
              className="mt-6 bg-red-700 px-6 py-3 rounded-xl"
              onPress={() => navigation.navigate('DeclareWar')}
            >
              <Text className="text-white font-bold text-center">
                DECLARE NEW WAR
              </Text>
            </TouchableOpacity>
          )}
        </View>
      );
    }

    return (
      <View className="mt-6 space-y-4">
        {currentWars.map(war => {
          const warTypeColor = getWarTypeColor(war.warType);
          const statusColor = getWarStatusClass(war.status);
          
          return (
            <TouchableOpacity
              key={war.id}
              className="bg-gray-800 p-5 rounded-xl border-l-4"
              style={{ borderLeftColor: warTypeColor.border }}
              onPress={() => navigation.navigate('WarDetail', { warId: war.id })}
            >
              <View className="flex-row justify-between items-start mb-4">
                <View>
                  <Text className="text-xl font-bold text-white uppercase">
                    {warTypeColor.displayName}
                  </Text>
                </View>
                <View 
                  className="px-3 py-1 rounded-lg"
                  style={{ backgroundColor: warTypeColor.background }}
                >
                  <Text className="text-xs font-bold text-white">
                    {war.warType?.toUpperCase()?.slice(0, 3) || 'WAR'}
                  </Text>
                </View>
              </View>

              <View className="mb-4 space-y-2">
                <View className="flex-row justify-between">
                  <Text className="font-bold text-gray-400">STATUS:</Text>
                  <View 
                    className="px-2 py-1 rounded"
                    style={{ backgroundColor: statusColor.background }}
                  >
                    <Text 
                      className="text-xs font-bold"
                      style={{ color: statusColor.text }}
                    >
                      {war.status.toUpperCase()}
                    </Text>
                  </View>
                </View>
                <View className="flex-row justify-between">
                  <Text className="font-bold text-gray-400">TARGET:</Text>
                  <Text className="text-white">
                    {war.warTarget ? war.warTarget.toUpperCase() : 'CLASSIFIED'}
                  </Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="font-bold text-gray-400">STARTED:</Text>
                  <Text className="text-gray-300">
                    {new Date(war.declaredAt).toLocaleDateString()}
                  </Text>
                </View>
              </View>

              <View className="mb-4 p-4 bg-gray-900 rounded-xl">
                <View className="flex-row justify-between mb-3">
                  <View className="items-center flex-1">
                    <Text className="font-bold text-red-400">ATTACKER</Text>
                    <TouchableOpacity 
                      className="text-center mt-1"
                      onPress={() => war.attackerRegion && navigation.navigate('RegionDetail', { regionId: war.attackerRegion.id })}
                    >
                      <Text className="text-sm text-neonBlue">
                        {war.attackerRegion ? war.attackerRegion.name : 'UNKNOWN'}
                      </Text>
                      {war.attackerState && (
                        <Text className="text-xs text-gray-400">
                          {war.attackerState.name}
                        </Text>
                      )}
                    </TouchableOpacity>
                  </View>
                  <View className="items-center flex-1">
                    <Text className="font-bold text-blue-400">DEFENDER</Text>
                    <TouchableOpacity 
                      className="text-center mt-1"
                      onPress={() => war.defenderRegion && navigation.navigate('RegionDetail', { regionId: war.defenderRegion.id })}
                    >
                      <Text className="text-sm text-neonBlue">
                        {war.defenderRegion ? war.defenderRegion.name : 'UNKNOWN'}
                      </Text>
                      {war.defenderState && (
                        <Text className="text-xs text-gray-400">
                          {war.defenderState.name}
                        </Text>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>

                {renderDamageIndicator(
                  (war.attackerGroundDamage || 0) + (war.attackerSeaDamage || 0),
                  (war.defenderGroundDamage || 0) + (war.defenderSeaDamage || 0)
                )}
              </View>

              <TouchableOpacity
                className="w-full py-3 px-4 rounded-xl"
                style={{ backgroundColor: warTypeColor.background }}
                onPress={() => navigation.navigate('WarDetail', { warId: war.id })}
              >
                <Text className="text-center font-bold text-white">
                  VIEW BATTLE DETAILS
                </Text>
              </TouchableOpacity>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  if (loading && stateWars.length === 0 && allWars.length === 0) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <ActivityIndicator size="large" color="#dc2626" />
        <Text className="mt-6 text-xl font-bold text-white text-center">
          GATHERING BATTLEFIELD INTELLIGENCE...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 bg-gray-900 p-4">
        <View className="bg-red-900 border-l-4 border-red-500 p-6 rounded-xl mt-8">
          <View className="flex-row items-start">
            <View className="ml-3">
              <Text className="font-bold text-lg text-red-100">
                COMMUNICATION FAILURE
              </Text>
              <Text className="mt-2 text-red-100">{error}</Text>
              <TouchableOpacity 
                onPress={handleRefresh}
                className="mt-4 bg-red-700 px-4 py-2 rounded-md"
              >
                <Text className="font-bold text-white text-sm">
                  RETRY TRANSMISSION
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-900">
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View className="p-4">
          {/* Header */}
          <View className="flex-row justify-between items-center mb-8">
            <View>
              <Text className="text-3xl font-bold text-white uppercase">
                WAR COMMAND CENTER
              </Text>
              <Text className="text-gray-400 mt-2 font-medium">
                Strategic overview of all active and historical conflicts
              </Text>
              {lastUpdated && (
                <Text className="text-xs text-gray-500 mt-1">
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </Text>
              )}
            </View>
          </View>

          {/* Action Buttons */}
          <View className="flex-row space-x-4 mb-8">
            <TouchableOpacity
              className="flex-1 bg-red-700 py-3 px-6 rounded-xl flex-row items-center justify-center"
              onPress={() => navigation.navigate('DeclareWar')}
            >
              <Plus width={20} height={20} color="#ffffff" />
              <Text className="text-white font-bold ml-2 uppercase">
                NEW WAR
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="bg-gray-800 py-3 px-6 rounded-xl flex-row items-center justify-center border-2 border-gray-700"
              onPress={handleRefresh}
              disabled={loading}
            >
              <RefreshCw 
                width={20} 
                height={20} 
                color={loading ? "#6b7280" : "#ffffff"} 
              />
              <Text className={`font-bold ml-2 ${loading ? 'text-gray-500' : 'text-white'}`}>
                {loading ? 'UPDATING...' : 'REFRESH'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Tab Selector */}
          <View className="mb-6">
            <View className="flex-row space-x-4">
              <TouchableOpacity
                className={`flex-1 py-3 px-6 rounded-xl border-2 ${
                  activeTab === 'state' 
                    ? 'bg-red-700 border-red-800' 
                    : 'bg-gray-800 border-gray-700'
                } flex-row items-center justify-center`}
                onPress={() => handleTabChange('state')}
              >
                <Shield width={20} height={20} color="#ffffff" />
                <Text className="text-white font-bold ml-2">STATE WARS</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`flex-1 py-3 px-6 rounded-xl border-2 ${
                  activeTab === 'all' 
                    ? 'bg-red-700 border-red-800' 
                    : 'bg-gray-800 border-gray-700'
                } flex-row items-center justify-center`}
                onPress={() => handleTabChange('all')}
              >
                <Users width={20} height={20} color="#ffffff" />
                <Text className="text-white font-bold ml-2">ALL WARS</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Filters */}
          <View className="flex-row space-x-4 mb-6">
            <View className="flex-1">
              <TouchableOpacity
                className="bg-gray-800 py-3 px-4 rounded-xl border-2 border-gray-700 flex-row items-center justify-between"
                onPress={() => setOpenDropdown(openDropdown === 'status' ? null : 'status')}
              >
                <Text className="text-white font-bold">
                  {statusFilter === 'all' ? 'ALL STATUSES' : statusFilter.toUpperCase()}
                </Text>
                <ChevronDown width={20} height={20} color="#ffffff" />
              </TouchableOpacity>

              {openDropdown === 'status' && (
                <View className="absolute top-14 left-0 right-0 bg-gray-800 rounded-xl border-2 border-gray-700 z-10">
                  {['all', 'active', 'pending', 'ended'].map((status) => (
                    <TouchableOpacity
                      key={status}
                      className="py-3 px-4 border-b border-gray-700 last:border-b-0"
                      onPress={() => {
                        setStatusFilter(status);
                        setOpenDropdown(null);
                      }}
                    >
                      <Text className={`font-bold ${statusFilter === status ? 'text-red-400' : 'text-white'}`}>
                        {status === 'all' ? 'All Statuses' : status.charAt(0).toUpperCase() + status.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>

            <View className="flex-1">
              <TouchableOpacity
                className="bg-gray-800 py-3 px-4 rounded-xl border-2 border-gray-700 flex-row items-center justify-between"
                onPress={() => setOpenDropdown(openDropdown === 'type' ? null : 'type')}
              >
                <Text className="text-white font-bold">
                  {typeFilter === 'all' ? 'ALL TYPES' : typeFilter.toUpperCase()}
                </Text>
                <ChevronDown width={20} height={20} color="#ffffff" />
              </TouchableOpacity>

              {openDropdown === 'type' && (
                <View className="absolute top-14 left-0 right-0 bg-gray-800 rounded-xl border-2 border-gray-700 z-10">
                  {['all', 'ground', 'sea', 'revolution'].map((type) => (
                    <TouchableOpacity
                      key={type}
                      className="py-3 px-4 border-b border-gray-700 last:border-b-0"
                      onPress={() => {
                        setTypeFilter(type);
                        setOpenDropdown(null);
                      }}
                    >
                      <Text className={`font-bold ${typeFilter === type ? 'text-red-400' : 'text-white'}`}>
                        {type === 'all' ? 'All War Types' : type.charAt(0).toUpperCase() + type.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>

          {/* Wars Count */}
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-gray-400">
              Showing <Text className="font-bold text-white">{currentWars.length}</Text> wars
            </Text>
            <View className="flex-row space-x-2">
              {statusFilter !== 'all' && (
                <View className="bg-gray-800 px-2 py-1 rounded-full">
                  <Text className="text-gray-300 text-xs">
                    Status: {statusFilter.toUpperCase()}
                  </Text>
                </View>
              )}
              {typeFilter !== 'all' && (
                <View className="bg-gray-800 px-2 py-1 rounded-full">
                  <Text className="text-gray-300 text-xs">
                    Type: {typeFilter.toUpperCase()}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {renderWarsList()}
        </View>
      </ScrollView>
    </View>
  );
};