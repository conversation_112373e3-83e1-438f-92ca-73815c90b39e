import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';

// Cross-platform secure storage utility
export const secureStorage = {
  async getItem(key: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        // On web, SecureStore falls back to localStorage
        return localStorage.getItem(key);
      } else {
        // On mobile, use SecureStore
        return await SecureStore.getItemAsync(key);
      }
    } catch (error) {
      console.error(`Error getting item ${key}:`, error);
      return null;
    }
  },

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // On web, SecureStore falls back to localStorage
        localStorage.setItem(key, value);
      } else {
        // On mobile, use SecureStore
        await SecureStore.setItemAsync(key, value);
      }
    } catch (error) {
      console.error(`Error setting item ${key}:`, error);
      throw error;
    }
  },

  async deleteItem(key: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // On web, SecureStore falls back to localStorage
        localStorage.removeItem(key);
      } else {
        // On mobile, use SecureStore
        await SecureStore.deleteItemAsync(key);
      }
    } catch (error) {
      console.error(`Error deleting item ${key}:`, error);
      throw error;
    }
  },
};