import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { BarChart3, User, Timeline, Trophy, Target, TrendingUp, Sword } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { warService } from '../services/warService';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { GlobalWarStats } from '../components/GlobalWarStats';
import { WarTimeline } from '../components/WarTimeline';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

interface GlobalWarStats {
  totalWars: number;
  activeWars: number;
  completedWars: number;
  totalParticipants: number;
  totalDamage: number;
  avgWarDuration: number;
}

interface UserWarStats {
  totalParticipations: number;
  totalDamage: number;
  totalKills: number;
  avgDamagePerWar: number;
  warsWon: number;
  warsLost: number;
  efficiency: number;
}

interface LeaderboardEntry {
  id: number;
  username: string;
  totalDamage: number;
  totalKills: number;
  participations: number;
  efficiency: number;
}

export const WarAnalyticsScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuthStore();
  useAuthGuard({ navigation });

  const [activeTab, setActiveTab] = useState<'overview' | 'personal' | 'timeline' | 'leaderboard' | 'trends'>('overview');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [globalStats, setGlobalStats] = useState<GlobalWarStats | null>(null);
  const [userStats, setUserStats] = useState<UserWarStats | null>(null);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);

  useEffect(() => {
    fetchData();
  }, [activeTab]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      if (activeTab === 'overview') {
        const stats = await warService.getGlobalWarStats();
        setGlobalStats(stats);
      } else if (activeTab === 'personal') {
        const stats = await warService.getUserWarStats();
        setUserStats(stats);
      } else if (activeTab === 'leaderboard') {
        const data = await warService.getWarLeaderboard();
        setLeaderboard(data);
      }
    } catch (error) {
      console.error('Error fetching war analytics:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load war analytics'
      });
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const renderTabButton = (tab: string, icon: any, label: string) => {
    const IconComponent = icon;
    const isActive = activeTab === tab;
    
    return (
      <TouchableOpacity
        key={tab}
        onPress={() => setActiveTab(tab as any)}
        style={{
          paddingVertical: 12,
          paddingHorizontal: 16,
          borderBottomWidth: isActive ? 2 : 0,
          borderBottomColor: '#3f87ff',
          flex: 1,
          alignItems: 'center',
        }}
      >
        <IconComponent width={20} height={20} color={isActive ? '#3f87ff' : '#9CA3AF'} />
        <Text style={{
          color: isActive ? '#3f87ff' : '#9CA3AF',
          fontSize: 12,
          fontWeight: isActive ? '600' : '400',
          marginTop: 4,
        }}>
          {label}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderOverviewTab = () => (
    <View style={{ padding: 16 }}>
      <GlobalWarStats
        onRegionPress={(regionId) => navigation.navigate('RegionDetail', { regionId })}
        onStatePress={(stateId) => navigation.navigate('StateDetail', { stateId })}
      />
    </View>
  );

  const renderPersonalTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        Your War Statistics
      </Text>
      
      {userStats ? (
        <View>
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 20,
            marginBottom: 16,
          }}>
            <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginBottom: 16 }}>
              Participation Stats
            </Text>
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
              <Text style={{ color: '#9CA3AF' }}>Wars Participated:</Text>
              <Text style={{ color: '#ffffff', fontWeight: '600' }}>{userStats.totalParticipations}</Text>
            </View>
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
              <Text style={{ color: '#9CA3AF' }}>Wars Won:</Text>
              <Text style={{ color: '#10B981', fontWeight: '600' }}>{userStats.warsWon}</Text>
            </View>
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#9CA3AF' }}>Wars Lost:</Text>
              <Text style={{ color: '#EF4444', fontWeight: '600' }}>{userStats.warsLost}</Text>
            </View>
          </View>

          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 20,
            marginBottom: 16,
          }}>
            <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginBottom: 16 }}>
              Combat Performance
            </Text>
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
              <Text style={{ color: '#9CA3AF' }}>Total Damage:</Text>
              <Text style={{ color: '#EF4444', fontWeight: '600' }}>{formatNumber(userStats.totalDamage)}</Text>
            </View>
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
              <Text style={{ color: '#9CA3AF' }}>Total Kills:</Text>
              <Text style={{ color: '#ffffff', fontWeight: '600' }}>{userStats.totalKills}</Text>
            </View>
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
              <Text style={{ color: '#9CA3AF' }}>Avg Damage/War:</Text>
              <Text style={{ color: '#ffffff', fontWeight: '600' }}>{formatNumber(userStats.avgDamagePerWar || 0)}</Text>
            </View>
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#9CA3AF' }}>Efficiency:</Text>
              <Text style={{ color: '#F59E0B', fontWeight: '600' }}>{(userStats.efficiency || 0).toFixed(1)}%</Text>
            </View>
          </View>
        </View>
      ) : (
        <View style={{ alignItems: 'center', paddingVertical: 40 }}>
          <Sword width={48} height={48} color="#6B7280" />
          <Text style={{ color: '#9CA3AF', fontSize: 16, marginTop: 16 }}>No personal statistics available</Text>
          <Text style={{ color: '#6B7280', fontSize: 14, marginTop: 8, textAlign: 'center' }}>
            Participate in wars to see your statistics here
          </Text>
        </View>
      )}
    </View>
  );

  const renderLeaderboardTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        War Leaderboard
      </Text>
      
      {leaderboard.length > 0 ? (
        <View style={{
          backgroundColor: '#1F2937',
          borderRadius: 12,
          overflow: 'hidden',
        }}>
          {leaderboard.map((entry, index) => (
            <View
              key={entry.id}
              style={{
                padding: 16,
                borderBottomWidth: index < leaderboard.length - 1 ? 1 : 0,
                borderBottomColor: '#374151',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <View style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: index < 3 ? '#F59E0B' : '#4B5563',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 16,
              }}>
                <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: 'bold' }}>
                  {index + 1}
                </Text>
              </View>
              
              <View style={{ flex: 1 }}>
                <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600', marginBottom: 4 }}>
                  {entry.username}
                </Text>
                <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
                  {formatNumber(entry.totalDamage)} damage • {entry.totalKills} kills
                </Text>
              </View>
              
              <View style={{ alignItems: 'flex-end' }}>
                <Text style={{ color: '#F59E0B', fontSize: 14, fontWeight: '600' }}>
                  {entry.efficiency.toFixed(1)}% eff
                </Text>
                <Text style={{ color: '#9CA3AF', fontSize: 12 }}>
                  {entry.participations} wars
                </Text>
              </View>
            </View>
          ))}
        </View>
      ) : (
        <View style={{ alignItems: 'center', paddingVertical: 40 }}>
          <Trophy width={48} height={48} color="#6B7280" />
          <Text style={{ color: '#9CA3AF', fontSize: 16, marginTop: 16 }}>No leaderboard data available</Text>
        </View>
      )}
    </View>
  );

  const renderTimelineTab = () => (
    <View style={{ padding: 16 }}>
      <WarTimeline
        limit={20}
        onWarPress={(warId) => navigation.navigate('WarDetail', { warId })}
      />
    </View>
  );

  const renderTrendsTab = () => (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 20 }}>
        War Trends
      </Text>
      
      <View style={{ alignItems: 'center', paddingVertical: 60 }}>
        <TrendingUp width={48} height={48} color="#6B7280" />
        <Text style={{ color: '#9CA3AF', fontSize: 16, marginTop: 16 }}>Trends Coming Soon</Text>
        <Text style={{ color: '#6B7280', fontSize: 14, marginTop: 8, textAlign: 'center' }}>
          Advanced trend analysis will be available in a future update
        </Text>
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#111827' }}>
      {/* Header */}
      <View style={{ padding: 16, paddingTop: 40 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <BarChart3 width={32} height={32} color="#3f87ff" />
          <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#ffffff', marginLeft: 12 }}>
            War Analytics
          </Text>
        </View>
        
        <Text style={{ fontSize: 16, color: '#9CA3AF' }}>
          Analyze war statistics and performance metrics
        </Text>
      </View>

      {/* Tabs */}
      <View style={{ 
        flexDirection: 'row', 
        backgroundColor: '#1F2937', 
        borderBottomWidth: 1, 
        borderBottomColor: '#374151' 
      }}>
        {renderTabButton('overview', BarChart3, 'Overview')}
        {renderTabButton('personal', User, 'Personal')}
        {renderTabButton('timeline', Timeline, 'Timeline')}
        {renderTabButton('leaderboard', Trophy, 'Rankings')}
        {renderTabButton('trends', TrendingUp, 'Trends')}
      </View>

      {/* Content */}
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {loading ? (
          <View style={{ alignItems: 'center', paddingVertical: 40 }}>
            <ActivityIndicator size="large" color="#3f87ff" />
            <Text style={{ color: '#3f87ff', fontSize: 16, marginTop: 16 }}>
              Loading analytics...
            </Text>
          </View>
        ) : (
          <>
            {activeTab === 'overview' && renderOverviewTab()}
            {activeTab === 'personal' && renderPersonalTab()}
            {activeTab === 'timeline' && renderTimelineTab()}
            {activeTab === 'leaderboard' && renderLeaderboardTab()}
            {activeTab === 'trends' && renderTrendsTab()}
          </>
        )}
      </ScrollView>
    </View>
  );
};