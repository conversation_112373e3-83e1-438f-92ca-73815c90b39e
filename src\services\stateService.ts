import { api } from './api';

export interface State {
  id: string;
  name: string;
  flag?: string;
  description?: string;
  leaderId: number;
  leader?: {
    id: number;
    username: string;
    level?: number;
  };
  governmentType: string;
  capital?: string;
  population: number;
  regions: any[];
  resources?: {
    money: number;
    gold: number;
    energy: number;
  };
  allies: string[];
  enemies: string[];
  createdAt: string;
  updatedAt: string;
}

export interface StateResource {
  id: string;
  stateId: string;
  resourceType: string;
  amount: number;
  lastUpdated: string;
}

export const stateService = {
  // State management
  getAllStates: async (): Promise<State[]> => {
    const response = await api.get('/states');
    return response.data;
  },

  getAllStatesCount: async (): Promise<number> => {
    const response = await api.get('/states/count');
    return response.data;
  },

  getStateById: async (stateId: string): Promise<State> => {
    const response = await api.get(`/states/${stateId}`);
    return response.data;
  },

  getMyState: async (): Promise<State> => {
    const response = await api.get('/states/my-state');
    return response.data;
  },

  getStateLedByUser: async (): Promise<State> => {
    const response = await api.get('/states/leader');
    return response.data;
  },

  createState: async (stateData: {
    name: string;
    description?: string;
    governmentType: string;
    capital?: string;
  }): Promise<State> => {
    const response = await api.post('/states', stateData);
    return response.data;
  },

  updateState: async (stateId: string, updates: Partial<State>): Promise<State> => {
    const response = await api.put(`/states/${stateId}`, updates);
    return response.data;
  },

  // State resources
  getStateResources: async (stateId: string): Promise<StateResource[]> => {
    const response = await api.get(`/states/${stateId}/resources`);
    return response.data;
  },

  // Region management
  addRegionToState: async (stateId: string, regionData: {
    regionId: string;
    name: string;
  }): Promise<State> => {
    const response = await api.post(`/states/${stateId}/regions`, regionData);
    return response.data;
  },

  removeRegionFromState: async (stateId: string, regionId: string): Promise<State> => {
    const response = await api.delete(`/states/${stateId}/regions/${regionId}`);
    return response.data;
  },

  // Leadership
  changeStateLeader: async (stateId: string, newLeaderId: number): Promise<State> => {
    const response = await api.post(`/states/${stateId}/change-leader/${newLeaderId}`);
    return response.data;
  },

  // Government
  changeGovernmentType: async (stateId: string, governmentType: string): Promise<State> => {
    const response = await api.post(`/states/${stateId}/change-government`, { governmentType });
    return response.data;
  },

  // Diplomacy
  addAlly: async (stateId: string, allyId: string): Promise<State> => {
    const response = await api.post(`/states/${stateId}/allies/${allyId}`);
    return response.data;
  },

  addEnemy: async (stateId: string, enemyId: string): Promise<State> => {
    const response = await api.post(`/states/${stateId}/enemies/${enemyId}`);
    return response.data;
  },

  // State images
  uploadStatePicture: async (stateId: string, imageFile: FormData): Promise<State> => {
    const response = await api.post(`/states/${stateId}/picture`, imageFile, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};