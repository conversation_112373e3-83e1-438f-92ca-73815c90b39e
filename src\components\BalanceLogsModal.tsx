import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { X, Filter, TrendingUp, TrendingDown, Calendar, DollarSign } from 'lucide-react-native';
import { balanceLogService, BalanceLog, GetBalanceLogsParams } from '../services/balanceLogService';
import { showErrorToast } from '../utils/toastUtils';

interface Props {
  visible: boolean;
  onClose: () => void;
  initialBalanceType?: 'gold' | 'money';
}

export const BalanceLogsModal: React.FC<Props> = ({ 
  visible, 
  onClose, 
  initialBalanceType = 'gold' 
}) => {
  const [logs, setLogs] = useState<BalanceLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<GetBalanceLogsParams>({
    page: 1,
    limit: 20,
    balanceType: initialBalanceType,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (visible) {
      fetchLogs(true);
    }
  }, [visible, filters]);

  const fetchLogs = async (reset = false) => {
    try {
      setLoading(true);
      const response = await balanceLogService.getBalanceLogs(filters);
      
      if (reset) {
        setLogs(response.data);
      } else {
        setLogs(prev => [...prev, ...response.data]);
      }
      
      setHasMore(response.pagination.hasNext);
    } catch (error) {
      console.error('Failed to fetch balance logs:', error);
      showErrorToast('Failed to load transaction history');
    } finally {
      setLoading(false);
    }
  };

  const loadMore = () => {
    if (!loading && hasMore) {
      setFilters(prev => ({ ...prev, page: (prev.page || 1) + 1 }));
    }
  };

  const resetFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
      balanceType: initialBalanceType,
    });
    setShowFilters(false);
  };

  const formatAmount = (log: BalanceLog) => {
    const sign = log.amount >= 0 ? '+' : '';
    const currency = log.balanceType === 'gold' ? '🪙' : '💰';
    return `${sign}${log.amount.toLocaleString()} ${currency}`;
  };

  const getTransactionIcon = (transactionType: string) => {
    if (transactionType.includes('income') || transactionType.includes('earn')) {
      return <TrendingUp width={16} height={16} color="#10b981" />;
    }
    return <TrendingDown width={16} height={16} color="#ef4444" />;
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View className="flex-1 bg-gray-900">
        {/* Header */}
        <View className="flex-row items-center justify-between p-4 border-b border-gray-700">
          <Text className="text-xl font-bold text-white">Transaction History</Text>
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => setShowFilters(!showFilters)}
              className="mr-4 p-2"
            >
              <Filter width={20} height={20} color="#3f87ff" />
            </TouchableOpacity>
            <TouchableOpacity onPress={onClose} className="p-2">
              <X width={24} height={24} color="#ffffff" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Filters */}
        {showFilters && (
          <View className="bg-gray-800 p-4 border-b border-gray-700">
            <Text className="text-white font-semibold mb-3">Filters</Text>
            
            {/* Balance Type Filter */}
            <View className="flex-row mb-3">
              <TouchableOpacity
                className={`px-4 py-2 rounded-l-lg border ${filters.balanceType === 'gold' ? 'bg-yellow-600 border-yellow-600' : 'bg-gray-700 border-gray-600'}`}
                onPress={() => setFilters(prev => ({ ...prev, balanceType: 'gold', page: 1 }))}
              >
                <Text className={`${filters.balanceType === 'gold' ? 'text-white' : 'text-gray-300'}`}>
                  🪙 Gold
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                className={`px-4 py-2 rounded-r-lg border ${filters.balanceType === 'money' ? 'bg-green-600 border-green-600' : 'bg-gray-700 border-gray-600'}`}
                onPress={() => setFilters(prev => ({ ...prev, balanceType: 'money', page: 1 }))}
              >
                <Text className={`${filters.balanceType === 'money' ? 'text-white' : 'text-gray-300'}`}>
                  💰 Money
                </Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              className="bg-gray-600 rounded-lg p-2"
              onPress={resetFilters}
            >
              <Text className="text-white text-center">Reset Filters</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Transaction List */}
        <ScrollView className="flex-1 p-4">
          {loading && logs.length === 0 ? (
            <View className="flex-1 justify-center items-center">
              <ActivityIndicator size="large" color="#3f87ff" />
              <Text className="text-gray-300 mt-4">Loading transactions...</Text>
            </View>
          ) : logs.length === 0 ? (
            <View className="flex-1 justify-center items-center">
              <DollarSign width={48} height={48} color="#6b7280" />
              <Text className="text-gray-400 text-lg mt-4">No transactions found</Text>
              <Text className="text-gray-500 text-center mt-2">
                Your transaction history will appear here
              </Text>
            </View>
          ) : (
            <View className="space-y-3">
              {logs.map((log, index) => (
                <View key={log.id} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <View className="flex-row justify-between items-start mb-2">
                    <View className="flex-row items-center flex-1">
                      {getTransactionIcon(log.transactionType)}
                      <Text className="text-white font-medium ml-2 flex-1">
                        {log.transactionType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Text>
                    </View>
                    <Text className={`font-bold ${log.amount >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {formatAmount(log)}
                    </Text>
                  </View>
                  
                  {log.description && (
                    <Text className="text-gray-300 text-sm mb-2">{log.description}</Text>
                  )}
                  
                  <View className="flex-row justify-between items-center">
                    <Text className="text-gray-400 text-xs">
                      {new Date(log.createdAt).toLocaleString()}
                    </Text>
                    <Text className="text-gray-400 text-xs">
                      Balance: {log.balanceAfter.toLocaleString()} {log.balanceType === 'gold' ? '🪙' : '💰'}
                    </Text>
                  </View>
                </View>
              ))}
              
              {/* Load More Button */}
              {hasMore && (
                <TouchableOpacity
                  className="bg-gray-700 rounded-lg p-4 mt-4"
                  onPress={loadMore}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color="#3f87ff" />
                  ) : (
                    <Text className="text-center text-gray-300">Load More</Text>
                  )}
                </TouchableOpacity>
              )}
            </View>
          )}
        </ScrollView>
      </View>
    </Modal>
  );
};
