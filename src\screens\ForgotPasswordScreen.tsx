import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
} from 'react-native';
import { authAPI } from '../services/api';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

export const ForgotPasswordScreen: React.FC<Props> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleForgotPassword = async () => {
    if (!email) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please enter your email address',
      });
      return;
    }

    try {
      setLoading(true);
      await authAPI.forgotPassword(email);
      setSubmitted(true);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'If an account exists, you will receive an email shortly.',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Something went wrong. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
        backgroundColor: '#0F0F10',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 16,
      }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
        showsVerticalScrollIndicator={false}
        style={{ width: '100%' }}
      >
        <View style={{
          width: '100%',
          maxWidth: 400,
          alignSelf: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: 16,
          padding: 32,
        }}>
          <View style={{ alignItems: 'center', marginBottom: 24 }}>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={{
                fontSize: 14,
                textAlign: 'center',
                color: '#A1A1A1',
                marginBottom: 16,
              }}>
                Back to login page
              </Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => navigation.navigate('Landing')}>
              <Image
                source={require('../../assets/images/wn-logo.png')}
                style={{
                  width: 56,
                  height: 56,
                  marginBottom: 16,
                }}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#ffffff',
              textAlign: 'center',
            }}>
              Forgot Password
            </Text>
          </View>

          <View style={{
            backgroundColor: '#1C1C1E',
            padding: 24,
            borderRadius: 12,
          }}>
            {submitted ? (
              <View>
                <Text style={{
                  color: '#4ade80',
                  marginBottom: 16,
                  fontSize: 16,
                  textAlign: 'center',
                }}>
                  If an account exists, you will receive an email shortly.
                </Text>
                <TouchableOpacity onPress={() => navigation.navigate('Login')}>
                  <Text style={{
                    fontSize: 14,
                    color: '#3f87ff',
                    textAlign: 'center',
                  }}>
                    Back to Login
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View>
                <TextInput
                  placeholder="Enter your email"
                  placeholderTextColor="#777"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  style={{
                    width: '100%',
                    marginBottom: 16,
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                    borderRadius: 12,
                    backgroundColor: '#1C1C1E',
                    color: '#ffffff',
                    borderWidth: 1,
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    fontSize: 16,
                  }}
                />

                <TouchableOpacity
                  onPress={handleForgotPassword}
                  disabled={loading}
                  style={{
                    width: '100%',
                    backgroundColor: '#1C1C1E',
                    borderWidth: 1,
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    paddingVertical: 12,
                    borderRadius: 12,
                    opacity: loading ? 0.6 : 1,
                  }}
                >
                  <Text style={{
                    color: '#ffffff',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    fontSize: 16,
                  }}>
                    {loading ? 'Sending...' : 'Reset Password'}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};