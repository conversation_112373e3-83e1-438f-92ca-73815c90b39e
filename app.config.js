import 'dotenv/config';

export default {
  expo: {
    name: "Warfront Nations",
    slug: "warfront-nations-mobile",
    version: "1.0.0",
    orientation: "portrait",
    userInterfaceStyle: "light",
    scheme: "warfront-nations",
    platforms: ["ios", "android", "web"],
    splash: {
      backgroundColor: "#1a1a2e",
      resizeMode: "contain"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.warfrontnationsmobile"
    },
    android: {
      package: "com.warfrontnationsmobile",
      adaptiveIcon: {
        backgroundColor: "#1a1a2e"
      }
    },
    web: {
      bundler: "metro"
    },
    plugins: [
      "expo-maps",
      "expo-secure-store",
      "expo-web-browser"
    ],
    extra: {
      apiBaseUrl: process.env.API_BASE_URL || "http://localhost:3000",
      googleClientIdAndroid: process.env.GOOGLE_CLIENT_ID_ANDROID || "",
      googleClientIdIos: process.env.GOOGLE_CLIENT_ID_IOS || "",
      enableAnalytics: process.env.ENABLE_ANALYTICS || "false",
      buildType: process.env.BUILD_TYPE || "development",
      firebaseProjectId: process.env.FIREBASE_PROJECT_ID || "",
      eas: {
        projectId: "your-eas-project-id"
      }
    }
  }
};
