#!/usr/bin/env node

/**
 * Firebase Setup Assistant
 * Helps configure Firebase for the project
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function ask(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

async function main() {
  console.log('🔥 Firebase Setup Assistant');
  console.log('==========================\n');

  console.log('This script will help you configure Firebase for your Warfront Nations app.\n');

  // Collect Firebase project information
  console.log('📝 Please provide your Firebase project information:\n');

  const devProjectId = await ask('Development Firebase Project ID: ');
  const stagingProjectId = await ask('Staging Firebase Project ID: ');
  const prodProjectId = await ask('Production Firebase Project ID: ');
  
  console.log('\n📱 Google OAuth Configuration:\n');
  const googleClientId = await ask('Google OAuth Client ID: ');
  
  console.log('\n💳 Stripe Configuration:\n');
  const stripeTestKey = await ask('Stripe Test Publishable Key: ');
  const stripeLiveKey = await ask('Stripe Live Publishable Key: ');

  // Update environment files
  console.log('\n🔧 Updating environment files...');

  const projectRoot = path.join(__dirname, '..');

  // Update .env (development)
  const devEnv = `API_BASE_URL=http://localhost:3000

# Firebase Configuration
FIREBASE_PROJECT_ID=${devProjectId}
ENABLE_CRASHLYTICS=false
ENABLE_ANALYTICS=false

# Google Services
GOOGLE_CLIENT_ID=${googleClientId}

# Stripe Configuration  
STRIPE_PUBLISHABLE_KEY=${stripeTestKey}
`;

  // Update .env.staging
  const stagingEnv = `API_BASE_URL=https://staging-api.warfrontnations.com

# Firebase Configuration
FIREBASE_PROJECT_ID=${stagingProjectId}
ENABLE_CRASHLYTICS=true
ENABLE_ANALYTICS=true

# Google Services
GOOGLE_CLIENT_ID=${googleClientId}

# Stripe Configuration  
STRIPE_PUBLISHABLE_KEY=${stripeTestKey}
`;

  // Update .env.production
  const prodEnv = `API_BASE_URL=https://api.warfrontnations.com

# Firebase Configuration
FIREBASE_PROJECT_ID=${prodProjectId}
ENABLE_CRASHLYTICS=true
ENABLE_ANALYTICS=true

# Google Services
GOOGLE_CLIENT_ID=${googleClientId}

# Stripe Configuration  
STRIPE_PUBLISHABLE_KEY=${stripeLiveKey}
`;

  // Write files
  fs.writeFileSync(path.join(projectRoot, '.env'), devEnv);
  fs.writeFileSync(path.join(projectRoot, '.env.staging'), stagingEnv);
  fs.writeFileSync(path.join(projectRoot, '.env.production'), prodEnv);

  console.log('✅ Environment files updated!');

  // Create directory structure for google-services.json files
  const androidAppPath = path.join(projectRoot, 'android/app');
  const srcPaths = [
    path.join(androidAppPath, 'src/debug'),
    path.join(androidAppPath, 'src/staging'),
    path.join(androidAppPath, 'src/freeRelease'),
    path.join(androidAppPath, 'src/premiumRelease'),
  ];

  console.log('\n📁 Creating directory structure for Firebase config files...');
  srcPaths.forEach(srcPath => {
    if (!fs.existsSync(srcPath)) {
      fs.mkdirSync(srcPath, { recursive: true });
      console.log(`✅ Created: ${srcPath}`);
    }
  });

  // Create Firebase configuration guide
  const firebaseGuide = `
# 🔥 Firebase Configuration Guide

## Next Steps:

### 1. Download Google Services Files

Go to Firebase Console for each project and download google-services.json:

**Development Project (${devProjectId}):**
- Download google-services.json for package: com.warfrontnations.mobile.debug
- Place in: android/app/src/debug/google-services.json

**Staging Project (${stagingProjectId}):**
- Download google-services.json for package: com.warfrontnations.mobile.staging  
- Place in: android/app/src/staging/google-services.json

**Production Free (${prodProjectId}):**
- Download google-services.json for package: com.warfrontnations.mobile.free
- Place in: android/app/src/freeRelease/google-services.json

**Production Premium (${prodProjectId}):**
- Download google-services.json for package: com.warfrontnations.mobile
- Place in: android/app/src/premiumRelease/google-services.json

### 2. Enable Firebase Services

For each Firebase project, enable:
- ✅ Authentication → Google Sign-in
- ✅ Cloud Messaging (FCM)
- ✅ Analytics
- ✅ Crashlytics

### 3. Configure Backend

Add Firebase Admin SDK to your backend:
\`\`\`bash
npm install firebase-admin
\`\`\`

Download service account keys from Firebase Console → Project Settings → Service Accounts

### 4. Test the Setup

Run the test script:
\`\`\`bash
node scripts/test-firebase.js
\`\`\`

### 5. Build and Test

\`\`\`bash
# Test development build
npm run android

# Test staging build  
cd android && ./gradlew assembleStaging

# Test production build
./scripts/build-release.sh
\`\`\`

## Troubleshooting

If you encounter issues:
1. Make sure all google-services.json files are in place
2. Clean and rebuild: cd android && ./gradlew clean
3. Check Firebase Console for correct package names
4. Verify environment variables are set correctly

## Firebase Console URLs

- Development: https://console.firebase.google.com/project/${devProjectId}
- Staging: https://console.firebase.google.com/project/${stagingProjectId}  
- Production: https://console.firebase.google.com/project/${prodProjectId}
`;

  fs.writeFileSync(path.join(projectRoot, 'FIREBASE_SETUP.md'), firebaseGuide);

  console.log('\n🎉 Firebase setup completed!');
  console.log('\n📚 Next steps saved to: FIREBASE_SETUP.md');
  console.log('\n🔍 Run this to verify setup: node scripts/test-firebase.js');

  rl.close();
}

main().catch(console.error);