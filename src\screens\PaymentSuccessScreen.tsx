import React, { useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { CheckCircle, Crown, ArrowRight, Coins, Gift } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
  route: any;
}

interface PlanInfo {
  name: string;
  price: string;
  period: string;
}

interface GoldInfo {
  name: string;
  amount: number;
  bonus: number;
  price: string;
}

export const PaymentSuccessScreen: React.FC<Props> = ({ navigation, route }) => {
  const { refreshUser } = useAuthStore();
  useAuthGuard({ navigation });

  // Get payment details from route parameters
  const paymentType = route?.params?.type || 'subscription';
  const plan = route?.params?.plan || 'premium';
  const goldPackage = route?.params?.package;
  const goldAmount = route?.params?.amount;
  const giftedToUserId = route?.params?.giftedToUserId;
  const giftedToUsername = route?.params?.giftedToUsername;
  const durationDays = route?.params?.durationDays;

  // Get plan display information
  const getPlanInfo = (): PlanInfo => {
    switch(plan) {
      case 'monthly':
        return { name: 'Monthly Premium', price: '€2.00', period: 'per month' };
      case 'semiannual':
        return { name: '6-Month Premium', price: '€9.00', period: 'per 6 months' };
      case 'yearly':
        return { name: 'Yearly Premium', price: '€12.00', period: 'per year' };
      default:
        return { name: 'Premium', price: '', period: '' };
    }
  };

  // Get gold package display information
  const getGoldPackageInfo = (): GoldInfo => {
    switch(goldPackage) {
      case 'small':
        return { name: 'Starter Pack', amount: 1000, bonus: 100, price: '€4.99' };
      case 'medium':
        return { name: 'Advanced Pack', amount: 3000, bonus: 500, price: '€9.99' };
      case 'large':
        return { name: 'Elite Pack', amount: 10000, bonus: 2000, price: '€24.99' };
      case 'extra_large':
        return { name: 'Ultimate Pack', amount: 25000, bonus: 5000, price: '€49.99' };
      default:
        return { name: 'Gold Package', amount: goldAmount ? parseInt(goldAmount) : 0, bonus: 0, price: '' };
    }
  };

  const planInfo = getPlanInfo();
  const goldInfo = getGoldPackageInfo();

  useEffect(() => {
    // Show success message
    Toast.show({
      type: 'success',
      text1: 'Payment Successful!',
      text2: 'Your purchase has been processed successfully.',
    });

    // Refresh user data to reflect new subscription status
    refreshUser();

    // If no parameters are provided, redirect to home after 5 seconds
    if (!paymentType) {
      const timer = setTimeout(() => {
        navigation.navigate('Home');
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [refreshUser, navigation, paymentType]);

  const renderSubscriptionSuccess = () => (
    <>
      <Text style={{ fontSize: 20, color: '#D1D5DB', marginBottom: 8, textAlign: 'center' }}>
        Thank you for subscribing to Warfront Nations Premium!
      </Text>
      <Text style={{ fontSize: 18, color: '#3f87ff', marginBottom: 24, textAlign: 'center' }}>
        {planInfo.name} - {planInfo.price} {planInfo.period}
      </Text>

      <View style={{
        backgroundColor: '#374151',
        borderRadius: 12,
        padding: 24,
        marginBottom: 32,
        alignItems: 'center',
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
          <Crown width={24} height={24} color="#F59E0B" />
          <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', marginLeft: 8 }}>
            Premium Benefits Activated
          </Text>
        </View>
        
        <View style={{ alignSelf: 'stretch', gap: 12 }}>
          {[
            '50% faster training times',
            'Auto-mode in wars',
            'Premium badge',
            'Priority customer support'
          ].map((benefit, index) => (
            <View key={index} style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
              <Text style={{ color: '#10B981', marginRight: 8, fontSize: 16 }}>✓</Text>
              <Text style={{ color: '#D1D5DB', fontSize: 16 }}>{benefit}</Text>
            </View>
          ))}
        </View>
      </View>
    </>
  );

  const renderGiftPremiumSuccess = () => (
    <>
      <Text style={{ fontSize: 20, color: '#D1D5DB', marginBottom: 8, textAlign: 'center' }}>
        Premium gift sent successfully!
      </Text>
      <Text style={{ fontSize: 18, color: '#3f87ff', marginBottom: 24, textAlign: 'center' }}>
        {planInfo.name} - {planInfo.price}
      </Text>

      <View style={{
        backgroundColor: '#374151',
        borderRadius: 12,
        padding: 24,
        marginBottom: 32,
        alignItems: 'center',
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
          <Gift width={24} height={24} color="#F59E0B" />
          <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', marginLeft: 8 }}>
            Gift Premium Delivered
          </Text>
        </View>
        
        <View style={{ alignItems: 'center', marginBottom: 16 }}>
          <Text style={{ color: '#D1D5DB', marginBottom: 8, textAlign: 'center' }}>
            Gifted to: <Text style={{ color: '#ffffff', fontWeight: '600' }}>
              {giftedToUsername || 'Unknown User'}
            </Text>
          </Text>
          <Text style={{ color: '#D1D5DB', textAlign: 'center' }}>
            Duration: <Text style={{ color: '#ffffff', fontWeight: '600' }}>
              {durationDays || '30'} days
            </Text>
          </Text>
        </View>
        
        <Text style={{ color: '#D1D5DB', textAlign: 'center', fontSize: 14 }}>
          The premium benefits have been applied to the recipient's account immediately.
        </Text>
      </View>
    </>
  );

  const renderGoldSuccess = () => (
    <>
      <Text style={{ fontSize: 20, color: '#D1D5DB', marginBottom: 8, textAlign: 'center' }}>
        Your gold purchase has been processed successfully and added to your account!
      </Text>
      <Text style={{ fontSize: 18, color: '#3f87ff', marginBottom: 24, textAlign: 'center' }}>
        {goldInfo.name} - {goldInfo.price}
      </Text>

      <View style={{
        backgroundColor: '#374151',
        borderRadius: 12,
        padding: 24,
        marginBottom: 32,
        alignItems: 'center',
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
          <Coins width={24} height={24} color="#F59E0B" />
          <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', marginLeft: 8 }}>
            Gold Added to Your Account
          </Text>
        </View>
        
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginBottom: 16 }}>
          <Text style={{ fontSize: 32, marginRight: 8 }}>🪙</Text>
          <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#ffffff' }}>
            {goldInfo.amount.toLocaleString()}
          </Text>
          {goldInfo.bonus > 0 && (
            <View style={{
              backgroundColor: '#065F46',
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 4,
              marginLeft: 8,
            }}>
              <Text style={{ color: '#10B981', fontSize: 12, fontWeight: '600' }}>
                +{goldInfo.bonus} BONUS
              </Text>
            </View>
          )}
        </View>
        
        <Text style={{ color: '#D1D5DB', textAlign: 'center', fontSize: 14 }}>
          You can use gold to accelerate training, purchase special items, and gain advantages in various game aspects.
        </Text>
      </View>
    </>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#111827' }}>
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {/* Logo */}
        <View style={{ alignItems: 'center', paddingTop: 60, marginBottom: 40 }}>
          <Image
            source={require('../../assets/images/wn-logo.png')}
            style={{ width: 80, height: 80, marginBottom: 16 }}
            resizeMode="contain"
          />
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff' }}>
            WARFRONT NATIONS
          </Text>
        </View>

        <View style={{ paddingHorizontal: 24 }}>
          {/* Success Icon and Title */}
          <View style={{ alignItems: 'center', marginBottom: 32 }}>
            <CheckCircle width={80} height={80} color="#10B981" />
            <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#ffffff', marginTop: 16, textAlign: 'center' }}>
              Payment Successful!
            </Text>
          </View>

          {/* Payment Type Specific Content */}
          {paymentType === 'subscription' && renderSubscriptionSuccess()}
          {paymentType === 'gift_premium' && renderGiftPremiumSuccess()}
          {paymentType === 'gold' && renderGoldSuccess()}

          {/* Action Buttons */}
          <View style={{ gap: 12, marginBottom: 40 }}>
            <TouchableOpacity
              onPress={() => navigation.navigate('Home')}
              style={{
                backgroundColor: '#3f87ff',
                paddingVertical: 16,
                borderRadius: 8,
                alignItems: 'center',
              }}
            >
              <Text style={{ color: '#ffffff', fontSize: 18, fontWeight: '600' }}>
                Return to Dashboard
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => navigation.navigate('ProfileScreen')}
              style={{
                backgroundColor: '#374151',
                paddingVertical: 16,
                borderRadius: 8,
                alignItems: 'center',
                flexDirection: 'row',
                justifyContent: 'center',
              }}
            >
              <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '500' }}>
                View Profile
              </Text>
              <ArrowRight width={20} height={20} color="#ffffff" style={{ marginLeft: 8 }} />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};