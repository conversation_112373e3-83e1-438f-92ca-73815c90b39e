import { api } from './api';
import { User, UserProfile } from '../types/user';

export interface UpdateProfileDto {
  id: number;
  username?: string;
  aboutMe?: string;
  avatarUrl?: string;
}

export interface TrainingData {
  trainingType: "strength" | "intelligence" | "endurance";
  duration: number;
  currency: "gold" | "money";
  amount: number;
}

interface TrainResponse {
  success: boolean;
  user: User;
  message: string;
}

interface TransferMoneyRequest {
  recipientId?: number;
  fromUserId?: number;
  toUserId?: number;
  amount: number;
}

interface MessageRequest {
  content: string;
}

export interface UploadAvatarResponse {
  avatarUrl: string;
  key: string;
  size: number;
}

export const userService = {
  // User data
  getAllUsers: async (): Promise<User[]> => {
    const response = await api.get('/users');
    return response.data;
  },

  getAllUsersCount: async (): Promise<number> => {
    const response = await api.get('/users/count');
    return response.data;
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await api.get('/users/profile');
    return response.data;
  },

  getUserById: async (id: string | number): Promise<UserProfile | null> => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  getCurrentUserProfile: async (): Promise<UserProfile> => {
    const response = await api.get('/users/profile');
    return response.data;
  },

  getOtherUserProfile: async (id: string | number): Promise<Omit<UserProfile, 'aboutMe'>> => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  updateProfile: async (userData: UpdateProfileDto): Promise<User> => {
    const response = await api.patch(`/users/${userData.id}`, userData);
    return response.data;
  },

  // Training and skills (with both new and old API support)
  trainUser: async (trainingData: TrainingData): Promise<User> => {
    const response = await api.post('/users/train', trainingData);
    return response.data;
  },

  // Simple train API (matching frontend)
  trainSkill: async (skill: 'strength' | 'intelligence' | 'endurance'): Promise<TrainResponse> => {
    const response = await api.post('/users/train', { skill });
    return response.data;
  },

  // Money transfers (supporting both patterns)
  sendMoney: async (transferData: TransferMoneyRequest): Promise<void> => {
    await api.post('/users/transfer-money', transferData);
  },

  // Transfer money (simplified frontend pattern)
  transferMoney: async (recipientId: number, amount: number): Promise<void> => {
    await api.post('/users/transfer-money', { recipientId, amount });
  },

  moveToRegion: async (regionId: string): Promise<User> => {
    const response = await api.post('/users/move-region', { regionId });
    return response.data;
  },

  // Avatar management
  uploadAvatar: async (imageFile: FormData): Promise<UploadAvatarResponse> => {
    const response = await api.post('/users/avatar', imageFile, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  deleteAvatar: async (): Promise<User> => {
    const response = await api.delete('/users/avatar');
    return response.data;
  },

  // Messaging
  sendMessage: async (recipientId: number, content: string): Promise<void> => {
    await api.post(`/messages/send/${recipientId}`, { content } as MessageRequest);
  },

  // War analytics
  getUserWarAnalytics: async (userId?: number): Promise<any> => {
    const url = userId ? `/wars/analytics/user/${userId}` : '/wars/analytics/user';
    const response = await api.get(url);
    return response.data;
  },
};

export default userService;