import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  TouchableOpacity,
  Image,
} from 'react-native';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react-native';
import { authAPI } from '../services/api';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
  route: any;
}

type VerificationStatus = 'verifying' | 'success' | 'failed' | 'invalid';

export const VerifyAccountScreen: React.FC<Props> = ({ navigation, route }) => {
  const [status, setStatus] = useState<VerificationStatus>('verifying');
  const token = route?.params?.token;

  useEffect(() => {
    if (!token) {
      setStatus('invalid');
      return;
    }

    const verify = async () => {
      try {
        await authAPI.verifyAccount(token);
        setStatus('success');
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Account verified! You can now log in.',
        });
        
        // Redirect to login after 3 seconds
        setTimeout(() => {
          navigation.navigate('Login');
        }, 3000);
      } catch (error: any) {
        console.error('Verification error:', error);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: error.response?.data?.message || 'Verification failed',
        });
        setStatus('failed');
      }
    };

    verify();
  }, [token, navigation]);

  const renderIcon = () => {
    switch (status) {
      case 'verifying':
        return <ActivityIndicator size="large" color="#3f87ff" />;
      case 'success':
        return <CheckCircle width={80} height={80} color="#10B981" />;
      case 'failed':
        return <XCircle width={80} height={80} color="#EF4444" />;
      case 'invalid':
        return <AlertCircle width={80} height={80} color="#F59E0B" />;
    }
  };

  const renderMessage = () => {
    switch (status) {
      case 'verifying':
        return {
          title: 'Verifying Account',
          message: 'Please wait while we verify your account...',
        };
      case 'success':
        return {
          title: 'Account Verified!',
          message: 'Your account has been successfully verified. Redirecting to login...',
        };
      case 'failed':
        return {
          title: 'Verification Failed',
          message: 'The verification link is invalid or has expired. Please try registering again.',
        };
      case 'invalid':
        return {
          title: 'Invalid Link',
          message: 'The verification link is invalid. Please check your email for the correct link.',
        };
    }
  };

  const messageData = renderMessage();

  return (
    <View style={{
      flex: 1,
      backgroundColor: '#0F0F10',
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 24,
    }}>
      {/* Logo */}
      <View style={{ marginBottom: 40, alignItems: 'center' }}>
        <Image
          source={require('../../assets/images/wn-logo.png')}
          style={{
            width: 80,
            height: 80,
            marginBottom: 16,
          }}
          resizeMode="contain"
        />
        <Text style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: '#ffffff',
          textAlign: 'center',
        }}>
          WARFRONT NATIONS
        </Text>
      </View>

      {/* Verification Status */}
      <View style={{
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderRadius: 16,
        padding: 40,
        alignItems: 'center',
        width: '100%',
        maxWidth: 400,
      }}>
        <View style={{ marginBottom: 24 }}>
          {renderIcon()}
        </View>

        <Text style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: '#ffffff',
          textAlign: 'center',
          marginBottom: 16,
        }}>
          {messageData.title}
        </Text>

        <Text style={{
          fontSize: 16,
          color: '#A1A1A1',
          textAlign: 'center',
          lineHeight: 24,
          marginBottom: 32,
        }}>
          {messageData.message}
        </Text>

        {/* Action Buttons */}
        <View style={{ width: '100%' }}>
          {status === 'success' && (
            <TouchableOpacity
              onPress={() => navigation.navigate('Login')}
              style={{
                backgroundColor: '#10B981',
                paddingVertical: 12,
                borderRadius: 8,
                alignItems: 'center',
                marginBottom: 12,
              }}
            >
              <Text style={{
                color: '#ffffff',
                fontSize: 16,
                fontWeight: '600',
              }}>
                Go to Login
              </Text>
            </TouchableOpacity>
          )}

          {(status === 'failed' || status === 'invalid') && (
            <>
              <TouchableOpacity
                onPress={() => navigation.navigate('Register')}
                style={{
                  backgroundColor: '#3f87ff',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                  marginBottom: 12,
                }}
              >
                <Text style={{
                  color: '#ffffff',
                  fontSize: 16,
                  fontWeight: '600',
                }}>
                  Try Registration Again
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => navigation.navigate('Login')}
                style={{
                  backgroundColor: 'transparent',
                  borderWidth: 1,
                  borderColor: 'rgba(255, 255, 255, 0.2)',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  color: '#A1A1A1',
                  fontSize: 16,
                }}>
                  Back to Login
                </Text>
              </TouchableOpacity>
            </>
          )}

          {status === 'verifying' && (
            <TouchableOpacity
              onPress={() => navigation.navigate('Login')}
              style={{
                backgroundColor: 'transparent',
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.2)',
                paddingVertical: 12,
                borderRadius: 8,
                alignItems: 'center',
              }}
            >
              <Text style={{
                color: '#A1A1A1',
                fontSize: 16,
              }}>
                Back to Login
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};