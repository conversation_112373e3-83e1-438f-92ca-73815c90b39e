import { api } from './api';

export interface GoldPackage {
  id: string;
  name: string;
  goldAmount: number;
  price: number; // in cents
  currency: string;
  bonus?: number;
  popular?: boolean;
}

export interface PremiumPlan {
  id: string;
  name: string;
  price: number; // in cents
  currency: string;
  interval: 'month' | 'year';
  features: string[];
}

export interface CheckoutSession {
  sessionId: string;
  url: string;
}

export interface PaymentIntent {
  clientSecret: string;
  amount: number;
  currency: string;
}

export interface SubscriptionStatus {
  isActive: boolean;
  planId?: string;
  planName?: string;
  currentPeriodStart?: string;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
  trialEnd?: string;
}

export interface TrialEligibility {
  eligible: boolean;
  reason?: string;
  trialDays?: number;
}

export const stripeService = {
  // Gold purchases
  createGoldCheckoutSession: async (goldPackageId: string): Promise<CheckoutSession> => {
    const response = await api.post('/payments/gold/create-session', { package: goldPackageId });
    return response.data;
  },

  createGoldPaymentIntent: async (goldPackageId: string): Promise<PaymentIntent> => {
    const response = await api.post('/payments/gold/create-intent', { package: goldPackageId });
    return response.data;
  },

  // Premium subscriptions
  createPremiumCheckoutSession: async (planId: string, trialDays?: number): Promise<CheckoutSession> => {
    const response = await api.post('/payments/premium/create-session', { plan: planId, trialDays });
    return response.data;
  },

  verifyPremiumSubscription: async (): Promise<SubscriptionStatus> => {
    const response = await api.get('/payments/premium/verify');
    return response.data;
  },

  cancelPremiumSubscription: async (): Promise<SubscriptionStatus> => {
    const response = await api.post('/payments/premium/cancel');
    return response.data;
  },

  // Trial eligibility
  checkTrialEligibility: async (): Promise<TrialEligibility> => {
    const response = await api.get('/payments/trial-eligibility');
    return response.data;
  },

  // Gift premium
  createGiftPremiumSession: async (recipientId: number, planId: string, months: number): Promise<CheckoutSession> => {
    const response = await api.post('/payments/premium/gift', { recipientId, plan: planId, months });
    return response.data;
  },

  // Available packages/plans (these might be hardcoded or come from another endpoint)
  getGoldPackages: (): GoldPackage[] => {
    return [
      {
        id: 'gold_100',
        name: '100 Gold',
        goldAmount: 100,
        price: 199, // $1.99
        currency: 'usd',
      },
      {
        id: 'gold_500',
        name: '500 Gold',
        goldAmount: 500,
        price: 899, // $8.99
        currency: 'usd',
        bonus: 50,
      },
      {
        id: 'gold_1000',
        name: '1000 Gold',
        goldAmount: 1000,
        price: 1599, // $15.99
        currency: 'usd',
        bonus: 150,
        popular: true,
      },
      {
        id: 'gold_2500',
        name: '2500 Gold',
        goldAmount: 2500,
        price: 3999, // $39.99
        currency: 'usd',
        bonus: 500,
      },
    ];
  },

  getPremiumPlans: (): PremiumPlan[] => {
    return [
      {
        id: 'premium_monthly',
        name: 'Premium Monthly',
        price: 999, // $9.99
        currency: 'usd',
        interval: 'month',
        features: [
          'No ads',
          'Premium support',
          'Exclusive features',
          'Priority queue',
        ],
      },
      {
        id: 'premium_yearly',
        name: 'Premium Yearly',
        price: 9999, // $99.99
        currency: 'usd',
        interval: 'year',
        features: [
          'No ads',
          'Premium support',
          'Exclusive features',
          'Priority queue',
          '2 months free',
        ],
      },
    ];
  },
};