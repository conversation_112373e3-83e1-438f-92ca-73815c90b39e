import React, { useEffect } from 'react';
import { Platform } from 'react-native';
import Toast from 'react-native-toast-message';
import { AppNavigator } from './src/navigation/AppNavigator';

// Import CSS for NativeWind support
import './global.css';

// Configure Reanimated logger if available
try {
  const { configureReanimatedLogger, ReanimatedLogLevel } = require('react-native-reanimated');
  if (configureReanimatedLogger && ReanimatedLogLevel) {
    configureReanimatedLogger({
      level: ReanimatedLogLevel.warn,
      strict: false, // Reanimated runs in strict mode by default
    });
  }
} catch (error) {
  // Reanimated logger not available, continue without it
  console.log('Reanimated logger not available');
}

function App() {
  useEffect(() => {
    // Fix NativeWind color scheme configuration for web
    if (typeof window !== 'undefined') {
      try {
        // @ts-ignore
        if (window.StyleSheet?.setFlag) {
          // @ts-ignore
          window.StyleSheet.setFlag('darkMode', 'class');
        }
      } catch (error) {
        console.warn('Could not configure NativeWind color scheme:', error);
      }
    }
  }, []);

  return (
    <>
      <AppNavigator />
      <Toast />
    </>
  );
}

export default App;
