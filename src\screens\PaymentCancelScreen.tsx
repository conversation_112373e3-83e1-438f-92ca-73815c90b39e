import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { XCircle, ArrowLeft, HelpCircle } from 'lucide-react-native';
import { useAuthGuard } from '../hooks/useAuthGuard';

interface Props {
  navigation: any;
  route: any;
}

export const PaymentCancelScreen: React.FC<Props> = ({ navigation, route }) => {
  useAuthGuard({ navigation });

  // Get payment details from route parameters
  const paymentType = route?.params?.type || 'subscription';
  const plan = route?.params?.plan || 'premium';
  const goldPackage = route?.params?.package;

  const getPaymentTypeName = () => {
    switch(paymentType) {
      case 'subscription':
        return 'Premium Subscription';
      case 'gift_premium':
        return 'Premium Gift';
      case 'gold':
        return 'Gold Purchase';
      default:
        return 'Payment';
    }
  };

  const getPaymentDetails = () => {
    if (paymentType === 'subscription' || paymentType === 'gift_premium') {
      switch(plan) {
        case 'monthly':
          return 'Monthly Premium (€2.00)';
        case 'semiannual':
          return '6-Month Premium (€9.00)';
        case 'yearly':
          return 'Yearly Premium (€12.00)';
        default:
          return 'Premium Plan';
      }
    } else if (paymentType === 'gold') {
      switch(goldPackage) {
        case 'small':
          return 'Starter Pack (€4.99)';
        case 'medium':
          return 'Advanced Pack (€9.99)';
        case 'large':
          return 'Elite Pack (€24.99)';
        case 'extra_large':
          return 'Ultimate Pack (€49.99)';
        default:
          return 'Gold Package';
      }
    }
    return '';
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#111827' }}>
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {/* Logo */}
        <View style={{ alignItems: 'center', paddingTop: 60, marginBottom: 40 }}>
          <Image
            source={require('../../assets/images/wn-logo.png')}
            style={{ width: 80, height: 80, marginBottom: 16 }}
            resizeMode="contain"
          />
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff' }}>
            WARFRONT NATIONS
          </Text>
        </View>

        <View style={{ paddingHorizontal: 24 }}>
          {/* Cancel Icon and Title */}
          <View style={{ alignItems: 'center', marginBottom: 32 }}>
            <XCircle width={80} height={80} color="#EF4444" />
            <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#ffffff', marginTop: 16, textAlign: 'center' }}>
              Payment Cancelled
            </Text>
          </View>

          {/* Cancel Message */}
          <View style={{ marginBottom: 32 }}>
            <Text style={{ fontSize: 20, color: '#D1D5DB', textAlign: 'center', marginBottom: 16 }}>
              Your payment process was cancelled. No charges have been made.
            </Text>
            
            {paymentType && (
              <Text style={{ fontSize: 16, color: '#9CA3AF', textAlign: 'center' }}>
                Cancelled: {getPaymentTypeName()} - {getPaymentDetails()}
              </Text>
            )}
          </View>

          {/* Common Reasons Card */}
          <View style={{
            backgroundColor: '#374151',
            borderRadius: 12,
            padding: 24,
            marginBottom: 32,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16, justifyContent: 'center' }}>
              <HelpCircle width={24} height={24} color="#F59E0B" />
              <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', marginLeft: 8 }}>
                Common Reasons for Cancellation
              </Text>
            </View>
            
            <View style={{ gap: 12 }}>
              {[
                'Payment method declined',
                'Manually cancelled during checkout',
                'Connection issues during payment',
                'Insufficient funds',
                'Browser or app session timeout',
                'Changed mind about purchase'
              ].map((reason, index) => (
                <View key={index} style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                  <Text style={{ color: '#EF4444', marginRight: 8, fontSize: 16 }}>•</Text>
                  <Text style={{ color: '#D1D5DB', fontSize: 16, flex: 1 }}>{reason}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Help Section */}
          <View style={{
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 1,
            borderColor: 'rgba(59, 130, 246, 0.3)',
            borderRadius: 12,
            padding: 20,
            marginBottom: 32,
          }}>
            <Text style={{ fontSize: 18, fontWeight: '600', color: '#3f87ff', marginBottom: 12, textAlign: 'center' }}>
              Need Help?
            </Text>
            <Text style={{ color: '#D1D5DB', textAlign: 'center', lineHeight: 24 }}>
              If you're experiencing issues with payments or have questions about our premium features, 
              please don't hesitate to contact our support team.
            </Text>
          </View>

          {/* Action Buttons */}
          <View style={{ gap: 12, marginBottom: 40 }}>
            <TouchableOpacity
              onPress={() => navigation.navigate('Shop')}
              style={{
                backgroundColor: '#3f87ff',
                paddingVertical: 16,
                borderRadius: 8,
                alignItems: 'center',
                flexDirection: 'row',
                justifyContent: 'center',
              }}
            >
              <ArrowLeft width={20} height={20} color="#ffffff" />
              <Text style={{ color: '#ffffff', fontSize: 18, fontWeight: '600', marginLeft: 8 }}>
                Try Again
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => navigation.navigate('Home')}
              style={{
                backgroundColor: '#374151',
                paddingVertical: 16,
                borderRadius: 8,
                alignItems: 'center',
              }}
            >
              <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '500' }}>
                Return to Dashboard
              </Text>
            </TouchableOpacity>

            {/* Contact Support Button */}
            <TouchableOpacity
              onPress={() => {
                // In a real app, this would open support chat or email
                navigation.navigate('ChatList');
              }}
              style={{
                backgroundColor: 'transparent',
                borderWidth: 1,
                borderColor: '#4B5563',
                paddingVertical: 12,
                borderRadius: 8,
                alignItems: 'center',
              }}
            >
              <Text style={{ color: '#9CA3AF', fontSize: 16 }}>
                Contact Support
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};