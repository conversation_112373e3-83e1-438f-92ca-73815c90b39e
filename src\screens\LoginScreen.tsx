import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
  ScrollView,
} from 'react-native';
import { Eye, EyeOff } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { GoogleSignInButton } from '../components/GoogleSignInButton';
import { authAPI } from '../services/api';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

export const LoginScreen: React.FC<Props> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showResendButton, setShowResendButton] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const { login, isLoading } = useAuthStore();

  const handleResendVerification = async () => {
    if (!email) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please enter your email address first',
      });
      return;
    }

    setResendLoading(true);
    try {
      await authAPI.resendVerification(email);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Verification email sent successfully!',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to send verification email',
      });
    } finally {
      setResendLoading(false);
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please fill in all fields',
      });
      return;
    }

    setShowResendButton(false);

    try {
      await login(email, password);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Logged in successfully!',
      });
    } catch (error: any) {
      // Check if the error is about account not being verified
      if (error.message?.includes('not yet activated') || 
          error.message?.includes('check your mail for activation')) {
        setShowResendButton(true);
      }
      Toast.show({
        type: 'error',
        text1: 'Login Failed',
        text2: error.message || 'Please check your credentials',
      });
    }
  };

  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
        backgroundColor: '#0F0F10',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 16,
      }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
        showsVerticalScrollIndicator={false}
        style={{ width: '100%' }}
      >
        <View style={{
          width: '100%',
          maxWidth: 400,
          alignSelf: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: 16,
          padding: 32,
        }}>
          <View style={{ alignItems: 'center', marginBottom: 24 }}>
            <TouchableOpacity onPress={() => navigation.navigate('Landing')}>
              <Image
                source={require('../../assets/images/wn-logo.png')}
                style={{
                  width: 56,
                  height: 56,
                  marginBottom: 16,
                }}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#ffffff',
              textAlign: 'center',
            }}>
              LOGIN
            </Text>
            <Text style={{
              marginTop: 4,
              fontSize: 14,
              color: '#A1A1A1',
              textAlign: 'center',
            }}>
              Some nations rise, others fall... but you lead them all
            </Text>
          </View>

          <View>
            <TextInput
              placeholder="Email"
              placeholderTextColor="#777"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              style={{
                width: '100%',
                marginBottom: 16,
                paddingHorizontal: 16,
                paddingVertical: 12,
                borderRadius: 12,
                backgroundColor: '#1C1C1E',
                color: '#ffffff',
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.1)',
                fontSize: 16,
              }}
            />

            <View style={{ position: 'relative', marginBottom: 16 }}>
              <TextInput
                placeholder="Password"
                placeholderTextColor="#777"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                style={{
                  width: '100%',
                  paddingHorizontal: 16,
                  paddingVertical: 12,
                  paddingRight: 48,
                  borderRadius: 12,
                  backgroundColor: '#1C1C1E',
                  color: '#ffffff',
                  borderWidth: 1,
                  borderColor: 'rgba(255, 255, 255, 0.1)',
                  fontSize: 16,
                }}
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                style={{
                  position: 'absolute',
                  right: 16,
                  top: 14,
                }}
              >
                {showPassword ? (
                  <EyeOff width={20} height={20} color="#888" />
                ) : (
                  <Eye width={20} height={20} color="#888" />
                )}
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              onPress={handleLogin}
              disabled={isLoading}
              style={{
                width: '100%',
                backgroundColor: '#1C1C1E',
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.2)',
                paddingVertical: 12,
                borderRadius: 12,
                opacity: isLoading ? 0.6 : 1,
              }}
            >
              <Text style={{
                color: '#ffffff',
                fontWeight: 'bold',
                textAlign: 'center',
                fontSize: 16,
              }}>
                {isLoading ? 'Loading...' : 'LOGIN'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* OAuth Divider */}
          <View style={{
            position: 'relative',
            marginVertical: 24,
            alignItems: 'center',
          }}>
            <View style={{
              position: 'absolute',
              top: '50%',
              left: 0,
              right: 0,
              height: 1,
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            }} />
            <View style={{
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              paddingHorizontal: 8,
            }}>
              <Text style={{
                color: '#A1A1A1',
                textAlign: 'center',
                fontSize: 14,
              }}>
                Or
              </Text>
            </View>
          </View>

          {/* Google Sign-In Button */}
          <GoogleSignInButton 
            onSuccess={() => {
              Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Logged in successfully!',
              });
            }}
          />

          {showResendButton && (
            <View style={{
              marginTop: 16,
              padding: 16,
              backgroundColor: '#1C1C1E',
              borderWidth: 1,
              borderColor: 'rgba(255, 107, 107, 0.2)',
              borderRadius: 12,
            }}>
              <Text style={{
                fontSize: 14,
                color: '#A1A1A1',
                marginBottom: 12,
                textAlign: 'center',
              }}>
                Your account needs to be verified. Check your email for the
                verification link.
              </Text>
              <TouchableOpacity
                onPress={handleResendVerification}
                disabled={resendLoading}
                style={{
                  width: '100%',
                  backgroundColor: 'rgba(255, 107, 107, 0.2)',
                  borderWidth: 1,
                  borderColor: 'rgba(255, 107, 107, 0.3)',
                  paddingVertical: 8,
                  borderRadius: 8,
                  opacity: resendLoading ? 0.6 : 1,
                }}
              >
                <Text style={{
                  color: '#FF6B6B',
                  fontWeight: 'bold',
                  textAlign: 'center',
                }}>
                  {resendLoading ? 'Sending...' : 'Resend Verification Email'}
                </Text>
              </TouchableOpacity>
            </View>
          )}

          <View style={{ marginTop: 24, alignItems: 'center' }}>
            <TouchableOpacity
              onPress={() => navigation.navigate('ForgotPassword')}
              style={{ marginBottom: 12 }}
            >
              <Text style={{
                color: '#A1A1A1',
                fontSize: 14,
                textAlign: 'center',
              }}>
                Forgot Password?
              </Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => navigation.navigate('Register')}>
              <Text style={{
                color: '#A1A1A1',
                fontSize: 14,
                textAlign: 'center',
              }}>
                Create New Account
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

