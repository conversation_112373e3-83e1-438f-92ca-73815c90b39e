import Toast from 'react-native-toast-message';

export function showErrorToast(error: any) {
  console.error('🚨 showErrorToast called with:', error);
  
  let message: string | string[];
  
  // Extract message from different error formats (matching your API structure)
  if (error?.response?.data?.message) {
    message = error.response.data.message;
    console.log('📝 Using response.data.message:', message);
  } else if (error?.data?.message) {
    message = error.data.message;
    console.log('📝 Using data.message:', message);
  } else if (error?.message) {
    message = error.message;
    console.log('📝 Using error.message:', message);
  } else if (typeof error === 'string') {
    message = error;
    console.log('📝 Using string error:', message);
  } else {
    message = "Something went wrong.";
    console.log('📝 Using fallback message');
  }

  // Handle array of messages
  if (Array.isArray(message)) {
    message.forEach((msg, index) => {
      setTimeout(() => {
        Toast.show({
          type: 'error',
          text1: '❌ Error',
          text2: msg
        });
      }, index * 100); // Stagger multiple toasts
    });
  } else {
    Toast.show({
      type: 'error',
      text1: '❌ Error',
      text2: message
    });
  }
}

export function showSuccessToast(message: string | string[]) {
  // Handle array of messages
  if (Array.isArray(message)) {
    message.forEach((msg, index) => {
      setTimeout(() => {
        Toast.show({
          type: 'success',
          text1: '✅ Success',
          text2: msg
        });
      }, index * 100); // Stagger multiple toasts
    });
  } else {
    Toast.show({
      type: 'success',
      text1: '✅ Success',
      text2: message || "Action completed successfully."
    });
  }
}

// Additional utility functions for specific use cases
export function showInfoToast(message: string) {
  Toast.show({
    type: 'info',
    text1: 'ℹ️ Info',
    text2: message
  });
}

export function showWarningToast(message: string) {
  Toast.show({
    type: 'error', // Using error type for warning since react-native-toast-message doesn't have warning
    text1: '⚠️ Warning',
    text2: message
  });
}