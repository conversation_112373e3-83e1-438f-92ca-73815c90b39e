import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Image,
} from "react-native";
import {
  MapPin,
  Users,
  Sword,
  Factory,
  MessageCircle,
  Plane,
  Vote,
  ArrowRight,
  Flag,
} from "lucide-react-native";
import { useAuthStore } from "../store/useAuthStore";
import { userService } from "../services/userService";
import { regionService } from "../services/regionService";
import { stateService } from "../services/stateService";
import { warService } from "../services/warService";
import { factoryService } from "../services/factoryService";
import { travelService } from "../services/travelService";
import { stateElectionService } from "../services/stateElectionService";
import { useAuthGuard } from "../hooks/useAuthGuard";
import Toast from "react-native-toast-message";
import SearchableModal from "../components/SearchableModal";
import { GeneralChatWidget } from "../components/GeneralChatWidget";
import { GlobalWarStats } from "../components/GlobalWarStats";
import { WarTimeline } from "../components/WarTimeline";
import { SearchableItem } from "../types/searchableModal";

interface Props {
  navigation: any;
}

interface Stats {
  totalPlayers: number;
  totalRegions: number;
  totalStates: number;
  activeWars: number;
  totalFactories: number;
}

interface GlobalWarStats {
  totalWars: number;
  activeWars: number;
  warTypes: {
    ground: number;
    sea: number;
    revolution: number;
  };
  warTargets: {
    conquest: number;
    resource: number;
  };
  mostActiveRegions: Array<{
    id: string;
    name: string;
    warCount: number;
  }>;
  mostActiveStates: Array<{
    id: string;
    name: string;
    warCount: number;
  }>;
}

export const HomeScreen: React.FC<Props> = ({ navigation }) => {
  const { user, logout } = useAuthStore();

  // Auth guard - redirect to login if not authenticated
  useAuthGuard({ navigation });

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<Stats>({
    totalPlayers: 0,
    totalRegions: 0,
    totalStates: 0,
    activeWars: 0,
    totalFactories: 0,
  });
  const [globalWarStats, setGlobalWarStats] = useState<GlobalWarStats | null>(
    null
  );
  const [myState, setMyState] = useState<any>(null);
  const [currentTravel, setCurrentTravel] = useState<any>(null);
  const [activeElection, setActiveElection] = useState<any>(null);
  const [activeWars, setActiveWars] = useState<any[]>([]);

  // Modal states
  const [playersModalOpen, setPlayersModalOpen] = useState(false);
  const [regionsModalOpen, setRegionsModalOpen] = useState(false);
  const [statesModalOpen, setStatesModalOpen] = useState(false);
  const [factoriesModalOpen, setFactoriesModalOpen] = useState(false);
  const [chatWidgetVisible, setChatWidgetVisible] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [playersData, setPlayersData] = useState<SearchableItem[]>([]);
  const [regionsData, setRegionsData] = useState<SearchableItem[]>([]);
  const [statesData, setStatesData] = useState<SearchableItem[]>([]);
  const [factoriesData, setFactoriesData] = useState<SearchableItem[]>([]);

  const fetchData = async () => {
    try {
      const [
        playersCount,
        regionsCount,
        statesCount,
        activeWarsData,
        factoriesData,
      ] = await Promise.all([
        userService.getAllUsersCount().catch(() => 0),
        regionService.getAllRegionsCount().catch(() => 0),
        stateService.getAllStatesCount().catch(() => 0),
        warService.getActiveWars().catch(() => []),
        factoryService.getAllFactories().catch(() => []),
      ]);

      setStats({
        totalPlayers: playersCount,
        totalRegions: regionsCount,
        totalStates: statesCount,
        activeWars: activeWarsData.length,
        totalFactories: factoriesData.length,
      });
      setActiveWars(activeWarsData);

      // Fetch global war statistics
      try {
        const warStats = await warService.getGlobalWarStats();

        // Calculate real war type statistics from active wars
        const warTypeStats = activeWarsData.reduce(
          (acc, war) => {
            switch (war.warType) {
              case "ground":
                acc.ground++;
                break;
              case "sea":
                acc.sea++;
                break;
              case "revolution":
                acc.revolution++;
                break;
              default:
                acc.ground++;
            }
            return acc;
          },
          { ground: 0, sea: 0, revolution: 0 }
        );

        // Calculate real war target statistics from active wars
        const warTargetStats = activeWarsData.reduce(
          (acc, war) => {
            switch (war.warTarget) {
              case "conquest":
                acc.conquest++;
                break;
              case "resources":
                acc.resource++;
                break;
              default:
                acc.conquest++;
            }
            return acc;
          },
          { conquest: 0, resource: 0 }
        );

        const realGlobalWarStats: GlobalWarStats = {
          totalWars: warStats.totalWars || 0,
          activeWars: activeWarsData.length,
          warTypes: warTypeStats,
          warTargets: warTargetStats,
          mostActiveRegions: [],
          mostActiveStates: [],
        };
        setGlobalWarStats(realGlobalWarStats);
      } catch (error) {
        console.error("Error fetching war stats:", error);
      }

      // War timeline is now handled by the WarTimeline component
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load dashboard data",
      });
    }
  };

  const fetchUserData = async () => {
    try {
      // Try to fetch user's state
      const stateData = await stateService.getMyState().catch(() => null);
      setMyState(stateData);

      // Try to fetch current travel
      const travelData = await travelService
        .getCurrentTravel()
        .catch(() => null);
      setCurrentTravel(travelData);

      // Try to fetch active election
      if (stateData && stateData.id) {
        const electionData = await stateElectionService
          .getActiveElection(stateData.id)
          .catch(() => null);
        setActiveElection(electionData);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  const loadAllData = async () => {
    setLoading(true);
    await Promise.all([fetchData(), fetchUserData()]);
    setLoading(false);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadAllData();
    setRefreshing(false);
  };

  useEffect(() => {
    loadAllData();
  }, []);

  // Modal data fetching functions
  const fetchPlayersData = async () => {
    setModalLoading(true);
    try {
      const players = await userService.getAllUsers();
      const playersFormatted: SearchableItem[] = players.map((player: any) => ({
        id: player.id,
        username: player.username,
        name: player.username,
        description: `Level ${player.level || 0} | Strength: ${
          player.strength || 0
        }`,
        location: player.region?.name || "No region",
      }));
      setPlayersData(playersFormatted);
    } catch (error) {
      console.error("Error fetching players:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load players data",
      });
    } finally {
      setModalLoading(false);
    }
  };

  const fetchRegionsData = async () => {
    setModalLoading(true);
    try {
      const regions = await regionService.getAllRegions();
      const regionsFormatted: SearchableItem[] = regions.map((region: any) => ({
        id: region.id,
        name: region.name,
        description: `Population: ${
          region.population?.toLocaleString() || "N/A"
        }`,
        location: region.state?.name || "No state",
      }));
      setRegionsData(regionsFormatted);
    } catch (error) {
      console.error("Error fetching regions:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load regions data",
      });
    } finally {
      setModalLoading(false);
    }
  };

  const fetchStatesData = async () => {
    setModalLoading(true);
    try {
      const states = await stateService.getAllStates();
      const statesFormatted: SearchableItem[] = states.map((state: any) => ({
        id: state.id,
        name: state.name,
        description: `Leader: ${
          state.leader?.username || "No leader"
        } | Treasury: ${state.treasury?.toLocaleString() || "0"}`,
        location: `${state.regions?.length || 0} regions`,
      }));
      setStatesData(statesFormatted);
    } catch (error) {
      console.error("Error fetching states:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load states data",
      });
    } finally {
      setModalLoading(false);
    }
  };

  const fetchFactoriesData = async () => {
    setModalLoading(true);
    try {
      const factories = await factoryService.getAllFactories();
      const factoriesFormatted: SearchableItem[] = factories.map(
        (factory: any) => ({
          id: factory.id,
          name: factory.name,
          title: factory.name,
          description: `Type: ${factory.type} | Wage: ${
            typeof factory.wage === "number" ? factory.wage.toFixed(2) : "0.00"
          } | Workers: ${factory.currentWorkers || 0}/${
            factory.maxWorkers || 0
          }`,
          location: factory.region?.name || "Unknown region",
          type: factory.type,
          wage: factory.wage,
          currentWorkers: factory.currentWorkers || 0,
          maxWorkers: factory.maxWorkers || 0,
          level: factory.level || 1,
          experience: factory.experience || 0,
        })
      );
      setFactoriesData(factoriesFormatted);
    } catch (error) {
      console.error("Error fetching factories:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load factories data",
      });
    } finally {
      setModalLoading(false);
    }
  };

  // Modal handlers
  const handlePlayersModalOpen = async () => {
    setPlayersModalOpen(true);
    if (playersData.length === 0) {
      await fetchPlayersData();
    }
  };

  const handleRegionsModalOpen = async () => {
    setRegionsModalOpen(true);
    if (regionsData.length === 0) {
      await fetchRegionsData();
    }
  };

  const handleStatesModalOpen = async () => {
    setStatesModalOpen(true);
    if (statesData.length === 0) {
      await fetchStatesData();
    }
  };

  const handleFactoriesModalOpen = async () => {
    setFactoriesModalOpen(true);
    if (factoriesData.length === 0) {
      await fetchFactoriesData();
    }
  };

  const handleItemClick = (item: SearchableItem, type: string) => {
    switch (type) {
      case "player":
        navigation.navigate("UserProfile", { userId: item.id });
        break;
      case "region":
        navigation.navigate("Regions", { regionId: item.id });
        break;
      case "state":
        navigation.navigate("StateBudget", { stateId: item.id });
        break;
      case "factory":
        navigation.navigate("Factories", { factoryId: item.id });
        break;
    }
  };

  const StatCard = ({
    title,
    value,
    icon,
    onPress,
    subtitle,
  }: {
    title: string;
    value: number | string;
    icon: string;
    onPress: () => void;
    subtitle?: string;
  }) => (
    <TouchableOpacity
      className="bg-gray-800 rounded-lg shadow-lg p-6 mb-4"
      onPress={onPress}
      style={{
        backgroundColor: "#1f2937",
        borderRadius: 8,
        padding: 24,
        marginBottom: 16,
      }}
    >
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-gray-400 text-base">{title}</Text>
        <Text className="text-2xl">{icon}</Text>
      </View>
      <Text className="text-3xl font-bold text-white mb-2">
        {typeof value === "number" ? value.toLocaleString() : value}
      </Text>
      {subtitle && (
        <Text className="text-sm text-neonBlue opacity-75">{subtitle}</Text>
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <ActivityIndicator size="large" color="#00d4ff" />
        <Text className="text-neonBlue text-xl mt-4">Loading...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-900">
      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View className="max-w-7xl mx-auto px-4 py-8">
          {/* Travel Status Section */}
          {currentTravel && currentTravel.status === "in_progress" && (
            <View className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
              <View className="flex-row items-center justify-between mb-4">
                <View className="flex-row items-center">
                  <Plane width={20} height={20} color="#60a5fa" />
                  <Text className="text-xl font-semibold text-white ml-2">
                    Travel Status
                  </Text>
                </View>
                <TouchableOpacity onPress={() => navigation.navigate("Travel")}>
                  <View className="flex-row items-center">
                    <Text className="text-blue-400 text-sm">Manage Travel</Text>
                    <ArrowRight width={16} height={16} color="#60a5fa" />
                  </View>
                </TouchableOpacity>
              </View>
              {/* Travel status details would go here */}
            </View>
          )}

          {/* Election Status Section */}
          {activeElection && activeElection.status === "active" && (
            <View className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
              <View className="flex-row items-center justify-between mb-4">
                <View className="flex-row items-center">
                  <Vote width={20} height={20} color="#a855f7" />
                  <Text className="text-xl font-semibold text-white ml-2">
                    Active Election
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={() => navigation.navigate("Elections")}
                >
                  <View className="flex-row items-center">
                    <Text className="text-purple-400 text-sm">
                      View Election
                    </Text>
                    <ArrowRight width={16} height={16} color="#a855f7" />
                  </View>
                </TouchableOpacity>
              </View>
              {/* Election details would go here */}
            </View>
          )}

          {/* Travel Quick Access (when not traveling) */}
          {(!currentTravel || currentTravel.status !== "in_progress") && (
            <View className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
              <View className="flex-row items-center justify-between mb-4">
                <View className="flex-row items-center">
                  <Plane width={20} height={20} color="#60a5fa" />
                  <Text className="text-xl font-semibold text-white ml-2">
                    Travel
                  </Text>
                </View>
              </View>
              <View>
                <Text className="text-gray-300 mb-2">
                  Plan your next journey
                </Text>
                <Text className="text-sm text-gray-400">
                  Travel to different regions to explore new opportunities
                </Text>
              </View>
            </View>
          )}

          {/* Stats Grid */}
          <View className="mb-6">
            <StatCard
              title="States"
              value={stats.totalStates}
              icon="🗺️"
              subtitle="Click to view all states"
              onPress={handleStatesModalOpen}
            />

            <StatCard
              title="Regions"
              value={stats.totalRegions}
              icon="🌍"
              subtitle="Click to view all regions"
              onPress={handleRegionsModalOpen}
            />

            <StatCard
              title="Total Population"
              value={stats.totalPlayers}
              icon="👥"
              subtitle="Click to view all players"
              onPress={handlePlayersModalOpen}
            />

            <StatCard
              title="Active Wars"
              value={stats.activeWars}
              icon="⚔️"
              subtitle="Click to view active wars"
              onPress={() => navigation.navigate("Wars", { showActive: true })}
            />

            <StatCard
              title="Factories"
              value={stats.totalFactories}
              icon="🏭"
              subtitle="Click to view all factories"
              onPress={handleFactoriesModalOpen}
            />
          </View>

          {/* Main Content Grid */}
          <View className="space-y-6">
            {/* My Region */}
            <View className="bg-gray-800 rounded-lg shadow-lg p-6">
              <View className="flex-row items-center justify-between mb-4">
                <Text className="text-xl font-semibold text-white">
                  My Region
                </Text>
                {user?.region && (
                  <TouchableOpacity
                    onPress={() =>
                      navigation.navigate("Regions", {
                        regionId: user.region.id,
                      })
                    }
                  >
                    <Text className="text-blue-400 text-sm">View Details</Text>
                  </TouchableOpacity>
                )}
              </View>
              {user?.region ? (
                <View className="space-y-4">
                  <View className="flex-row items-center mb-6">
                    <View className="w-16 h-16 bg-gray-700 rounded-lg flex items-center justify-center">
                      <Text className="text-2xl text-emerald-400">
                        {user.region.name.charAt(0).toUpperCase()}
                      </Text>
                    </View>
                    <View className="ml-4 flex-1">
                      <Text className="text-lg font-medium text-white">
                        {user.region.name}
                      </Text>
                      <Text className="text-gray-400">
                        Population:{" "}
                        {user.region?.population?.toLocaleString() || "N/A"}
                      </Text>
                      {user.region.state && (
                        <Text className="text-blue-400 text-sm">
                          State: {user.region.state.name}
                        </Text>
                      )}
                    </View>
                  </View>

                  {/* Region Resources */}
                  {user.region.resources && (
                    <View className="mb-4">
                      <Text className="text-white font-semibold mb-3">
                        Natural Resources
                      </Text>
                      <View className="flex-row flex-wrap justify-between">
                        {user.region.resources.gold && (
                          <View className="w-[48%] bg-gray-700 rounded-lg p-3 mb-2">
                            <Text className="text-yellow-400 font-bold">
                              🥇 Gold
                            </Text>
                            <Text className="text-white text-sm">
                              {user.region.resources.gold.current}/
                              {user.region.resources.gold.max}
                            </Text>
                          </View>
                        )}
                        {user.region.resources.oil && (
                          <View className="w-[48%] bg-gray-700 rounded-lg p-3 mb-2">
                            <Text className="text-black font-bold">🛢️ Oil</Text>
                            <Text className="text-white text-sm">
                              {user.region.resources.oil.current}/
                              {user.region.resources.oil.max}
                            </Text>
                          </View>
                        )}
                        {user.region.resources.ore && (
                          <View className="w-[48%] bg-gray-700 rounded-lg p-3 mb-2">
                            <Text className="text-gray-400 font-bold">
                              ⚙️ Ore
                            </Text>
                            <Text className="text-white text-sm">
                              {user.region.resources.ore.current}/
                              {user.region.resources.ore.max}
                            </Text>
                          </View>
                        )}
                        {user.region.resources.uranium && (
                          <View className="w-[48%] bg-gray-700 rounded-lg p-3 mb-2">
                            <Text className="text-green-400 font-bold">
                              ☢️ Uranium
                            </Text>
                            <Text className="text-white text-sm">
                              {user.region.resources.uranium.current}/
                              {user.region.resources.uranium.max}
                            </Text>
                          </View>
                        )}
                        {user.region.resources.diamonds && (
                          <View className="w-[48%] bg-gray-700 rounded-lg p-3 mb-2">
                            <Text className="text-blue-400 font-bold">
                              💎 Diamonds
                            </Text>
                            <Text className="text-white text-sm">
                              {user.region.resources.diamonds.current}/
                              {user.region.resources.diamonds.max}
                            </Text>
                          </View>
                        )}
                      </View>
                    </View>
                  )}

                  {/* Region Indices */}
                  <View>
                    <Text className="text-white font-semibold mb-3">
                      Development Indices
                    </Text>
                    <View className="flex-row justify-between">
                      <View className="flex-1 bg-gray-700 rounded-lg p-3 mr-1">
                        <Text className="text-red-400 font-bold text-lg">
                          {user.region.healthIndex || 0}
                        </Text>
                        <Text className="text-gray-400 text-xs">Health</Text>
                      </View>
                      <View className="flex-1 bg-gray-700 rounded-lg p-3 mx-1">
                        <Text className="text-orange-400 font-bold text-lg">
                          {user.region.militaryIndex || 0}
                        </Text>
                        <Text className="text-gray-400 text-xs">Military</Text>
                      </View>
                      <View className="flex-1 bg-gray-700 rounded-lg p-3 mx-1">
                        <Text className="text-blue-400 font-bold text-lg">
                          {user.region.educationIndex || 0}
                        </Text>
                        <Text className="text-gray-400 text-xs">Education</Text>
                      </View>
                      <View className="flex-1 bg-gray-700 rounded-lg p-3 ml-1">
                        <Text className="text-green-400 font-bold text-lg">
                          {user.region.developmentIndex || 0}
                        </Text>
                        <Text className="text-gray-400 text-xs">
                          Development
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              ) : (
                <View className="text-center py-4">
                  <Text className="text-gray-400">
                    You don't have a region assigned yet.
                  </Text>
                </View>
              )}
            </View>

            {/* My State */}
            <View className="bg-gray-800 rounded-lg shadow-lg p-6">
              <View className="flex-row items-center justify-between mb-4">
                <Text className="text-xl font-semibold text-white">
                  My State
                </Text>
                {myState && (
                  <TouchableOpacity
                    onPress={() =>
                      navigation.navigate("StateBudget", {
                        stateId: myState.id,
                      })
                    }
                  >
                    <Text className="text-blue-400 text-sm">View Details</Text>
                  </TouchableOpacity>
                )}
              </View>
              {myState ? (
                <View>
                  <View className="flex-row items-center mb-4">
                    <View className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center">
                      <Text className="text-xl text-neonBlue">
                        {myState.name.charAt(0).toUpperCase()}
                      </Text>
                    </View>
                    <View className="ml-3">
                      <Text className="text-lg font-medium text-white">
                        {myState.name}
                      </Text>
                      <Text className="text-gray-400 text-sm">
                        Leader: {myState.leader?.username || "Unknown"}
                      </Text>
                    </View>
                  </View>
                  <View className="space-y-2 mb-4">
                    <View className="flex-row justify-between">
                      <Text className="text-gray-400">Regions:</Text>
                      <Text className="text-white">
                        {myState.regions?.length || 0}
                      </Text>
                    </View>
                    <View className="flex-row justify-between">
                      <Text className="text-gray-400">Treasury:</Text>
                      <Text className="text-white">
                        {myState.treasury?.toLocaleString() || 0}
                      </Text>
                    </View>
                    <View className="flex-row justify-between">
                      <Text className="text-gray-400">Status:</Text>
                      <Text
                        className={
                          myState.isActive ? "text-green-400" : "text-red-400"
                        }
                      >
                        {myState.isActive ? "Active" : "Inactive"}
                      </Text>
                    </View>
                  </View>
                </View>
              ) : (
                <View className="text-center py-4">
                  <Text className="text-gray-400 mb-4">
                    You don't belong to any state yet.
                  </Text>
                  <TouchableOpacity
                    className="bg-blue-600 px-4 py-2 rounded"
                    onPress={() => navigation.navigate("CreateState")}
                  >
                    <Text className="text-white text-center">
                      Create a State
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            {/* Active Wars */}
            <View className="bg-gray-800 rounded-lg shadow-lg p-6">
              <View className="flex-row items-center justify-between mb-4">
                <Text className="text-xl font-semibold text-white">
                  Active Wars
                </Text>
                <TouchableOpacity onPress={() => navigation.navigate("Wars")}>
                  <Text className="text-red-400 text-sm">View All Wars</Text>
                </TouchableOpacity>
              </View>
              {activeWars.length > 0 ? (
                <View className="space-y-4">
                  {activeWars.slice(0, 3).map((war) => (
                    <View
                      key={war.id}
                      className="border-b border-gray-700 pb-4 last:border-0 last:pb-0"
                    >
                      <Text className="text-lg font-medium text-white mb-2">
                        {war.warType} War
                      </Text>
                      <View className="space-y-1 mb-2">
                        <View className="flex-row justify-between">
                          <Text className="text-gray-400">Attacker:</Text>
                          <TouchableOpacity
                            onPress={() =>
                              navigation.navigate("Regions", {
                                regionId: war.attackerRegion.id,
                              })
                            }
                          >
                            <Text className="text-neonBlue">
                              {war.attackerRegion.name}
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View className="flex-row justify-between">
                          <Text className="text-gray-400">Defender:</Text>
                          <TouchableOpacity
                            onPress={() =>
                              navigation.navigate("Regions", {
                                regionId: war.defenderRegion.id,
                              })
                            }
                          >
                            <Text className="text-neonBlue">
                              {war.defenderRegion.name}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                      <TouchableOpacity
                        onPress={() =>
                          navigation.navigate("Wars", { warId: war.id })
                        }
                      >
                        <Text className="text-red-400 text-sm">
                          View War Details
                        </Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              ) : (
                <View className="text-center py-4">
                  <Text className="text-gray-400 mb-4">No active wars.</Text>
                  <TouchableOpacity
                    className="bg-red-600 px-4 py-2 rounded"
                    onPress={() => navigation.navigate("DeclareWar")}
                  >
                    <Text className="text-white text-center">Declare War</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            {/* Global War Stats */}
            <View className="mb-6">
              <GlobalWarStats
                onRegionPress={(regionId) =>
                  navigation.navigate("RegionDetail", { regionId })
                }
                onStatePress={(stateId) =>
                  navigation.navigate("StateDetail", { stateId })
                }
              />
            </View>

            {/* War Timeline */}
            <View className="mb-6">
              <WarTimeline
                limit={10}
                onWarPress={(warId) =>
                  navigation.navigate("WarDetail", { warId })
                }
              />
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Searchable Modals */}
      <SearchableModal
        isOpen={playersModalOpen}
        onClose={() => setPlayersModalOpen(false)}
        title="All Players"
        icon={Users}
        data={playersData}
        loading={modalLoading}
        onItemClick={(item) => {
          handleItemClick(item, "player");
          setPlayersModalOpen(false);
        }}
        searchPlaceholder="Search players..."
      />

      <SearchableModal
        isOpen={regionsModalOpen}
        onClose={() => setRegionsModalOpen(false)}
        title="All Regions"
        icon={MapPin}
        data={regionsData}
        loading={modalLoading}
        onItemClick={(item) => {
          handleItemClick(item, "region");
          setRegionsModalOpen(false);
        }}
        searchPlaceholder="Search regions..."
      />

      <SearchableModal
        isOpen={statesModalOpen}
        onClose={() => setStatesModalOpen(false)}
        title="All States"
        icon={Flag}
        data={statesData}
        loading={modalLoading}
        onItemClick={(item) => {
          handleItemClick(item, "state");
          setStatesModalOpen(false);
        }}
        searchPlaceholder="Search states..."
      />

      <SearchableModal
        isOpen={factoriesModalOpen}
        onClose={() => setFactoriesModalOpen(false)}
        title="All Factories"
        icon={Factory}
        data={factoriesData}
        loading={modalLoading}
        onItemClick={(item) => {
          handleItemClick(item, "factory");
          setFactoriesModalOpen(false);
        }}
        searchPlaceholder="Search factories..."
      />

      {/* General Chat Widget */}
      <GeneralChatWidget
        isVisible={chatWidgetVisible}
        onToggle={() => setChatWidgetVisible(!chatWidgetVisible)}
      />
    </View>
  );
};
