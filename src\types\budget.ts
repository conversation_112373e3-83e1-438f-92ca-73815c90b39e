export enum BudgetTransactionType {
  // Income types
  INCOME_TAX = 'income_tax',
  BORDER_CROSSING_TAX = 'border_crossing_tax',
  FACTORY_TAX = 'factory_tax',
  TRADE_TAX = 'trade_tax',
  RESOURCE_EXTRACTION = 'resource_extraction',
  FOREIGN_AID = 'foreign_aid',
  
  // Expense types
  WAR_DECLARATION = 'war_declaration',
  BORDER_MAINTENANCE = 'border_maintenance',
  INFRASTRUCTURE = 'infrastructure',
  MILITARY_SUPPLIES = 'military_supplies',
  GOVERNMENT_SALARIES = 'government_salaries',
  EMERGENCY_FUND = 'emergency_fund',
  FOREIGN_AID_SENT = 'foreign_aid_sent',
  
  // Administrative
  MANUAL_ADJUSTMENT = 'manual_adjustment',
  SYSTEM_CORRECTION = 'system_correction'
}

export enum BudgetTransactionCategory {
  INCOME = 'income',
  EXPENSE = 'expense'
}

export interface BudgetTransaction {
  id: string;
  stateId: string;
  type: BudgetTransactionType;
  category: BudgetTransactionCategory;
  amount: number;
  description: string;
  relatedUserId?: number;
  relatedUserUsername?: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaxConfiguration {
  id: string;
  stateId: string;
  incomeTaxRate: number; // 0-100%
  borderCrossingTaxRate: number; // 0-50%
  minIncomeTaxRate: number;
  maxIncomeTaxRate: number;
  minBorderCrossingTaxRate: number;
  maxBorderCrossingTaxRate: number;
  lastUpdatedBy: number;
  lastUpdatedByUsername: string;
  createdAt: Date;
  updatedAt: Date;
}

// API response interface (what we actually get from the backend)
export interface BudgetSummaryResponse {
  currentTreasury: number;
  dailyIncome: number;
  dailyExpenses: number;
  taxConfig: any; // Tax configuration object
  recentTransactions: any[]; // Recent transactions array
}

// Frontend interface (what our components expect)
export interface BudgetSummary {
  stateId: string;
  treasury: number;
  dailyIncome: number;
  dailyExpenses: number;
  netDailyChange: number;
  weeklyIncome: number;
  weeklyExpenses: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  lastUpdated: Date;
}

export interface BudgetTransactionsPaginatedResponse {
  transactions: BudgetTransaction[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  summary: {
    totalIncome: number;
    totalExpenses: number;
    netChange: number;
  };
}

// API DTOs
export interface UpdateTaxConfigurationDto {
  incomeTaxRate?: number;
  borderCrossingTaxRate?: number;
}

export interface GetBudgetTransactionsDto {
  page?: number;
  limit?: number;
  category?: BudgetTransactionCategory;
  type?: BudgetTransactionType;
  startDate?: string;
  endDate?: string;
  relatedUserId?: number;
}

export interface CreateBudgetTransactionDto {
  type: BudgetTransactionType;
  amount: number;
  description: string;
  relatedUserId?: number;
  relatedEntityId?: string;
  relatedEntityType?: string;
  metadata?: Record<string, any>;
}

// Tax calculation interfaces
export interface TaxCalculationResult {
  originalAmount: number;
  taxAmount: number;
  netAmount: number;
  taxRate: number;
  taxType: 'income' | 'border_crossing';
}

export interface TravelCostBreakdown {
  baseCost: number;
  borderTax: number;
  totalCost: number;
  currency: 'money' | 'gold';
  distance: number;
  taxRate: number;
}