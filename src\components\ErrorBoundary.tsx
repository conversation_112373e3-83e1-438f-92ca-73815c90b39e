import React, { Component, ReactNode } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { theme } from '../styles/theme';

interface Props {
  children: ReactNode;
  fallbackComponent?: React.ComponentType<{ error: Error; errorInfo: any; onRetry: () => void }>;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);

    // In production, you might want to log this to a crash reporting service
    if (!__DEV__) {
      // crashReportingService.recordError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleReportError = () => {
    const { error, errorInfo } = this.state;
    
    Alert.alert(
      'Report Error',
      'Would you like to report this error to help us improve the app?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Report', 
          onPress: () => {
            // In production, send error report to your backend
            console.log('Error reported:', { error, errorInfo });
            Alert.alert('Thank you!', 'Error report has been sent.');
          }
        }
      ]
    );
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback component if provided
      if (this.props.fallbackComponent) {
        const FallbackComponent = this.props.fallbackComponent;
        return (
          <FallbackComponent
            error={this.state.error!}
            errorInfo={this.state.errorInfo}
            onRetry={this.handleRetry}
          />
        );
      }

      // Default fallback UI
      return (
        <View style={styles.container}>
          <View style={styles.content}>
            <Text style={styles.title}>Oops! Something went wrong</Text>
            <Text style={styles.message}>
              The app encountered an unexpected error. Don't worry, your data is safe.
            </Text>

            <View style={styles.actions}>
              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={this.handleRetry}
              >
                <Text style={styles.buttonText}>Try Again</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={this.handleReportError}
              >
                <Text style={[styles.buttonText, styles.secondaryButtonText]}>
                  Report Error
                </Text>
              </TouchableOpacity>
            </View>

            {__DEV__ && (
              <ScrollView style={styles.errorDetails}>
                <Text style={styles.errorDetailsTitle}>Error Details (Dev Mode):</Text>
                <Text style={styles.errorDetailsText}>
                  {this.state.error?.toString()}
                </Text>
                {this.state.errorInfo && (
                  <Text style={styles.errorDetailsText}>
                    {JSON.stringify(this.state.errorInfo, null, 2)}
                  </Text>
                )}
              </ScrollView>
            )}
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for wrapping individual screens
export function withErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  fallbackComponent?: React.ComponentType<{ error: Error; errorInfo: any; onRetry: () => void }>
) {
  const WithErrorBoundaryComponent = (props: P) => (
    <ErrorBoundary fallbackComponent={fallbackComponent}>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  WithErrorBoundaryComponent.displayName = `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithErrorBoundaryComponent;
}

// Custom fallback component for navigation errors
export const NavigationErrorFallback: React.FC<{
  error: Error;
  errorInfo: any;
  onRetry: () => void;
}> = ({ error, onRetry }) => (
  <View style={styles.navigationError}>
    <Text style={styles.navigationErrorTitle}>Navigation Error</Text>
    <Text style={styles.navigationErrorMessage}>
      Unable to load this screen. Please try again.
    </Text>
    <TouchableOpacity style={[styles.button, styles.primaryButton]} onPress={onRetry}>
      <Text style={styles.buttonText}>Retry</Text>
    </TouchableOpacity>
  </View>
);

// Custom fallback component for network errors
export const NetworkErrorFallback: React.FC<{
  error: Error;
  errorInfo: any;
  onRetry: () => void;
}> = ({ error, onRetry }) => (
  <View style={styles.networkError}>
    <Text style={styles.networkErrorTitle}>Connection Problem</Text>
    <Text style={styles.networkErrorMessage}>
      Check your internet connection and try again.
    </Text>
    <TouchableOpacity style={[styles.button, styles.primaryButton]} onPress={onRetry}>
      <Text style={styles.buttonText}>Retry</Text>
    </TouchableOpacity>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: theme.spacing.xl,
    alignItems: 'center',
    maxWidth: 350,
  },
  title: {
    fontSize: theme.fontSize.title,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  message: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: theme.spacing.xl,
  },
  actions: {
    width: '100%',
    gap: theme.spacing.md,
  },
  button: {
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.medium,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: theme.colors.primary,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  buttonText: {
    fontSize: theme.fontSize.medium,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
  },
  secondaryButtonText: {
    color: theme.colors.textSecondary,
  },
  errorDetails: {
    marginTop: theme.spacing.xl,
    maxHeight: 200,
    width: '100%',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.small,
    padding: theme.spacing.md,
  },
  errorDetailsTitle: {
    fontSize: theme.fontSize.small,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.error,
    marginBottom: theme.spacing.sm,
  },
  errorDetailsText: {
    fontSize: theme.fontSize.small,
    color: theme.colors.textMuted,
    fontFamily: 'monospace',
  },
  navigationError: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
    backgroundColor: theme.colors.background,
  },
  navigationErrorTitle: {
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.error,
    marginBottom: theme.spacing.md,
  },
  navigationErrorMessage: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  networkError: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
    backgroundColor: theme.colors.background,
  },
  networkErrorTitle: {
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.warning,
    marginBottom: theme.spacing.md,
  },
  networkErrorMessage: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
});