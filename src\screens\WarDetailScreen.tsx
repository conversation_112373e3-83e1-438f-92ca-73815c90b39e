import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Modal,
} from 'react-native';
import {
  ArrowLeft,
  Sword,
  Users,
  MapPin,
  Clock,
  Zap,
  AlertTriangle,
  X,
  ExternalLink,
  Target,
  Shield,
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { warService } from '../services/warService';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
  route: any;
}

interface War {
  id: string;
  warType: string;
  warTarget: string;
  status: string;
  declaredAt: string;
  endedAt?: string;
  attackerRegion?: {
    id: string;
    name: string;
  };
  defenderRegion?: {
    id: string;
    name: string;
  };
  attackerState?: {
    id: string;
    name: string;
  };
  defenderState?: {
    id: string;
    name: string;
  };
  region?: {
    id: string;
    name: string;
  };
  state?: {
    id: string;
    name: string;
  };
  declaredBy?: {
    username: string;
  };
  declaration?: string;
  attackerGroundDamage: number;
  attackerSeaDamage: number;
  defenderGroundDamage: number;
  defenderSeaDamage: number;
  damageRequirement?: number;
  participants: {
    attackers: Array<{
      id: string;
      userId?: string;
      username?: string;
      damage: number;
      state?: {
        id: string;
        name: string;
      };
    }>;
    defenders: Array<{
      id: string;
      userId?: string;
      username?: string;
      damage: number;
      state?: {
        id: string;
        name: string;
      };
    }>;
  };
  battleEvents?: Array<{
    timestamp: string;
    description: string;
    damage: number;
    side: string;
  }>;
}

export const WarDetailScreen: React.FC<Props> = ({ navigation, route }) => {
  useAuthGuard({ navigation });
  
  const { warId } = route.params;
  const { user } = useAuthStore();
  const [war, setWar] = useState<War | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [participating, setParticipating] = useState(false);
  const [participationLoading, setParticipationLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [energyAmount, setEnergyAmount] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [modalSide, setModalSide] = useState<'attackers' | 'defenders' | null>(null);
  const [userCurrentSide, setUserCurrentSide] = useState<string | null>(null);

  useEffect(() => {
    loadWarData();
  }, [warId]);

  const loadWarData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [warData, myWars] = await Promise.all([
        warService.getWar(warId),
        warService.getMyWars().catch(() => [])
      ]);
      
      setWar(warData);
      
      // Check if user is participating
      const isParticipating = myWars.some((w: any) => w.id === warData.id);
      setParticipating(isParticipating);
      
      // Determine user side if participating
      if (isParticipating && user) {
        const isInAttackers = warData.participants?.attackers?.some((p: any) => p.userId === user.id);
        const isInDefenders = warData.participants?.defenders?.some((p: any) => p.userId === user.id);
        
        if (isInAttackers && !isInDefenders) {
          setUserCurrentSide('attacker');
        } else if (isInDefenders && !isInAttackers) {
          setUserCurrentSide('defender');
        }
      }
    } catch (error: any) {
      console.error('Error loading war:', error);
      setError(error.message || 'Failed to load war details');
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load war details',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadWarData();
  };

  const handleJoinWar = async (isAttacker: boolean) => {
    if (participating) return;
    
    try {
      setParticipationLoading(true);
      
      if (!user || user.energy <= 0) {
        Toast.show({
          type: 'error',
          text1: 'Insufficient Energy',
          text2: 'You do not have enough energy to participate in the war.',
        });
        return;
      }

      // For revolution wars, set user side
      if (war?.warType === 'revolution') {
        const side = isAttacker ? 'attacker' : 'defender';
        setUserCurrentSide(side);
      }

      Toast.show({
        type: 'success',
        text1: 'Joined War!',
        text2: `Successfully joined as ${isAttacker ? 'attacker' : 'defender'}!`,
      });
      
      setParticipating(true);
      setActiveTab('participate');
      
      // Refresh war data
      await loadWarData();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Failed to Join',
        text2: error.message || 'Failed to join the war',
      });
    } finally {
      setParticipationLoading(false);
    }
  };

  const handleParticipate = async () => {
    if (!energyAmount || isNaN(Number(energyAmount))) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Energy',
        text2: 'Please enter a valid energy amount',
      });
      return;
    }

    const energy = Number(energyAmount);
    if (energy <= 0 || (user && energy > user.energy)) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Energy',
        text2: user ? `You only have ${user.energy} energy available` : 'Invalid energy amount',
      });
      return;
    }

    try {
      setParticipationLoading(true);
      
      const participateDto = {
        energyAmount: energy,
        autoMode: false,
        ...(userCurrentSide && { side: userCurrentSide })
      };

      await warService.participateInWar(warId, participateDto);

      Toast.show({
        type: 'success',
        text1: 'Attack Successful!',
        text2: `Dealt damage with ${energy} energy!`,
      });
      
      setEnergyAmount('');
      // Refresh war data
      await loadWarData();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Attack Failed',
        text2: error.message || 'Failed to participate in war',
      });
    } finally {
      setParticipationLoading(false);
    }
  };

  const handleSwitchSide = async (newSide: string) => {
    if (war?.warType !== 'revolution' || newSide === userCurrentSide) return;
    
    try {
      setParticipationLoading(true);
      
      if (!user || user.energy <= 0) {
        Toast.show({
          type: 'error',
          text1: 'Insufficient Energy',
          text2: 'You need energy to switch sides.',
        });
        return;
      }

      const participateData = {
        energyAmount: user.energy,
        autoMode: false,
        side: newSide
      };
      
      await warService.participateInWar(warId, participateData);
      
      Toast.show({
        type: 'success',
        text1: 'Side Switched!',
        text2: `Successfully switched to ${newSide} side!`,
      });
      
      setUserCurrentSide(newSide);
      await loadWarData();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Switch Failed',
        text2: error.message || 'Failed to switch sides',
      });
    } finally {
      setParticipationLoading(false);
    }
  };

  const getWarTypeColor = (warType?: string) => {
    switch (warType) {
      case 'sea':
        return {
          border: '#3b82f6',
          badge: '#1d4ed8',
          displayName: 'SEA WAR'
        };
      case 'ground':
        return {
          border: '#10b981',
          badge: '#047857',
          displayName: 'GROUND WAR'
        };
      case 'revolution':
        return {
          border: '#f59e0b',
          badge: '#d97706',
          displayName: 'REVOLUTION'
        };
      default:
        return {
          border: '#8b5cf6',
          badge: '#7c3aed',
          displayName: 'WAR'
        };
    }
  };

  const getTotalDamage = (side: 'attacker' | 'defender') => {
    if (!war) return 0;
    if (side === 'attacker') {
      return (war.attackerSeaDamage || 0) + (war.attackerGroundDamage || 0);
    }
    return (war.defenderSeaDamage || 0) + (war.defenderGroundDamage || 0);
  };

  const consolidateParticipants = (participants: any[]) => {
    const consolidated: { [key: string]: any } = {};
    participants.forEach(participant => {
      const key = participant.userId
        ? `user-${participant.userId}`
        : participant.state
        ? `state-${participant.state.id}`
        : `unknown-${participant.id}`;
      if (!consolidated[key]) {
        consolidated[key] = {
          ...participant,
          totalDamage: participant.damage || 0,
          count: 1
        };
      } else {
        consolidated[key].totalDamage += (participant.damage || 0);
        consolidated[key].count += 1;
      }
    });
    return Object.values(consolidated).sort((a: any, b: any) => b.totalDamage - a.totalDamage);
  };

  const isWarActive = (status?: string) => {
    const activeStatuses = ['active'];
    return activeStatuses.includes(status || '');
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading && !refreshing) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <View className="relative">
          <View className="w-16 h-16 border-t-2 border-b-2 border-red-600 rounded-full" />
          <View className="absolute inset-0 justify-center items-center">
            <View className="w-8 h-8 bg-red-600 rounded-full" />
          </View>
        </View>
        <Text className="mt-6 text-xl font-bold text-white tracking-wider text-center">
          GATHERING BATTLEFIELD INTELLIGENCE...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 bg-gray-900 p-4">
        <View className="bg-gradient-to-r from-red-900 to-red-800 border-l-4 border-red-500 p-6 rounded-xl mt-8">
          <View className="flex-row items-start">
            <AlertTriangle width={32} height={32} color="#fbbf24" />
            <View className="ml-3 flex-1">
              <Text className="font-black text-lg text-yellow-100">COMMUNICATION FAILURE</Text>
              <Text className="text-yellow-100 mt-2">{error}</Text>
              <TouchableOpacity
                onPress={() => loadWarData()}
                className="mt-4 bg-gradient-to-r from-red-700 to-red-800 py-2 px-4 rounded-lg"
              >
                <Text className="font-bold text-white text-sm">RETRY TRANSMISSION</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    );
  }

  if (!war) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <Text className="text-white text-center text-xl">War not found</Text>
      </View>
    );
  }

  const warTypeColor = getWarTypeColor(war.warType);
  const attackers = war.participants?.attackers || [];
  const defenders = war.participants?.defenders || [];
  const consolidatedAttackers = consolidateParticipants(attackers);
  const consolidatedDefenders = consolidateParticipants(defenders);

  return (
    <View className="flex-1 bg-gray-900">
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View className="p-4">
          {/* Back Button */}
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="flex-row items-center mb-6"
          >
            <ArrowLeft width={20} height={20} color="#00d4ff" />
            <Text className="text-neonBlue ml-2">Back to Wars</Text>
          </TouchableOpacity>

          {/* War Header */}
          <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-2xl p-6 mb-8">
            <View className="flex-row justify-between items-start mb-6">
              <View className="flex-1">
                <View className="flex-row items-center mb-2">
                  <Text className="text-3xl font-black text-white uppercase tracking-wide">
                    {warTypeColor.displayName}
                  </Text>
                  <View 
                    className="ml-3 px-3 py-1 rounded-lg"
                    style={{ backgroundColor: warTypeColor.badge }}
                  >
                    <Text className="text-xs font-black uppercase text-white">
                      {war.warType?.toUpperCase()?.slice(0, 3) || 'WAR'}
                    </Text>
                  </View>
                </View>
                <Text className="text-gray-400 mt-1">
                  Strategic overview of this conflict
                </Text>
              </View>
            </View>

            {/* Tabs Navigation */}
            <View className="mb-6">
              <View className="flex-row flex-wrap">
                <TouchableOpacity
                  onPress={() => setActiveTab('overview')}
                  className={`px-4 py-2 rounded-xl mr-2 mb-2 border-2 border-gray-700 ${
                    activeTab === 'overview'
                      ? 'bg-gradient-to-r from-red-700 to-red-800'
                      : 'bg-gradient-to-r from-gray-800 to-gray-900'
                  }`}
                >
                  <Text className={`font-bold ${
                    activeTab === 'overview' ? 'text-white' : 'text-gray-400'
                  }`}>
                    Overview
                  </Text>
                </TouchableOpacity>
                
                {participating && isWarActive(war.status) && (
                  <TouchableOpacity
                    onPress={() => setActiveTab('participate')}
                    className={`px-4 py-2 rounded-xl mr-2 mb-2 border-2 border-gray-700 ${
                      activeTab === 'participate'
                        ? 'bg-gradient-to-r from-red-700 to-red-800'
                        : 'bg-gradient-to-r from-gray-800 to-gray-900'
                    }`}
                  >
                    <Text className={`font-bold ${
                      activeTab === 'participate' ? 'text-white' : 'text-gray-400'
                    }`}>
                      Fight
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <View>
                {/* War Details and Progress Grid */}
                <View className="space-y-6 mb-8">
                  <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-xl p-5">
                    <Text className="text-xl font-bold text-white mb-4 border-b border-gray-700 pb-2">
                      War Details
                    </Text>
                    <View className="space-y-3">
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Target:</Text>
                        <Text className="text-white font-medium">{war.warTarget}</Text>
                      </View>
                      {war.region && (
                        <View className="flex-row justify-between">
                          <Text className="text-gray-400">Target Region:</Text>
                          <Text className="text-neonBlue">{war.region.name}</Text>
                        </View>
                      )}
                      {war.state && (
                        <View className="flex-row justify-between">
                          <Text className="text-gray-400">Target State:</Text>
                          <Text className="text-neonBlue">{war.state.name}</Text>
                        </View>
                      )}
                      <View className="flex-row justify-between">
                        <Text className="text-gray-400">Started:</Text>
                        <Text className="text-white">{formatDate(war.declaredAt)}</Text>
                      </View>
                      {war.endedAt && (
                        <View className="flex-row justify-between">
                          <Text className="text-gray-400">Ended:</Text>
                          <Text className="text-white">{formatDate(war.endedAt)}</Text>
                        </View>
                      )}
                    </View>
                  </View>

                  <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-xl p-5">
                    <Text className="text-xl font-bold text-white mb-4 border-b border-gray-700 pb-2">
                      War Progress
                    </Text>
                    <View className="mb-2">
                      <View className="flex-row justify-between text-xs mb-1">
                        <Text className="font-bold text-gray-400">
                          ATTACKER DMG: {getTotalDamage('attacker')}
                        </Text>
                        <Text className="font-bold text-gray-400">
                          DEFENDER DMG: {getTotalDamage('defender')}
                        </Text>
                      </View>
                      <View className="w-full bg-gray-700 h-3 rounded-full overflow-hidden border border-gray-600">
                        <View 
                          className="bg-gradient-to-r from-red-700 to-red-900 h-full"
                          style={{ 
                            width: `${Math.round((getTotalDamage('attacker') / (getTotalDamage('attacker') + getTotalDamage('defender') || 1)) * 100)}%` 
                          }}
                        />
                      </View>
                      <View className="flex-row justify-between text-xs mt-1 text-gray-400">
                        <Text>Attacker Victory</Text>
                        <Text>Defender Victory</Text>
                      </View>
                    </View>
                  </View>
                </View>

                {/* Participants Grid */}
                <View className="space-y-6 mb-8">
                  <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-xl p-5">
                    <View className="flex-row items-center justify-between mb-4">
                      <Text className="text-xl font-bold text-white">
                        Attackers ({consolidatedAttackers.length})
                      </Text>
                      <View className="bg-red-900 px-2 py-1 rounded-full">
                        <Text className="text-red-100 text-xs">
                          {getTotalDamage('attacker')} DMG
                        </Text>
                      </View>
                    </View>
                    
                    {war.attackerRegion && (
                      <View className="mb-3 flex-row items-center">
                        <Text className="text-gray-400 text-sm mr-2">Region:</Text>
                        <Text className="text-neonBlue text-sm">{war.attackerRegion.name}</Text>
                      </View>
                    )}
                    
                    {consolidatedAttackers.length === 0 ? (
                      <Text className="text-gray-400 text-center py-4">No attackers yet</Text>
                    ) : (
                      <View>
                        {consolidatedAttackers.slice(0, 5).map((participant: any) => (
                          <View key={participant.id} className="bg-gray-800 p-3 rounded-lg mb-2 flex-row justify-between items-center">
                            <View>
                              <Text className="text-white font-medium">
                                {participant.username || participant.state?.name || 'Unknown participant'}
                              </Text>
                              {participant.count > 1 && (
                                <Text className="text-xs text-gray-400">
                                  ({participant.count} attacks)
                                </Text>
                              )}
                            </View>
                            {participant.totalDamage > 0 && (
                              <View className="bg-red-900 px-2 py-1 rounded">
                                <Text className="text-sm text-red-100">
                                  {participant.totalDamage.toLocaleString()}
                                </Text>
                              </View>
                            )}
                          </View>
                        ))}
                        {consolidatedAttackers.length > 5 && (
                          <TouchableOpacity
                            onPress={() => {
                              setModalSide('attackers');
                              setShowModal(true);
                            }}
                            className="mt-4"
                          >
                            <Text className="text-neonBlue text-center text-sm">
                              View all {consolidatedAttackers.length} attackers...
                            </Text>
                          </TouchableOpacity>
                        )}
                      </View>
                    )}
                  </View>

                  <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-xl p-5">
                    <View className="flex-row items-center justify-between mb-4">
                      <Text className="text-xl font-bold text-white">
                        Defenders ({consolidatedDefenders.length})
                      </Text>
                      <View className="bg-blue-900 px-2 py-1 rounded-full">
                        <Text className="text-blue-100 text-xs">
                          {getTotalDamage('defender')} DMG
                        </Text>
                      </View>
                    </View>
                    
                    {war.defenderRegion && (
                      <View className="mb-3 flex-row items-center">
                        <Text className="text-gray-400 text-sm mr-2">Region:</Text>
                        <Text className="text-neonBlue text-sm">{war.defenderRegion.name}</Text>
                      </View>
                    )}
                    
                    {consolidatedDefenders.length === 0 ? (
                      <Text className="text-gray-400 text-center py-4">No defenders yet</Text>
                    ) : (
                      <View>
                        {consolidatedDefenders.slice(0, 5).map((participant: any) => (
                          <View key={participant.id} className="bg-gray-800 p-3 rounded-lg mb-2 flex-row justify-between items-center">
                            <View>
                              <Text className="text-white font-medium">
                                {participant.username || participant.state?.name || 'Unknown participant'}
                              </Text>
                              {participant.count > 1 && (
                                <Text className="text-xs text-gray-400">
                                  ({participant.count} attacks)
                                </Text>
                              )}
                            </View>
                            {participant.totalDamage > 0 && (
                              <View className="bg-blue-900 px-2 py-1 rounded">
                                <Text className="text-sm text-blue-100">
                                  {participant.totalDamage.toLocaleString()}
                                </Text>
                              </View>
                            )}
                          </View>
                        ))}
                        {consolidatedDefenders.length > 5 && (
                          <TouchableOpacity
                            onPress={() => {
                              setModalSide('defenders');
                              setShowModal(true);
                            }}
                            className="mt-4"
                          >
                            <Text className="text-neonBlue text-center text-sm">
                              View all {consolidatedDefenders.length} defenders...
                            </Text>
                          </TouchableOpacity>
                        )}
                      </View>
                    )}
                  </View>
                </View>

                {/* Join War Section */}
                {isWarActive(war.status) && !participating && (
                  <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-xl p-6 mb-8">
                    <Text className="text-xl font-bold text-white mb-4">Join the Battle</Text>
                    {war.warType === 'revolution' ? (
                      <View className="items-center">
                        <Text className="text-yellow-400 mb-4 text-center">
                          Choose your side in this revolution:
                        </Text>
                        <View className="w-full space-y-4">
                          <TouchableOpacity
                            onPress={() => handleJoinWar(true)}
                            disabled={participationLoading}
                            className="w-full px-6 py-3 bg-gradient-to-r from-red-700 to-red-800 rounded-xl"
                          >
                            <Text className="font-bold text-white text-center">
                              Fight for Attacking Side
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => handleJoinWar(false)}
                            disabled={participationLoading}
                            className="w-full px-6 py-3 bg-gradient-to-r from-blue-700 to-blue-800 rounded-xl"
                          >
                            <Text className="font-bold text-white text-center">
                              Fight For Defending Side
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    ) : (
                      <View className="w-full space-y-4">
                        <TouchableOpacity
                          onPress={() => handleJoinWar(true)}
                          disabled={participationLoading}
                          className="w-full px-6 py-3 bg-gradient-to-r from-red-700 to-red-800 rounded-xl"
                        >
                          <Text className="font-bold text-white text-center">
                            Fight for Attacking Side
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => handleJoinWar(false)}
                          disabled={participationLoading}
                          className="w-full px-6 py-3 bg-gradient-to-r from-blue-700 to-blue-800 rounded-xl"
                        >
                          <Text className="font-bold text-white text-center">
                            Fight for Defending Side
                          </Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                )}

                {/* Participating Section */}
                {participating && (
                  <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-xl p-6 mb-8">
                    <View className="flex-row items-center justify-between mb-4">
                      <Text className="text-green-400 font-bold">
                        You are already participating in this war.
                      </Text>
                    </View>

                    {war.warType === 'revolution' && (
                      <View className="mb-4">
                        <View className="bg-gray-700 p-4 rounded-lg mb-4">
                          <View className="flex-row items-center justify-between">
                            <Text className="text-gray-300 font-medium">Your Current Side:</Text>
                            <View className={`px-3 py-1 rounded-full ${
                              userCurrentSide === 'attacker'
                                ? 'bg-gradient-to-r from-red-600 to-red-800'
                                : 'bg-gradient-to-r from-blue-600 to-blue-800'
                            }`}>
                              <Text className="font-bold text-white text-sm">
                                {userCurrentSide === 'attacker' ? 'Attacker' : 'Defender'}
                              </Text>
                            </View>
                          </View>
                        </View>

                        {isWarActive(war.status) && (
                          <View className="items-center">
                            {userCurrentSide === 'attacker' ? (
                              <TouchableOpacity
                                onPress={() => handleSwitchSide('defender')}
                                disabled={participationLoading}
                                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl"
                              >
                                <Text className="text-white font-medium text-center">
                                  {participationLoading ? 'Switching...' : 'Switch to Defender Side'}
                                </Text>
                              </TouchableOpacity>
                            ) : (
                              <TouchableOpacity
                                onPress={() => handleSwitchSide('attacker')}
                                disabled={participationLoading}
                                className="px-6 py-2 bg-gradient-to-r from-red-600 to-red-700 rounded-xl"
                              >
                                <Text className="text-white font-medium text-center">
                                  {participationLoading ? 'Switching...' : 'Switch to Attacker Side'}
                                </Text>
                              </TouchableOpacity>
                            )}
                            <Text className="text-gray-400 text-xs mt-2 text-center">
                              Switching sides requires energy and will immediately change your allegiance
                            </Text>
                          </View>
                        )}
                      </View>
                    )}

                    {isWarActive(war.status) && (
                      <TouchableOpacity
                        onPress={() => setActiveTab('participate')}
                        className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl"
                      >
                        <Text className="font-bold text-white text-center">Continue Fighting</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                )}
              </View>
            )}

            {/* Participate Tab */}
            {activeTab === 'participate' && participating && isWarActive(war.status) && (
              <View>
                <View className="mb-6">
                  <Text className="text-2xl font-bold text-white mb-2">
                    {war.warType === 'revolution' ? 'Revolution Participation' : 'War Participation'}
                  </Text>
                  <Text className="text-gray-300 mb-4">
                    {war.warType === 'revolution'
                      ? 'Fight for your chosen side in this revolution. Use your energy to deal damage and help your side win.'
                      : 'Use your energy to participate in this war. The more energy you spend, the more damage you\'ll deal to the enemy.'
                    }
                  </Text>
                </View>

                <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-xl p-6">
                  <Text className="text-xl font-bold text-white mb-4">Launch Attack</Text>
                  
                  <Text className="text-gray-300 mb-4">
                    Available Energy: {user?.energy || 0}
                  </Text>
                  
                  <TextInput
                    className="bg-gray-800 border-2 border-gray-700 rounded-xl p-4 text-white text-lg mb-4"
                    placeholder="Enter energy amount"
                    placeholderTextColor="#9ca3af"
                    value={energyAmount}
                    onChangeText={setEnergyAmount}
                    keyboardType="numeric"
                  />
                  
                  <TouchableOpacity
                    onPress={handleParticipate}
                    disabled={participationLoading}
                    className={`w-full py-3 rounded-xl ${
                      participationLoading
                        ? 'bg-gray-600'
                        : 'bg-gradient-to-r from-red-700 to-red-800'
                    }`}
                  >
                    <View className="flex-row items-center justify-center">
                      {participationLoading ? (
                        <ActivityIndicator size="small" color="#ffffff" />
                      ) : (
                        <Sword width={20} height={20} color="#ffffff" />
                      )}
                      <Text className="text-white font-bold ml-2">
                        {participationLoading ? 'Attacking...' : '⚔️ Launch Attack'}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            )}

          </View>
        </View>
      </ScrollView>

      {/* Participants Modal */}
      <Modal
        visible={showModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-75 justify-center items-center p-4">
          <View className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-2xl max-w-md w-full max-h-[80%] flex">
            <View className="flex-row justify-between items-center p-4 border-b border-gray-700">
              <Text className="text-xl font-bold text-white">
                {modalSide === 'attackers' ? 'All Attackers' : 'All Defenders'} 
                ({modalSide === 'attackers' ? consolidatedAttackers.length : consolidatedDefenders.length})
              </Text>
              <TouchableOpacity onPress={() => setShowModal(false)}>
                <X width={24} height={24} color="#9ca3af" />
              </TouchableOpacity>
            </View>

            <ScrollView className="p-4 flex-1" showsVerticalScrollIndicator={false}>
              {modalSide === 'attackers' ? (
                <View className="space-y-3">
                  {consolidatedAttackers.map((participant: any) => (
                    <View key={participant.userId || participant.id} className="py-3 flex-row justify-between items-center border-b border-gray-700">
                      <View>
                        <Text className="text-white font-medium">
                          {participant.username || participant.state?.name || 'Unknown participant'}
                        </Text>
                        {participant.count > 1 && (
                          <Text className="text-xs text-gray-400">
                            ({participant.count} attacks)
                          </Text>
                        )}
                      </View>
                      {participant.totalDamage > 0 && (
                        <View className="bg-red-900 px-2 py-1 rounded">
                          <Text className="text-red-100 text-sm">
                            {participant.totalDamage.toLocaleString()} damage
                          </Text>
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              ) : (
                <View className="space-y-3">
                  {consolidatedDefenders.map((participant: any) => (
                    <View key={participant.userId || participant.id} className="py-3 flex-row justify-between items-center border-b border-gray-700">
                      <View>
                        <Text className="text-white font-medium">
                          {participant.username || participant.state?.name || 'Unknown participant'}
                        </Text>
                        {participant.count > 1 && (
                          <Text className="text-xs text-gray-400">
                            ({participant.count} attacks)
                          </Text>
                        )}
                      </View>
                      {participant.totalDamage > 0 && (
                        <View className="bg-blue-900 px-2 py-1 rounded">
                          <Text className="text-blue-100 text-sm">
                            {participant.totalDamage.toLocaleString()} damage
                          </Text>
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              )}
            </ScrollView>

            <View className="p-4 border-t border-gray-700">
              <TouchableOpacity
                onPress={() => setShowModal(false)}
                className="w-full bg-gradient-to-r from-gray-700 to-gray-800 py-2 px-4 rounded-xl"
              >
                <Text className="text-white text-center font-medium">Close</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};