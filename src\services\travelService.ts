import { api } from './api';

export interface Travel {
  id: string;
  userId: number;
  user?: {
    id: number;
    username: string;
  };
  fromRegionId: string;
  fromRegion?: {
    id: string;
    name: string;
  };
  toRegionId: string;
  toRegion?: {
    id: string;
    name: string;
  };
  startTime: string;
  estimatedArrivalTime: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  distance: number;
  cost: number;
  createdAt: string;
}

export interface TravelTimeEstimate {
  estimatedTime: number; // in milliseconds
  distance: number;
  cost: number;
}

export interface TravelPermissionRequest {
  id: string;
  userId: number;
  user?: {
    id: number;
    username: string;
    level?: number;
  };
  fromRegionId: string;
  fromRegion?: {
    id: string;
    name: string;
  };
  toRegionId: string;
  toRegion?: {
    id: string;
    name: string;
  };
  stateId: string;
  state?: {
    id: string;
    name: string;
  };
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  respondedBy?: number;
  respondedAt?: string;
  createdAt: string;
}

export const travelService = {
  // Travel management
  initiateTravel: async (travelData: {
    toRegionId: string;
    reason?: string;
  }): Promise<Travel> => {
    const response = await api.post('/travel/initiate', travelData);
    return response.data;
  },

  getCurrentTravel: async (): Promise<Travel | null> => {
    const response = await api.get('/travel/current');
    return response.data;
  },

  cancelTravel: async (travelId: string): Promise<void> => {
    await api.post(`/travel/cancel/${travelId}`);
  },

  getTimeEstimate: async (toRegionId: string): Promise<TravelTimeEstimate> => {
    const response = await api.post('/travel/time-estimate', { toRegionId });
    return response.data;
  },

  // Travel permissions
  requestTravelPermission: async (requestData: {
    toRegionId: string;
    reason?: string;
  }): Promise<TravelPermissionRequest> => {
    const response = await api.post('/travel/request-permission', requestData);
    return response.data;
  },

  respondToPermissionRequest: async (requestId: string, response: 'approve' | 'reject', reason?: string): Promise<TravelPermissionRequest> => {
    const responseData = await api.patch(`/travel/permission-requests/${requestId}`, { 
      status: response === 'approve' ? 'approved' : 'rejected',
      reason 
    });
    return responseData.data;
  },

  getStatePermissionRequests: async (): Promise<TravelPermissionRequest[]> => {
    const response = await api.get('/travel/permission-requests/state');
    return response.data;
  },

  getUserPermissionRequests: async (): Promise<TravelPermissionRequest[]> => {
    const response = await api.get('/travel/permission-requests/user');
    return response.data;
  },

  // Additional methods for TravelPermissionsScreen
  getIncomingPermissions: async (): Promise<TravelPermissionRequest[]> => {
    const response = await api.get('/travel/permission-requests/state');
    return response.data;
  },

  getMyPermissions: async (): Promise<TravelPermissionRequest[]> => {
    const response = await api.get('/travel/permission-requests/user');
    return response.data;
  },

  approvePermission: async (permissionId: number): Promise<TravelPermissionRequest> => {
    const response = await api.patch(`/travel/permission-requests/${permissionId}`, { 
      status: 'approved'
    });
    return response.data;
  },

  denyPermission: async (permissionId: number): Promise<TravelPermissionRequest> => {
    const response = await api.patch(`/travel/permission-requests/${permissionId}`, { 
      status: 'rejected'
    });
    return response.data;
  },
};