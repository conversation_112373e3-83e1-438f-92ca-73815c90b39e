import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Camera, Upload } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';

export interface PhotoUploadProps {
  currentPhotoUrl?: string;
  placeholder: string;
  onUpload: (formData: FormData) => Promise<any>;
  isUploading?: boolean;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const PhotoUpload: React.FC<PhotoUploadProps> = ({
  currentPhotoUrl,
  placeholder,
  onUpload,
  isUploading = false,
  disabled = false,
  size = 'md',
}) => {
  const [localError, setLocalError] = useState<string | null>(null);

  const sizeStyles = {
    sm: { width: 64, height: 64, fontSize: 20 },
    md: { width: 96, height: 96, fontSize: 32 },
    lg: { width: 128, height: 128, fontSize: 40 },
  };

  const currentSize = sizeStyles[size];

  const showImagePicker = () => {
    if (disabled || isUploading) return;

    Alert.alert(
      "Upload Avatar",
      "Choose how you'd like to upload your avatar:",
      [
        {
          text: "Camera",
          onPress: openCamera,
        },
        {
          text: "Photo Library",
          onPress: openImageLibrary,
        },
        {
          text: "Cancel",
          style: "cancel",
        },
      ]
    );
  };

  const requestPermissions = async (type: 'camera' | 'mediaLibrary') => {
    if (type === 'camera') {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please grant camera access to upload photos from camera.'
        );
        return false;
      }
    } else {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Photo Library Permission Required',
          'Please grant photo library access to upload photos.'
        );
        return false;
      }
    }
    return true;
  };

  const openCamera = async () => {
    const hasPermission = await requestPermissions('camera');
    if (!hasPermission) return;

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      await handleImageUpload(result.assets[0]);
    }
  };

  const openImageLibrary = async () => {
    const hasPermission = await requestPermissions('mediaLibrary');
    if (!hasPermission) return;

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      await handleImageUpload(result.assets[0]);
    }
  };

  const handleImageUpload = async (asset: ImagePicker.ImagePickerAsset) => {
    setLocalError(null);

    // Client-side validation
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    const mimeType = asset.mimeType || 'image/jpeg';
    
    if (!allowedTypes.includes(mimeType)) {
      setLocalError('Please select a JPEG, PNG, or WebP image file.');
      return;
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    if (asset.fileSize && asset.fileSize > maxSize) {
      setLocalError('File size must be less than 5MB.');
      return;
    }

    try {
      // Create FormData for upload
      const formData = new FormData();
      
      // For React Native, we need to create a file-like object
      const file = {
        uri: asset.uri,
        type: mimeType,
        name: `avatar.${mimeType.split('/')[1]}`,
      } as any;
      
      formData.append('avatar', file);
      
      await onUpload(formData);
      setLocalError(null);
    } catch (error: any) {
      console.error('Error uploading avatar:', error);
      setLocalError(error.message || 'Failed to upload avatar. Please try again.');
    }
  };

  return (
    <View style={{ position: 'relative' }}>
      <TouchableOpacity
        onPress={showImagePicker}
        disabled={disabled || isUploading}
        style={{
          width: currentSize.width,
          height: currentSize.height,
          borderRadius: currentSize.width / 2,
          backgroundColor: '#1f2937',
          borderWidth: 2,
          borderColor: '#3b82f6',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden',
          opacity: disabled || isUploading ? 0.5 : 1,
        }}
      >
        {currentPhotoUrl ? (
          <Image
            source={{ uri: currentPhotoUrl }}
            style={{
              width: currentSize.width,
              height: currentSize.height,
              borderRadius: currentSize.width / 2,
            }}
            resizeMode="cover"
          />
        ) : (
          <Text
            style={{
              fontSize: currentSize.fontSize,
              fontWeight: 'bold',
              color: '#60a5fa',
            }}
          >
            {placeholder.charAt(0).toUpperCase()}
          </Text>
        )}

        {/* Upload overlay */}
        {!disabled && !isUploading && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              borderRadius: currentSize.width / 2,
              alignItems: 'center',
              justifyContent: 'center',
              opacity: 0.8,
            }}
          >
            <View
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                padding: 8,
                borderRadius: 20,
              }}
            >
              <Camera width={20} height={20} color="#ffffff" />
            </View>
          </View>
        )}

        {/* Loading overlay */}
        {isUploading && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              borderRadius: currentSize.width / 2,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <ActivityIndicator size="small" color="#ffffff" />
          </View>
        )}
      </TouchableOpacity>

      {/* Error message */}
      {localError && (
        <View
          style={{
            position: 'absolute',
            top: currentSize.height + 8,
            left: 0,
            right: 0,
            backgroundColor: 'rgba(239, 68, 68, 0.9)',
            padding: 8,
            borderRadius: 8,
            zIndex: 10,
          }}
        >
          <Text style={{ color: '#ffffff', fontSize: 12, textAlign: 'center' }}>
            {localError}
          </Text>
        </View>
      )}
    </View>
  );
};

export default PhotoUpload;