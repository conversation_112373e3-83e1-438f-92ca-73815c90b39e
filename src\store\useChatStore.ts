import { create } from 'zustand';
import { Chat, Message } from '../types/chat';
import { chatService } from '../services/chatService';
import { chatWebSocketService } from '../services/chatWebSocketService';

interface TypingUser {
  userId: number;
  username: string;
}

interface ChatState {
  // Chat data
  chats: Chat[];
  currentChat: Chat | null;
  messages: Record<string, Message[]>;
  
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  
  // UI state
  typingUsers: Record<string, TypingUser[]>;
  unreadCounts: Record<string, number>;
  totalUnreadCount: number;
  
  // Loading states
  isLoadingChats: boolean;
  isLoadingMessages: Record<string, boolean>;
  
  // Actions
  initializeWebSocket: () => Promise<void>;
  disconnectWebSocket: () => void;
  
  // Chat management
  loadChats: () => Promise<void>;
  createChat: (participantIds: number[], name?: string, type?: 'direct' | 'group') => Promise<Chat>;
  selectChat: (chatId: string) => Promise<void>;
  leaveChat: (chatId: string) => Promise<void>;
  
  // Message management
  loadMessages: (chatId: string, loadMore?: boolean) => Promise<void>;
  sendMessage: (chatId: string, content: string) => Promise<void>;
  markAsRead: (chatId: string) => Promise<void>;
  
  // Typing indicators
  startTyping: (chatId: string) => void;
  stopTyping: (chatId: string) => void;
  
  // WebSocket event handlers
  handleNewMessage: (message: Message) => void;
  handleMessagesRead: (data: { chatId: string; readBy: number; readAt: string }) => void;
  handleTyping: (data: { chatId: string; userId: number; username: string; isTyping: boolean }) => void;
  handleConnectionChange: (connected: boolean) => void;
  
  // Utilities
  getChatUnreadCount: (chatId: string) => number;
  refreshUnreadCounts: () => Promise<void>;
  clearChat: () => void;
}

const MAX_MESSAGES_PER_CHAT = 500;
const MESSAGE_CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes

export const useChatStore = create<ChatState>((set, get) => ({
  // Initial state
  chats: [],
  currentChat: null,
  messages: {},
  isConnected: false,
  isConnecting: false,
  typingUsers: {},
  unreadCounts: {},
  totalUnreadCount: 0,
  isLoadingChats: false,
  isLoadingMessages: {},

  initializeWebSocket: async () => {
    const state = get();
    if (state.isConnected || state.isConnecting) {
      return;
    }

    set({ isConnecting: true });

    // Set up WebSocket callbacks
    chatWebSocketService.setCallbacks({
      onMessage: state.handleNewMessage,
      onMessagesRead: state.handleMessagesRead,
      onTyping: state.handleTyping,
      onConnectionChange: state.handleConnectionChange,
      onError: (error) => {
        console.error('WebSocket error:', error);
      },
    });

    try {
      const connected = await chatWebSocketService.connect();
      set({ isConnected: connected, isConnecting: false });
      
      if (connected) {
        // Load initial chat data
        state.loadChats();
        state.refreshUnreadCounts();
      }
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
      set({ isConnected: false, isConnecting: false });
    }
  },

  disconnectWebSocket: () => {
    chatWebSocketService.disconnect();
    set({ 
      isConnected: false, 
      isConnecting: false,
      typingUsers: {}
    });
  },

  loadChats: async () => {
    set({ isLoadingChats: true });
    try {
      const chats = await chatService.getAllChats({ limit: 50 });
      set({ chats, isLoadingChats: false });
    } catch (error) {
      console.error('Failed to load chats:', error);
      set({ isLoadingChats: false });
    }
  },

  createChat: async (participantIds: number[], name?: string, type: 'direct' | 'group' = 'direct') => {
    try {
      const newChat = await chatService.createChat(participantIds, name, type);
      set(state => ({
        chats: [newChat, ...state.chats]
      }));
      return newChat;
    } catch (error) {
      console.error('Failed to create chat:', error);
      throw error;
    }
  },

  selectChat: async (chatId: string) => {
    const state = get();
    const chat = state.chats.find(c => c.id === chatId);
    
    if (!chat) {
      try {
        const fetchedChat = await chatService.getChat(chatId);
        set(prevState => ({
          currentChat: fetchedChat,
          chats: prevState.chats.some(c => c.id === chatId) 
            ? prevState.chats 
            : [fetchedChat, ...prevState.chats]
        }));
      } catch (error) {
        console.error('Failed to fetch chat:', error);
        return;
      }
    } else {
      set({ currentChat: chat });
    }

    // Load messages for the selected chat
    await state.loadMessages(chatId);
    
    // Mark as read
    await state.markAsRead(chatId);
  },

  leaveChat: async (chatId: string) => {
    try {
      await chatService.leaveChat(chatId);
      set(state => ({
        chats: state.chats.filter(chat => chat.id !== chatId),
        currentChat: state.currentChat?.id === chatId ? null : state.currentChat,
        messages: Object.fromEntries(
          Object.entries(state.messages).filter(([id]) => id !== chatId)
        )
      }));
    } catch (error) {
      console.error('Failed to leave chat:', error);
      throw error;
    }
  },

  loadMessages: async (chatId: string, loadMore = false) => {
    const state = get();
    const currentMessages = state.messages[chatId] || [];
    
    if (state.isLoadingMessages[chatId]) {
      return;
    }

    set(prevState => ({
      isLoadingMessages: {
        ...prevState.isLoadingMessages,
        [chatId]: true
      }
    }));

    try {
      const params = loadMore && currentMessages.length > 0 
        ? { limit: 50, cursor: currentMessages[0]?.id }
        : { limit: 50 };

      const newMessages = await chatService.getChatMessages(chatId, params);
      
      set(prevState => {
        const existingMessages = prevState.messages[chatId] || [];
        const allMessages = loadMore 
          ? [...newMessages.reverse(), ...existingMessages]
          : newMessages.reverse();

        // Keep only the most recent messages to prevent memory issues
        const trimmedMessages = allMessages.slice(-MAX_MESSAGES_PER_CHAT);

        return {
          messages: {
            ...prevState.messages,
            [chatId]: trimmedMessages
          },
          isLoadingMessages: {
            ...prevState.isLoadingMessages,
            [chatId]: false
          }
        };
      });
    } catch (error) {
      console.error('Failed to load messages:', error);
      set(prevState => ({
        isLoadingMessages: {
          ...prevState.isLoadingMessages,
          [chatId]: false
        }
      }));
    }
  },

  sendMessage: async (chatId: string, content: string) => {
    if (!content.trim()) return;

    try {
      // For WebSocket, the actual message will come through the WebSocket event
      // For HTTP fallback, we get the message immediately
      const message = await chatService.sendMessage(chatId, content);
      
      // Only add message if it's from HTTP fallback (has real ID)
      if (!message.id.startsWith('temp-')) {
        get().handleNewMessage(message);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  },

  markAsRead: async (chatId: string) => {
    try {
      await chatService.markAsRead(chatId);
      
      // Update local unread count
      set(state => {
        const chat = state.chats.find(c => c.id === chatId);
        if (chat) {
          chat.unreadCount = 0;
        }
        
        return {
          unreadCounts: {
            ...state.unreadCounts,
            [chatId]: 0
          },
          chats: [...state.chats]
        };
      });
    } catch (error) {
      console.error('Failed to mark as read:', error);
    }
  },

  startTyping: (chatId: string) => {
    chatWebSocketService.sendTypingStart(chatId);
  },

  stopTyping: (chatId: string) => {
    chatWebSocketService.sendTypingStop(chatId);
  },

  handleNewMessage: (message: Message) => {
    set(state => {
      const chatId = message.id;
      const existingMessages = state.messages[chatId] || [];
      
      // Check for duplicate messages
      const isDuplicate = existingMessages.some(m => m.id === message.id);
      if (isDuplicate) {
        return state;
      }

      // Add message to the end (newest)
      const updatedMessages = [...existingMessages, message];
      
      // Update chat's last message and move to top
      const updatedChats = state.chats.map(chat => {
        if (chat.id === chatId) {
          return {
            ...chat,
            lastMessage: message,
            unreadCount: chat.unreadCount + 1
          };
        }
        return chat;
      }).sort((a, b) => {
        if (a.id === chatId) return -1;
        if (b.id === chatId) return 1;
        return 0;
      });

      return {
        messages: {
          ...state.messages,
          [chatId]: updatedMessages.slice(-MAX_MESSAGES_PER_CHAT)
        },
        chats: updatedChats,
        totalUnreadCount: state.totalUnreadCount + 1
      };
    });
  },

  handleMessagesRead: (data: { chatId: string; readBy: number; readAt: string }) => {
    set(state => {
      const updatedMessages = state.messages[data.chatId]?.map(message => ({
        ...message,
        isRead: true
      })) || [];

      return {
        messages: {
          ...state.messages,
          [data.chatId]: updatedMessages
        }
      };
    });
  },

  handleTyping: (data: { chatId: string; userId: number; username: string; isTyping: boolean }) => {
    set(state => {
      const currentTyping = state.typingUsers[data.chatId] || [];
      
      if (data.isTyping) {
        // Add user to typing list if not already there
        const isAlreadyTyping = currentTyping.some(user => user.userId === data.userId);
        if (!isAlreadyTyping) {
          return {
            typingUsers: {
              ...state.typingUsers,
              [data.chatId]: [...currentTyping, { userId: data.userId, username: data.username }]
            }
          };
        }
      } else {
        // Remove user from typing list
        return {
          typingUsers: {
            ...state.typingUsers,
            [data.chatId]: currentTyping.filter(user => user.userId !== data.userId)
          }
        };
      }
      
      return state;
    });
  },

  handleConnectionChange: (connected: boolean) => {
    set({ isConnected: connected });
    
    if (connected) {
      // Reload data when reconnected
      const state = get();
      state.loadChats();
      state.refreshUnreadCounts();
    }
  },

  getChatUnreadCount: (chatId: string) => {
    const state = get();
    const chat = state.chats.find(c => c.id === chatId);
    return chat?.unreadCount || state.unreadCounts[chatId] || 0;
  },

  refreshUnreadCounts: async () => {
    try {
      const totalUnread = await chatService.getUserUnreadCount();
      set({ totalUnreadCount: totalUnread });
    } catch (error) {
      console.error('Failed to refresh unread counts:', error);
    }
  },

  clearChat: () => {
    set({
      chats: [],
      currentChat: null,
      messages: {},
      typingUsers: {},
      unreadCounts: {},
      totalUnreadCount: 0,
    });
  },
}));