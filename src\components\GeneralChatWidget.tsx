import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { MessageCircle, Send, X, Users } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { chatService } from '../services/chatService';
import Toast from 'react-native-toast-message';

interface Props {
  isVisible: boolean;
  onToggle: () => void;
}

interface ChatMessage {
  id: string;
  content: string;
  sender: {
    id: number;
    username: string;
  };
  createdAt: string;
}

export const GeneralChatWidget: React.FC<Props> = ({ isVisible, onToggle }) => {
  const { user } = useAuthStore();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (isVisible) {
      loadMessages();
    } else {
      loadUnreadCount();
    }
  }, [isVisible]);

  useEffect(() => {
    // Load unread count on mount
    loadUnreadCount();
  }, []);

  const loadMessages = async () => {
    setLoading(true);
    try {
      const data = await chatService.getGeneralChatMessages({ limit: 50 });
      if (Array.isArray(data)) {
        setMessages(data.reverse()); // Show newest at bottom
        setUnreadCount(0);
        await chatService.markGeneralChatAsRead();
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load chat messages',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    try {
      const count = await chatService.getGeneralChatUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    setSending(true);
    try {
      const message = await chatService.sendGeneralChatMessage(newMessage.trim());
      setMessages(prev => [...prev, message]);
      setNewMessage('');
      setUnreadCount(0);
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to send message',
      });
    } finally {
      setSending(false);
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return 'now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return date.toLocaleDateString();
  };

  if (!isVisible) {
    return (
      <TouchableOpacity
        onPress={onToggle}
        style={{
          position: 'absolute',
          bottom: 20,
          right: 20,
          backgroundColor: '#3f87ff',
          borderRadius: 25,
          width: 50,
          height: 50,
          alignItems: 'center',
          justifyContent: 'center',
          elevation: 5,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }}
      >
        <MessageCircle width={24} height={24} color="#ffffff" />
        {unreadCount > 0 && (
          <View style={{
            position: 'absolute',
            top: -5,
            right: -5,
            backgroundColor: '#ef4444',
            borderRadius: 10,
            minWidth: 20,
            height: 20,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Text style={{
              color: '#ffffff',
              fontSize: 12,
              fontWeight: 'bold',
            }}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <View style={{
      position: 'absolute',
      bottom: 20,
      right: 20,
      backgroundColor: '#1F2937',
      borderRadius: 12,
      width: 300,
      height: 400,
      elevation: 10,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
      borderWidth: 1,
      borderColor: '#374151',
    }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#374151',
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Users width={20} height={20} color="#3f87ff" />
          <Text style={{
            color: '#ffffff',
            fontSize: 16,
            fontWeight: '600',
            marginLeft: 8,
          }}>
            General Chat
          </Text>
        </View>
        <TouchableOpacity onPress={onToggle}>
          <X width={20} height={20} color="#9CA3AF" />
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <View style={{ flex: 1, padding: 12 }}>
        {loading ? (
          <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
            <ActivityIndicator size="small" color="#3f87ff" />
            <Text style={{ color: '#9CA3AF', marginTop: 8 }}>Loading messages...</Text>
          </View>
        ) : (
          <ScrollView 
            style={{ flex: 1 }}
            showsVerticalScrollIndicator={false}
          >
            {messages.length === 0 ? (
              <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 40 }}>
                <MessageCircle width={32} height={32} color="#6B7280" />
                <Text style={{ color: '#9CA3AF', marginTop: 8, textAlign: 'center' }}>
                  No messages yet. Start the conversation!
                </Text>
              </View>
            ) : (
              messages.map((message) => (
                <View key={message.id} style={{ marginBottom: 12 }}>
                  <View style={{
                    backgroundColor: message.sender.id === user?.id ? '#3f87ff' : '#374151',
                    borderRadius: 8,
                    padding: 8,
                    maxWidth: '80%',
                    alignSelf: message.sender.id === user?.id ? 'flex-end' : 'flex-start',
                  }}>
                    {message.sender.id !== user?.id && (
                      <Text style={{
                        color: '#F59E0B',
                        fontSize: 12,
                        fontWeight: '600',
                        marginBottom: 2,
                      }}>
                        {message.sender.username}
                      </Text>
                    )}
                    <Text style={{
                      color: '#ffffff',
                      fontSize: 14,
                    }}>
                      {message.content}
                    </Text>
                    <Text style={{
                      color: message.sender.id === user?.id ? '#E5E7EB' : '#9CA3AF',
                      fontSize: 10,
                      marginTop: 2,
                      textAlign: 'right',
                    }}>
                      {formatTime(message.createdAt)}
                    </Text>
                  </View>
                </View>
              ))
            )}
          </ScrollView>
        )}
      </View>

      {/* Input */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        padding: 12,
        borderTopWidth: 1,
        borderTopColor: '#374151',
      }}>
        <TextInput
          style={{
            flex: 1,
            backgroundColor: '#374151',
            borderRadius: 20,
            paddingHorizontal: 12,
            paddingVertical: 8,
            color: '#ffffff',
            fontSize: 14,
            marginRight: 8,
          }}
          placeholder="Type a message..."
          placeholderTextColor="#9CA3AF"
          value={newMessage}
          onChangeText={setNewMessage}
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          onPress={sendMessage}
          disabled={!newMessage.trim() || sending}
          style={{
            backgroundColor: newMessage.trim() && !sending ? '#3f87ff' : '#6B7280',
            borderRadius: 20,
            width: 36,
            height: 36,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {sending ? (
            <ActivityIndicator size="small" color="#ffffff" />
          ) : (
            <Send width={16} height={16} color="#ffffff" />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};