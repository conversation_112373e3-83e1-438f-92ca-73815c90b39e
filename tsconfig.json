{"compilerOptions": {"jsx": "react-jsx", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "lib": ["dom", "esnext"], "moduleResolution": "bundler", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "strict": true, "target": "esnext"}, "extends": "expo/tsconfig.base", "include": ["**/*.ts", "**/*.tsx", "nativewind-env.d.ts"], "exclude": ["**/node_modules"]}