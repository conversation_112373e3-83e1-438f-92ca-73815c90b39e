// Array of distinct colors for states - matching frontend exactly
const baseColors = [
  '#FF6B6B', // Red
  '#4ECDC4', // Turquoise
  '#45B7D1', // Light Blue
  '#96CEB4', // Mint
  '#FFEEAD', // Light Yellow
  '#D4A5A5', // <PERSON>
  '#9B59B6', // Purple
  '#3498DB', // Blue
  '#E67E22', // Orange
  '#2ECC71', // Green
  '#F1C40F', // Yellow
  '#E74C3C', // Dark Red
  '#1ABC9C', // Turquoise
  '#9B59B6', // Amethyst
  '#34495E', // Navy Blue
];

export const generateStateColor = (index: number): string => {
  // If we run out of base colors, generate a random one
  if (index >= baseColors.length) {
    return `#${Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')}`;
  }
  return baseColors[index];
};

// Helper function to generate a lighter version of a color
export const lightenColor = (color: string, percent: number): string => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  const newR = Math.min(255, Math.floor(r + (255 - r) * percent));
  const newG = Math.min(255, Math.floor(g + (255 - g) * percent));
  const newB = Math.min(255, Math.floor(b + (255 - b) * percent));
  
  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
};

// Helper function to darken a color
export const darkenColor = (color: string, percent: number): string => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  const newR = Math.floor(r * (1 - percent));
  const newG = Math.floor(g * (1 - percent));
  const newB = Math.floor(b * (1 - percent));
  
  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
};