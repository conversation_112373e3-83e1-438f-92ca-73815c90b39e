import { getApiUrl } from './platformUtils';

export interface NetworkTestResult {
  success: boolean;
  message: string;
  details?: any;
  url?: string;
}

// Test basic internet connectivity
export const testInternetConnection = async (): Promise<NetworkTestResult> => {
  try {
    const response = await fetch('https://httpbin.org/get', {
      method: 'GET',
      timeout: 5000,
    });
    
    if (response.ok) {
      return {
        success: true,
        message: 'Internet connection is working',
        url: 'https://httpbin.org/get'
      };
    } else {
      return {
        success: false,
        message: 'Internet connection test failed',
        details: response.status
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: 'No internet connection',
      details: error.message
    };
  }
};

// Test connection to your backend server
export const testBackendConnection = async (): Promise<NetworkTestResult> => {
  const apiUrl = getApiUrl();
  
  try {
    console.log('🧪 Testing backend connection to:', apiUrl);
    
    // Try to reach the backend health endpoint or any simple endpoint
    const response = await fetch(`${apiUrl}/health`, {
      method: 'GET',
      timeout: 10000,
    });
    
    if (response.ok) {
      return {
        success: true,
        message: 'Backend server is reachable',
        url: apiUrl
      };
    } else {
      return {
        success: false,
        message: `Backend server responded with status ${response.status}`,
        details: response.status,
        url: apiUrl
      };
    }
  } catch (error: any) {
    console.log('🚨 Backend connection test failed:', error);
    
    let message = 'Cannot connect to backend server';
    
    if (error.message?.includes('Network request failed')) {
      message = `Cannot reach backend at ${apiUrl}. Check if server is running and IP is correct.`;
    } else if (error.message?.includes('timeout')) {
      message = `Connection to ${apiUrl} timed out. Server might be slow or unreachable.`;
    }
    
    return {
      success: false,
      message,
      details: error.message,
      url: apiUrl
    };
  }
};

// Run comprehensive network tests
export const runNetworkDiagnostics = async (): Promise<{
  internet: NetworkTestResult;
  backend: NetworkTestResult;
  summary: string;
}> => {
  console.log('🔍 Running network diagnostics...');
  
  const internet = await testInternetConnection();
  const backend = await testBackendConnection();
  
  let summary = '';
  
  if (!internet.success) {
    summary = 'No internet connection detected. Check your WiFi or mobile data.';
  } else if (!backend.success) {
    summary = 'Internet works but cannot reach backend server. Check server URL and ensure backend is running.';
  } else {
    summary = 'All network tests passed successfully.';
  }
  
  return {
    internet,
    backend,
    summary
  };
};

// Get network information for debugging
export const getNetworkInfo = () => {
  const apiUrl = getApiUrl();
  
  return {
    apiUrl,
    timestamp: new Date().toISOString(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'React Native',
  };
};
