import { 
  ImagePickerResponse, 
  MediaType, 
  launchImageLibrary, 
  launchCamera,
  ImageLibraryOptions,
  CameraOptions 
} from 'react-native-image-picker';
import ImageResizer, { Response as ResizerResponse } from 'react-native-image-resizer';
import { Alert, PermissionsAndroid, Platform } from 'react-native';
import { api } from './api';

export interface ImagePickerConfig {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  allowsEditing?: boolean;
  mediaType?: MediaType;
  includeBase64?: boolean;
}

export interface ProcessedImage {
  uri: string;
  type: string;
  name: string;
  size: number;
  width?: number;
  height?: number;
}

export interface UploadedImage {
  id: string;
  url: string;
  thumbnailUrl?: string;
  originalName: string;
  size: number;
  mimeType: string;
  width?: number;
  height?: number;
}

class ImageService {
  private defaultConfig: ImagePickerConfig = {
    maxWidth: 1920,
    maxHeight: 1920,
    quality: 0.8,
    allowsEditing: true,
    mediaType: 'photo',
    includeBase64: false,
  };

  async requestCameraPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.CAMERA,
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        ]);

        return (
          granted[PermissionsAndroid.PERMISSIONS.CAMERA] === PermissionsAndroid.RESULTS.GRANTED &&
          granted[PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE] === PermissionsAndroid.RESULTS.GRANTED
        );
      } catch (err) {
        console.warn('Camera permission request failed:', err);
        return false;
      }
    }
    return true;
  }

  showImagePicker(config?: ImagePickerConfig): Promise<ProcessedImage[]> {
    return new Promise((resolve, reject) => {
      const options = { ...this.defaultConfig, ...config };

      Alert.alert(
        'Select Image',
        'Choose from where you want to select an image',
        [
          { text: 'Camera', onPress: () => this.openCamera(options, resolve, reject) },
          { text: 'Gallery', onPress: () => this.openGallery(options, resolve, reject) },
          { text: 'Cancel', style: 'cancel', onPress: () => resolve([]) },
        ]
      );
    });
  }

  private async openCamera(
    options: ImagePickerConfig,
    resolve: (images: ProcessedImage[]) => void,
    reject: (error: any) => void
  ): Promise<void> {
    const hasPermission = await this.requestCameraPermission();
    if (!hasPermission) {
      Alert.alert(
        'Permission Required',
        'Camera permission is required to take photos',
        [{ text: 'OK' }]
      );
      resolve([]);
      return;
    }

    const cameraOptions: CameraOptions = {
      mediaType: options.mediaType || 'photo',
      quality: options.quality || 0.8,
      maxWidth: options.maxWidth,
      maxHeight: options.maxHeight,
      includeBase64: options.includeBase64 || false,
    };

    launchCamera(cameraOptions, (response) => {
      this.handleImagePickerResponse(response, resolve, reject);
    });
  }

  private openGallery(
    options: ImagePickerConfig,
    resolve: (images: ProcessedImage[]) => void,
    reject: (error: any) => void
  ): void {
    const libraryOptions: ImageLibraryOptions = {
      mediaType: options.mediaType || 'photo',
      quality: options.quality || 0.8,
      maxWidth: options.maxWidth,
      maxHeight: options.maxHeight,
      includeBase64: options.includeBase64 || false,
      selectionLimit: 1, // Can be made configurable
    };

    launchImageLibrary(libraryOptions, (response) => {
      this.handleImagePickerResponse(response, resolve, reject);
    });
  }

  private async handleImagePickerResponse(
    response: ImagePickerResponse,
    resolve: (images: ProcessedImage[]) => void,
    reject: (error: any) => void
  ): Promise<void> {
    if (response.didCancel || response.errorMessage) {
      if (response.errorMessage) {
        reject(new Error(response.errorMessage));
      } else {
        resolve([]);
      }
      return;
    }

    if (!response.assets || response.assets.length === 0) {
      resolve([]);
      return;
    }

    try {
      const processedImages: ProcessedImage[] = [];

      for (const asset of response.assets) {
        if (asset.uri) {
          const processed = await this.processImage(asset);
          if (processed) {
            processedImages.push(processed);
          }
        }
      }

      resolve(processedImages);
    } catch (error) {
      reject(error);
    }
  }

  private async processImage(asset: any): Promise<ProcessedImage | null> {
    try {
      if (!asset.uri) return null;

      // Generate a filename if not provided
      const fileName = asset.fileName || `image_${Date.now()}.jpg`;
      const fileType = asset.type || 'image/jpeg';

      // Resize image if it's too large
      let processedUri = asset.uri;
      let processedWidth = asset.width;
      let processedHeight = asset.height;
      let processedSize = asset.fileSize;

      if (asset.width && asset.height) {
        const maxDimension = Math.max(asset.width, asset.height);
        if (maxDimension > 1920) {
          const resizeResult = await this.resizeImage(asset.uri, {
            maxWidth: 1920,
            maxHeight: 1920,
            quality: 0.8,
          });

          if (resizeResult) {
            processedUri = resizeResult.uri;
            processedWidth = resizeResult.width;
            processedHeight = resizeResult.height;
            processedSize = resizeResult.size;
          }
        }
      }

      return {
        uri: processedUri,
        type: fileType,
        name: fileName,
        size: processedSize || 0,
        width: processedWidth,
        height: processedHeight,
      };
    } catch (error) {
      console.error('Error processing image:', error);
      return null;
    }
  }

  private async resizeImage(
    uri: string,
    options: {
      maxWidth: number;
      maxHeight: number;
      quality: number;
    }
  ): Promise<ResizerResponse | null> {
    try {
      const resized = await ImageResizer.createResizedImage(
        uri,
        options.maxWidth,
        options.maxHeight,
        'JPEG',
        options.quality * 100, // ImageResizer expects 0-100
        0, // rotation
        undefined, // outputPath
        false, // keepMeta
        {
          mode: 'contain',
          onlyScaleDown: true,
        }
      );

      return resized;
    } catch (error) {
      console.error('Error resizing image:', error);
      return null;
    }
  }

  async uploadImage(
    image: ProcessedImage,
    endpoint: string,
    additionalData?: Record<string, any>
  ): Promise<UploadedImage> {
    try {
      const formData = new FormData();
      
      formData.append('image', {
        uri: image.uri,
        type: image.type,
        name: image.name,
      } as any);

      // Add any additional form data
      if (additionalData) {
        Object.keys(additionalData).forEach(key => {
          formData.append(key, additionalData[key]);
        });
      }

      const response = await api.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }

  // Specific upload methods for different types
  async uploadUserAvatar(image: ProcessedImage): Promise<UploadedImage> {
    return this.uploadImage(image, '/users/avatar');
  }

  async uploadStatePicture(stateId: string, image: ProcessedImage): Promise<UploadedImage> {
    return this.uploadImage(image, `/states/${stateId}/picture`);
  }

  async uploadPartyPicture(partyId: string, image: ProcessedImage): Promise<UploadedImage> {
    return this.uploadImage(image, `/party/${partyId}/picture`);
  }

  // Utility methods
  async pickAndUploadUserAvatar(): Promise<UploadedImage | null> {
    try {
      const images = await this.showImagePicker({
        maxWidth: 512,
        maxHeight: 512,
        quality: 0.8,
      });

      if (images.length === 0) {
        return null;
      }

      const uploadedImage = await this.uploadUserAvatar(images[0]);
      return uploadedImage;
    } catch (error) {
      console.error('Error picking and uploading avatar:', error);
      Alert.alert('Upload Failed', 'Failed to upload avatar. Please try again.');
      return null;
    }
  }

  async pickAndUploadStatePicture(stateId: string): Promise<UploadedImage | null> {
    try {
      const images = await this.showImagePicker({
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 0.9,
      });

      if (images.length === 0) {
        return null;
      }

      const uploadedImage = await this.uploadStatePicture(stateId, images[0]);
      return uploadedImage;
    } catch (error) {
      console.error('Error picking and uploading state picture:', error);
      Alert.alert('Upload Failed', 'Failed to upload state picture. Please try again.');
      return null;
    }
  }

  async pickAndUploadPartyPicture(partyId: string): Promise<UploadedImage | null> {
    try {
      const images = await this.showImagePicker({
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 0.9,
      });

      if (images.length === 0) {
        return null;
      }

      const uploadedImage = await this.uploadPartyPicture(partyId, images[0]);
      return uploadedImage;
    } catch (error) {
      console.error('Error picking and uploading party picture:', error);
      Alert.alert('Upload Failed', 'Failed to upload party picture. Please try again.');
      return null;
    }
  }

  // Image validation
  validateImage(image: ProcessedImage, maxSizeMB: number = 10): boolean {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    
    if (image.size > maxSizeBytes) {
      Alert.alert(
        'File Too Large',
        `Image size must be less than ${maxSizeMB}MB. Current size: ${(image.size / 1024 / 1024).toFixed(2)}MB`
      );
      return false;
    }

    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!supportedTypes.includes(image.type.toLowerCase())) {
      Alert.alert(
        'Unsupported Format',
        'Please select a JPEG, PNG, or WebP image.'
      );
      return false;
    }

    return true;
  }

  // Get optimized image URI (for caching)
  getOptimizedImageUri(originalUri: string, width?: number, height?: number): string {
    // This could be enhanced to work with CDN transformations
    // For now, return the original URI
    return originalUri;
  }

  // Clear image cache
  async clearImageCache(): Promise<void> {
    try {
      // Implementation would depend on the caching library used
      console.log('Image cache cleared');
    } catch (error) {
      console.error('Error clearing image cache:', error);
    }
  }
}

// Export singleton instance
export const imageService = new ImageService();