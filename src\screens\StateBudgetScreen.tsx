import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import { 
  Shield, 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Settings, 
  AlertTriangle,
  ArrowLeft,
  Coins,
  FileText,
  Calendar
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { stateService } from '../services/stateService';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
  route: any;
}

interface State {
  id: number;
  name: string;
  treasury: number;
  leader: {
    id: number;
    username: string;
  };
  taxRate: number;
  regions: any[];
}

interface BudgetTransaction {
  id: number;
  amount: number;
  type: 'income' | 'expense';
  description: string;
  createdAt: string;
  category: string;
}

interface TaxConfiguration {
  taxRate: number;
  travelTaxRate: number;
  factoryTaxRate: number;
  warTaxRate: number;
}

export const StateBudgetScreen: React.FC<Props> = ({ navigation, route }) => {
  const { user } = useAuthStore();
  const stateId = route?.params?.stateId;
  useAuthGuard({ navigation });

  const [state, setState] = useState<State | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isLeader, setIsLeader] = useState(false);
  const [budgetTransactions, setBudgetTransactions] = useState<BudgetTransaction[]>([]);
  const [taxConfig, setTaxConfig] = useState<TaxConfiguration>({
    taxRate: 0,
    travelTaxRate: 0,
    factoryTaxRate: 0,
    warTaxRate: 0,
  });
  const [showTaxModal, setShowTaxModal] = useState(false);
  const [updatingTax, setUpdatingTax] = useState(false);

  useEffect(() => {
    if (stateId) {
      loadStateData();
    }
  }, [stateId]);

  const loadStateData = async () => {
    try {
      setLoading(true);
      const [stateData, transactions, taxConfigData] = await Promise.all([
        stateService.getState(stateId),
        stateService.getBudgetTransactions(stateId).catch(() => []),
        stateService.getTaxConfiguration(stateId).catch(() => ({
          taxRate: 0,
          travelTaxRate: 0,
          factoryTaxRate: 0,
          warTaxRate: 0,
        }))
      ]);

      setState(stateData);
      setBudgetTransactions(transactions);
      setTaxConfig(taxConfigData);

      // Check if user is the state leader
      const userIsLeader = user?.id === stateData.leader.id;
      setIsLeader(userIsLeader);

      if (!userIsLeader) {
        Toast.show({
          type: 'error',
          text1: 'Access Denied',
          text2: 'You must be the state leader to access the budget dashboard'
        });
        navigation.goBack();
        return;
      }
    } catch (error: any) {
      console.error('Failed to load state data:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.response?.data?.message || 'Failed to load state data'
      });
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadStateData();
    setRefreshing(false);
  };

  const updateTaxConfiguration = async () => {
    setUpdatingTax(true);
    try {
      await stateService.updateTaxConfiguration(stateId, taxConfig);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Tax configuration updated successfully'
      });
      setShowTaxModal(false);
      await loadStateData();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.response?.data?.message || 'Failed to update tax configuration'
      });
    } finally {
      setUpdatingTax(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString();
  };

  const getTransactionColor = (type: string) => {
    return type === 'income' ? '#10B981' : '#EF4444';
  };

  const getTransactionIcon = (type: string) => {
    return type === 'income' ? TrendingUp : TrendingDown;
  };

  if (loading) {
    return (
      <View style={{ flex: 1, backgroundColor: '#111827', justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#3f87ff" />
        <Text style={{ color: '#3f87ff', fontSize: 18, marginTop: 16 }}>
          Loading budget dashboard...
        </Text>
      </View>
    );
  }

  if (!state || !isLeader) {
    return (
      <View style={{ flex: 1, backgroundColor: '#111827', justifyContent: 'center', alignItems: 'center', padding: 16 }}>
        <AlertTriangle width={48} height={48} color="#EF4444" />
        <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: '600', marginTop: 16, textAlign: 'center' }}>
          Access Denied
        </Text>
        <Text style={{ color: '#EF4444', fontSize: 16, marginTop: 8, textAlign: 'center' }}>
          You do not have permission to access this budget dashboard.
        </Text>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            backgroundColor: '#3f87ff',
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 8,
            marginTop: 20,
          }}
        >
          <Text style={{ color: '#ffffff', fontSize: 16 }}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#111827' }}>
      {/* Header */}
      <View style={{ padding: 16, paddingTop: 40, borderBottomWidth: 1, borderBottomColor: '#374151' }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{ marginRight: 12 }}
          >
            <ArrowLeft width={24} height={24} color="#9CA3AF" />
          </TouchableOpacity>
          
          <View style={{ flex: 1 }}>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff' }}>
              {state.name} Budget
            </Text>
            <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
              State financial management and tax configuration
            </Text>
          </View>
        </View>

        {/* Leader Badge */}
        <View style={{
          backgroundColor: 'rgba(59, 130, 246, 0.2)',
          borderWidth: 1,
          borderColor: 'rgba(59, 130, 246, 0.3)',
          borderRadius: 8,
          paddingHorizontal: 12,
          paddingVertical: 6,
          alignSelf: 'flex-start',
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <Shield width={16} height={16} color="#3f87ff" />
          <Text style={{ color: '#3f87ff', fontSize: 14, fontWeight: '500', marginLeft: 6 }}>
            State Leader Access
          </Text>
        </View>
      </View>

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <View style={{ padding: 16 }}>
          {/* Budget Overview */}
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 20,
            marginBottom: 20,
          }}>
            <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', marginBottom: 16 }}>
              Treasury Overview
            </Text>

            <View style={{ alignItems: 'center', marginBottom: 20 }}>
              <Coins width={48} height={48} color="#F59E0B" />
              <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#ffffff', marginTop: 12 }}>
                {formatCurrency(state.treasury)}
              </Text>
              <Text style={{ color: '#9CA3AF', fontSize: 16 }}>Current Treasury Balance</Text>
            </View>

            <View style={{
              backgroundColor: '#374151',
              borderRadius: 8,
              padding: 16,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
              <Text style={{ color: '#9CA3AF', fontSize: 16 }}>Base Tax Rate:</Text>
              <Text style={{ color: '#ffffff', fontSize: 18, fontWeight: '600' }}>
                {taxConfig.taxRate}%
              </Text>
            </View>
          </View>

          {/* Tax Configuration */}
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 20,
            marginBottom: 20,
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff' }}>
                Tax Configuration
              </Text>
              <TouchableOpacity
                onPress={() => setShowTaxModal(true)}
                style={{
                  backgroundColor: '#3f87ff',
                  paddingHorizontal: 12,
                  paddingVertical: 6,
                  borderRadius: 6,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
              >
                <Settings width={16} height={16} color="#ffffff" />
                <Text style={{ color: '#ffffff', fontSize: 14, marginLeft: 4 }}>Edit</Text>
              </TouchableOpacity>
            </View>

            <View style={{ gap: 12 }}>
              <View style={{
                backgroundColor: '#374151',
                borderRadius: 8,
                padding: 16,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
                <Text style={{ color: '#9CA3AF' }}>Travel Tax:</Text>
                <Text style={{ color: '#ffffff', fontWeight: '600' }}>{taxConfig.travelTaxRate}%</Text>
              </View>

              <View style={{
                backgroundColor: '#374151',
                borderRadius: 8,
                padding: 16,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
                <Text style={{ color: '#9CA3AF' }}>Factory Tax:</Text>
                <Text style={{ color: '#ffffff', fontWeight: '600' }}>{taxConfig.factoryTaxRate}%</Text>
              </View>

              <View style={{
                backgroundColor: '#374151',
                borderRadius: 8,
                padding: 16,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
                <Text style={{ color: '#9CA3AF' }}>War Tax:</Text>
                <Text style={{ color: '#ffffff', fontWeight: '600' }}>{taxConfig.warTaxRate}%</Text>
              </View>
            </View>
          </View>

          {/* Recent Transactions */}
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 20,
          }}>
            <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', marginBottom: 16 }}>
              Recent Transactions
            </Text>

            {budgetTransactions.length > 0 ? (
              <View style={{ gap: 12 }}>
                {budgetTransactions.slice(0, 10).map((transaction) => {
                  const IconComponent = getTransactionIcon(transaction.type);
                  const color = getTransactionColor(transaction.type);
                  
                  return (
                    <View
                      key={transaction.id}
                      style={{
                        backgroundColor: '#374151',
                        borderRadius: 8,
                        padding: 16,
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}
                    >
                      <IconComponent width={20} height={20} color={color} />
                      
                      <View style={{ flex: 1, marginLeft: 12 }}>
                        <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '500', marginBottom: 2 }}>
                          {transaction.description}
                        </Text>
                        <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
                          {new Date(transaction.createdAt).toLocaleDateString()} • {transaction.category}
                        </Text>
                      </View>
                      
                      <Text style={{ color: color, fontSize: 16, fontWeight: '600' }}>
                        {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                      </Text>
                    </View>
                  );
                })}
                
                {budgetTransactions.length > 10 && (
                  <TouchableOpacity
                    style={{
                      backgroundColor: '#374151',
                      borderRadius: 8,
                      padding: 12,
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{ color: '#3f87ff', fontSize: 14 }}>
                      View All Transactions ({budgetTransactions.length - 10} more)
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : (
              <View style={{ alignItems: 'center', paddingVertical: 40 }}>
                <FileText width={48} height={48} color="#6B7280" />
                <Text style={{ color: '#9CA3AF', fontSize: 16, marginTop: 16 }}>
                  No transactions yet
                </Text>
                <Text style={{ color: '#6B7280', fontSize: 14, marginTop: 8, textAlign: 'center' }}>
                  Budget transactions will appear here as they occur
                </Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Tax Configuration Modal */}
      <Modal visible={showTaxModal} transparent animationType="slide">
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 16,
        }}>
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 24,
            width: '100%',
            maxWidth: 400,
          }}>
            <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', marginBottom: 20 }}>
              Update Tax Configuration
            </Text>

            <View style={{ gap: 16 }}>
              <View>
                <Text style={{ color: '#9CA3AF', fontSize: 16, marginBottom: 8 }}>
                  Base Tax Rate (%)
                </Text>
                <TextInput
                  value={taxConfig.taxRate.toString()}
                  onChangeText={(text) => setTaxConfig(prev => ({ 
                    ...prev, 
                    taxRate: parseFloat(text) || 0 
                  }))}
                  style={{
                    backgroundColor: '#374151',
                    borderWidth: 1,
                    borderColor: '#4B5563',
                    borderRadius: 8,
                    padding: 12,
                    color: '#ffffff',
                    fontSize: 16,
                  }}
                  placeholder="0"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="numeric"
                />
              </View>

              <View>
                <Text style={{ color: '#9CA3AF', fontSize: 16, marginBottom: 8 }}>
                  Travel Tax Rate (%)
                </Text>
                <TextInput
                  value={taxConfig.travelTaxRate.toString()}
                  onChangeText={(text) => setTaxConfig(prev => ({ 
                    ...prev, 
                    travelTaxRate: parseFloat(text) || 0 
                  }))}
                  style={{
                    backgroundColor: '#374151',
                    borderWidth: 1,
                    borderColor: '#4B5563',
                    borderRadius: 8,
                    padding: 12,
                    color: '#ffffff',
                    fontSize: 16,
                  }}
                  placeholder="0"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="numeric"
                />
              </View>

              <View>
                <Text style={{ color: '#9CA3AF', fontSize: 16, marginBottom: 8 }}>
                  Factory Tax Rate (%)
                </Text>
                <TextInput
                  value={taxConfig.factoryTaxRate.toString()}
                  onChangeText={(text) => setTaxConfig(prev => ({ 
                    ...prev, 
                    factoryTaxRate: parseFloat(text) || 0 
                  }))}
                  style={{
                    backgroundColor: '#374151',
                    borderWidth: 1,
                    borderColor: '#4B5563',
                    borderRadius: 8,
                    padding: 12,
                    color: '#ffffff',
                    fontSize: 16,
                  }}
                  placeholder="0"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="numeric"
                />
              </View>

              <View>
                <Text style={{ color: '#9CA3AF', fontSize: 16, marginBottom: 8 }}>
                  War Tax Rate (%)
                </Text>
                <TextInput
                  value={taxConfig.warTaxRate.toString()}
                  onChangeText={(text) => setTaxConfig(prev => ({ 
                    ...prev, 
                    warTaxRate: parseFloat(text) || 0 
                  }))}
                  style={{
                    backgroundColor: '#374151',
                    borderWidth: 1,
                    borderColor: '#4B5563',
                    borderRadius: 8,
                    padding: 12,
                    color: '#ffffff',
                    fontSize: 16,
                  }}
                  placeholder="0"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={{ flexDirection: 'row', gap: 12, marginTop: 24 }}>
              <TouchableOpacity
                onPress={() => setShowTaxModal(false)}
                style={{
                  flex: 1,
                  backgroundColor: '#4B5563',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                }}
              >
                <Text style={{ color: '#ffffff', fontSize: 16 }}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                onPress={updateTaxConfiguration}
                disabled={updatingTax}
                style={{
                  flex: 1,
                  backgroundColor: updatingTax ? '#1E3A8A' : '#3f87ff',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                }}
              >
                {updatingTax && <ActivityIndicator size="small" color="#ffffff" style={{ marginRight: 8 }} />}
                <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
                  {updatingTax ? 'Updating...' : 'Update'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};