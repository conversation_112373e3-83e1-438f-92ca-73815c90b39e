import analytics from '@react-native-firebase/analytics';
import crashlytics from '@react-native-firebase/crashlytics';
import { Platform } from 'react-native';

export interface UserProperties {
  level?: number;
  stateId?: string;
  stateName?: string;
  premiumUser?: boolean;
  registrationDate?: string;
}

export interface CustomEvent {
  name: string;
  parameters?: Record<string, any>;
}

export interface ScreenViewEvent {
  screenName: string;
  screenClass?: string;
}

class AnalyticsService {
  private isInitialized = false;
  private userId: string | null = null;

  async initialize(): Promise<void> {
    try {
      // Set analytics collection enabled
      await analytics().setAnalyticsCollectionEnabled(true);
      
      // Set crashlytics collection enabled
      await crashlytics().setCrashlyticsCollectionEnabled(true);
      
      this.isInitialized = true;
      console.log('Analytics service initialized');
      
      // Log app open event
      this.logEvent('app_open', {
        platform: Platform.OS,
        version: Platform.Version.toString(),
      });
    } catch (error) {
      console.error('Failed to initialize analytics:', error);
    }
  }

  // User identification
  async setUserId(userId: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      this.userId = userId;
      await analytics().setUserId(userId);
      await crashlytics().setUserId(userId);
    } catch (error) {
      console.error('Failed to set user ID:', error);
    }
  }

  async setUserProperties(properties: UserProperties): Promise<void> {
    if (!this.isInitialized) return;

    try {
      // Set analytics user properties
      for (const [key, value] of Object.entries(properties)) {
        if (value !== undefined && value !== null) {
          await analytics().setUserProperty(key, value.toString());
        }
      }

      // Set crashlytics user attributes
      for (const [key, value] of Object.entries(properties)) {
        if (value !== undefined && value !== null) {
          await crashlytics().setAttributes({
            [key]: value.toString(),
          });
        }
      }
    } catch (error) {
      console.error('Failed to set user properties:', error);
    }
  }

  // Event logging
  async logEvent(eventName: string, parameters?: Record<string, any>): Promise<void> {
    if (!this.isInitialized) return;

    try {
      await analytics().logEvent(eventName, parameters);
    } catch (error) {
      console.error(`Failed to log event ${eventName}:`, error);
    }
  }

  async logScreenView(screenName: string, screenClass?: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      await analytics().logScreenView({
        screen_name: screenName,
        screen_class: screenClass || screenName,
      });
    } catch (error) {
      console.error(`Failed to log screen view ${screenName}:`, error);
    }
  }

  // Game-specific events
  async logLogin(method: string = 'email'): Promise<void> {
    await this.logEvent('login', { method });
  }

  async logSignUp(method: string = 'email'): Promise<void> {
    await this.logEvent('sign_up', { method });
  }

  async logWarParticipation(warId: string, side: string, energySpent: number): Promise<void> {
    await this.logEvent('war_participation', {
      war_id: warId,
      side,
      energy_spent: energySpent,
    });
  }

  async logWarDeclaration(targetStateId: string, warType: string): Promise<void> {
    await this.logEvent('war_declaration', {
      target_state_id: targetStateId,
      war_type: warType,
    });
  }

  async logChatMessage(chatType: 'direct' | 'group' | 'general'): Promise<void> {
    await this.logEvent('chat_message_sent', {
      chat_type: chatType,
    });
  }

  async logElectionVote(electionId: string, candidateId: number): Promise<void> {
    await this.logEvent('election_vote', {
      election_id: electionId,
      candidate_id: candidateId,
    });
  }

  async logFactoryWork(factoryType: string, energySpent: number): Promise<void> {
    await this.logEvent('factory_work', {
      factory_type: factoryType,
      energy_spent: energySpent,
    });
  }

  async logTraining(skill: string, currency: string, amount: number): Promise<void> {
    await this.logEvent('user_training', {
      skill,
      currency,
      amount,
    });
  }

  async logMoneyTransfer(amount: number, recipientId: number): Promise<void> {
    await this.logEvent('money_transfer', {
      amount,
      recipient_id: recipientId,
    });
  }

  async logRegionMove(fromRegion: string, toRegion: string): Promise<void> {
    await this.logEvent('region_move', {
      from_region: fromRegion,
      to_region: toRegion,
    });
  }

  async logStateCreation(stateName: string, governmentType: string): Promise<void> {
    await this.logEvent('state_creation', {
      state_name: stateName,
      government_type: governmentType,
    });
  }

  async logPartyJoin(partyId: string): Promise<void> {
    await this.logEvent('party_join', {
      party_id: partyId,
    });
  }

  async logPremiumPurchase(planId: string, amount: number): Promise<void> {
    await this.logEvent('purchase', {
      item_id: planId,
      value: amount,
      currency: 'USD',
    });
  }

  async logGoldPurchase(packageId: string, goldAmount: number, cost: number): Promise<void> {
    await this.logEvent('purchase', {
      item_id: packageId,
      item_name: 'gold',
      quantity: goldAmount,
      value: cost,
      currency: 'USD',
    });
  }

  // Error and crash reporting
  async recordError(error: Error, context?: Record<string, any>): Promise<void> {
    if (!this.isInitialized) return;

    try {
      // Set additional context
      if (context) {
        await crashlytics().setAttributes(context);
      }

      // Record the error
      crashlytics().recordError(error);
    } catch (crashError) {
      console.error('Failed to record error:', crashError);
    }
  }

  async log(message: string, level: 'debug' | 'info' | 'warn' | 'error' = 'info'): Promise<void> {
    if (!this.isInitialized) return;

    try {
      crashlytics().log(message);
    } catch (error) {
      console.error('Failed to log message:', error);
    }
  }

  // Custom crash
  async crash(message?: string): Promise<void> {
    if (!this.isInitialized) return;
    
    if (message) {
      await this.log(message, 'error');
    }
    
    crashlytics().crash();
  }

  // Performance monitoring
  async logTiming(name: string, duration: number, category?: string): Promise<void> {
    await this.logEvent('timing_complete', {
      name,
      value: duration,
      category: category || 'performance',
    });
  }

  // A/B Testing support
  async logABTestExposure(testName: string, variant: string): Promise<void> {
    await this.logEvent('ab_test_exposure', {
      test_name: testName,
      variant,
    });
  }

  // User engagement
  async logSessionStart(): Promise<void> {
    await this.logEvent('session_start', {
      timestamp: Date.now(),
    });
  }

  async logSessionEnd(duration: number): Promise<void> {
    await this.logEvent('session_end', {
      session_duration: duration,
    });
  }

  async logFeatureUsage(featureName: string, action: string): Promise<void> {
    await this.logEvent('feature_usage', {
      feature_name: featureName,
      action,
    });
  }

  // Conversion funnel tracking
  async logFunnelStep(funnel: string, step: string, stepNumber: number): Promise<void> {
    await this.logEvent('funnel_step', {
      funnel_name: funnel,
      step_name: step,
      step_number: stepNumber,
    });
  }

  // Set custom keys for crash reports
  async setCrashKey(key: string, value: string): Promise<void> {
    if (!this.isInitialized) return;

    try {
      await crashlytics().setAttribute(key, value);
    } catch (error) {
      console.error('Failed to set crash key:', error);
    }
  }

  // Performance helper for measuring function execution time
  async measurePerformance<T>(
    name: string,
    fn: () => Promise<T>,
    category?: string
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      await this.logTiming(name, duration, category);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      await this.logTiming(`${name}_error`, duration, category);
      await this.recordError(error as Error, { operation: name });
      throw error;
    }
  }

  // Debug information
  getAnalyticsInfo(): {
    isInitialized: boolean;
    userId: string | null;
    platform: string;
  } {
    return {
      isInitialized: this.isInitialized,
      userId: this.userId,
      platform: Platform.OS,
    };
  }

  // Reset user data (for logout)
  async resetUserData(): Promise<void> {
    if (!this.isInitialized) return;

    try {
      await analytics().setUserId(null);
      await crashlytics().setUserId('');
      this.userId = null;
    } catch (error) {
      console.error('Failed to reset user data:', error);
    }
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();