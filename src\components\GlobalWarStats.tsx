import React, { useState, useEffect } from 'react';
import { View, Text, ActivityIndicator, TouchableOpacity } from 'react-native';
import { Sword, Target, TrendingUp, Users, MapPin, Flag } from 'lucide-react-native';
import { warService } from '../services/warService';
import { showErrorToast } from '../utils/toastUtils';

interface GlobalWarStatsData {
  totalWars: number;
  activeWars: number;
  endedWars: number;
  groundWars: number;
  seaWars: number;
  revolutionWars: number;
  conquestWars: number;
  resourceWars: number;
  averageDuration: number;
  mostActiveRegions: Array<{
    regionId: string;
    regionName: string;
    warCount: number;
  }>;
  mostActiveStates: Array<{
    stateId?: string;
    stateName?: string;
    warCount: number;
  }>;
}

interface Props {
  onRegionPress?: (regionId: string) => void;
  onStatePress?: (stateId: string) => void;
}

export const GlobalWarStats: React.FC<Props> = ({ onRegionPress, onStatePress }) => {
  const [stats, setStats] = useState<GlobalWarStatsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGlobalStats = async () => {
      try {
        setLoading(true);
        const data = await warService.getGlobalWarStats();
        setStats(data);
      } catch (error) {
        console.error('Failed to fetch global war statistics:', error);
        showErrorToast('Failed to load war statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchGlobalStats();
  }, []);

  if (loading) {
    return (
      <View className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <View className="flex-row items-center justify-center">
          <ActivityIndicator size="large" color="#3f87ff" />
          <Text className="text-gray-300 ml-3">Loading war statistics...</Text>
        </View>
      </View>
    );
  }

  if (!stats) {
    return (
      <View className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <Text className="text-gray-400 text-center">No war statistics available</Text>
      </View>
    );
  }

  return (
    <View className="bg-gray-800 rounded-xl p-6 border border-gray-700">
      <Text className="text-xl font-bold text-white mb-4">Global War Statistics</Text>
      
      {/* Main Stats Grid */}
      <View className="flex-row flex-wrap justify-between mb-6">
        <View className="bg-gray-700 rounded-lg p-4 w-[48%] mb-3">
          <View className="flex-row items-center mb-2">
            <Sword width={20} height={20} color="#ef4444" />
            <Text className="text-gray-300 ml-2 text-sm">Total Wars</Text>
          </View>
          <Text className="text-2xl font-bold text-white">{stats.totalWars}</Text>
        </View>

        <View className="bg-gray-700 rounded-lg p-4 w-[48%] mb-3">
          <View className="flex-row items-center mb-2">
            <TrendingUp width={20} height={20} color="#10b981" />
            <Text className="text-gray-300 ml-2 text-sm">Active Wars</Text>
          </View>
          <Text className="text-2xl font-bold text-white">{stats.activeWars}</Text>
        </View>

        <View className="bg-gray-700 rounded-lg p-4 w-[48%] mb-3">
          <View className="flex-row items-center mb-2">
            <Target width={20} height={20} color="#f59e0b" />
            <Text className="text-gray-300 ml-2 text-sm">Conquest Wars</Text>
          </View>
          <Text className="text-2xl font-bold text-white">{stats.conquestWars || 0}</Text>
        </View>

        <View className="bg-gray-700 rounded-lg p-4 w-[48%] mb-3">
          <View className="flex-row items-center mb-2">
            <Users width={20} height={20} color="#8b5cf6" />
            <Text className="text-gray-300 ml-2 text-sm">Revolutions</Text>
          </View>
          <Text className="text-2xl font-bold text-white">{stats.revolutionWars || 0}</Text>
        </View>
      </View>

      {/* War Types Breakdown */}
      <View className="mb-6">
        <Text className="text-lg font-semibold text-white mb-3">War Types</Text>
        <View className="space-y-2">
          <View className="flex-row justify-between items-center">
            <Text className="text-gray-300">Ground Wars</Text>
            <Text className="text-white font-semibold">{stats.groundWars || 0}</Text>
          </View>
          <View className="flex-row justify-between items-center">
            <Text className="text-gray-300">Sea Wars</Text>
            <Text className="text-white font-semibold">{stats.seaWars || 0}</Text>
          </View>
          <View className="flex-row justify-between items-center">
            <Text className="text-gray-300">Revolutions</Text>
            <Text className="text-white font-semibold">{stats.revolutionWars || 0}</Text>
          </View>
        </View>
      </View>

      {/* Most Active Regions */}
      {stats.mostActiveRegions.length > 0 && (
        <View className="mb-4">
          <Text className="text-lg font-semibold text-white mb-3">Most Active Regions</Text>
          {stats.mostActiveRegions.slice(0, 3).map((region, index) => (
            <TouchableOpacity
              key={region.regionId}
              className="flex-row justify-between items-center py-2 border-b border-gray-700"
              onPress={() => onRegionPress?.(region.regionId)}
            >
              <View className="flex-row items-center">
                <MapPin width={16} height={16} color="#3f87ff" />
                <Text className="text-gray-300 ml-2">{region.regionName}</Text>
              </View>
              <Text className="text-white font-semibold">{region.warCount} wars</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Most Active States */}
      {stats.mostActiveStates.length > 0 && (
        <View>
          <Text className="text-lg font-semibold text-white mb-3">Most Active States</Text>
          {stats.mostActiveStates.slice(0, 3).map((state, index) => (
            <TouchableOpacity
              key={state.stateId || index}
              className="flex-row justify-between items-center py-2 border-b border-gray-700"
              onPress={() => state.stateId && onStatePress?.(state.stateId)}
            >
              <View className="flex-row items-center">
                <Flag width={16} height={16} color="#10b981" />
                <Text className="text-gray-300 ml-2">{state.stateName || 'Unknown State'}</Text>
              </View>
              <Text className="text-white font-semibold">{state.warCount} wars</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
};
