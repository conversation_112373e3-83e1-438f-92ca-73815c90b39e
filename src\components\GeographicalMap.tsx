import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';
import { Asset } from 'expo-asset';
import { showErrorToast } from '../utils/toastUtils';
import { Map } from './Map';

interface Props {
  onRegionSelect?: (region: any) => void;
  onStateSelect?: (state: any) => void;
  onRegionCheck?: (regionId: string) => void;
  mapMode?: 'political' | 'wars' | 'targets';
  wars?: any[];
  availableTargets?: any[];
  showControls?: boolean;
  interactive?: boolean;
  height?: number;
  states?: any[];
}

interface GeoJSONFeature {
  type: 'Feature';
  properties: {
    NAME: string;
    ISO_A2: string;
    ISO_A3: string;
    [key: string]: any;
  };
  geometry: {
    type: 'Polygon' | 'MultiPolygon';
    coordinates: number[][][] | number[][][][];
  };
}

interface GeoJSONData {
  type: 'FeatureCollection';
  features: GeoJSONFeature[];
}

export const GeographicalMap: React.FC<Props> = ({
  onRegionSelect,
  onStateSelect,
  onRegionCheck,
  mapMode = 'political',
  wars = [],
  availableTargets = [],
  showControls = true,
  interactive = true,
  height = 300,
  states = [],
}) => {
  const [geoJsonData, setGeoJsonData] = useState<GeoJSONData | null>(null);
  const [loading, setLoading] = useState(true);
  const [mapReady, setMapReady] = useState(false);

  // Map region state
  const [mapRegion, setMapRegion] = useState({
    latitude: 20,
    longitude: 0,
    latitudeDelta: 100,
    longitudeDelta: 100,
  });

  useEffect(() => {
    loadGeoJsonData();
  }, []);

  const loadGeoJsonData = async () => {
    try {
      setLoading(true);

      // Try to load from bundled assets first (web-compatible approach)
      try {
        // For web, try to fetch from public directory
        const response = await fetch('/world.geojson');
        if (response.ok) {
          const data: GeoJSONData = await response.json();
          setGeoJsonData(data);
          return;
        }
      } catch (webError) {
        console.log('Web asset loading failed, trying Expo asset approach');
      }

      // For native platforms, try Expo Asset approach
      try {
        const asset = Asset.fromModule(require('../../assets/world.geojson'));
        await asset.downloadAsync();

        const response = await fetch(asset.localUri || asset.uri);
        const data: GeoJSONData = await response.json();
        setGeoJsonData(data);
        return;
      } catch (assetError) {
        console.log('Asset loading failed, trying external fallback');
      }

      // Fallback to external source
      const response = await fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson');
      const data: GeoJSONData = await response.json();
      setGeoJsonData(data);

    } catch (error) {
      console.error('Error loading GeoJSON data:', error);
      showErrorToast('Failed to load map data');
    } finally {
      setLoading(false);
    }
  };

  const convertGeoJSONToPolygons = () => {
    if (!geoJsonData || !mapReady) return [];

    return geoJsonData.features.map((feature, index) => {
      const { geometry, properties } = feature;
      
      // Find if this country has a state
      const countryState = states.find(state => 
        state.regions.some((region: any) => 
          region.countryCode === properties.ISO_A2
        )
      );

      // Determine color based on state ownership and map mode
      let fillColor = '#2d3748'; // Default unclaimed color
      let strokeColor = '#4a5568';
      let strokeWidth = 1;

      if (countryState) {
        // Use state color if claimed
        fillColor = generateStateColor(countryState.id);
      }

      // Apply war visualization
      if (mapMode === 'wars' && isCountryInWar(properties.ISO_A2)) {
        strokeColor = '#ef4444';
        strokeWidth = 3;
      }

      // Apply target visualization
      if (mapMode === 'targets' && isCountryAvailableTarget(properties.ISO_A2)) {
        strokeColor = '#f59e0b';
        strokeWidth = 3;
      }

      // Convert coordinates to map coordinates
      const coordinates = convertCoordinatesToMapFormat(geometry);

      return (
        <Polygon
          key={`country-${index}`}
          coordinates={coordinates}
          fillColor={fillColor + '80'} // Add transparency
          strokeColor={strokeColor}
          strokeWidth={strokeWidth}
          tappable={interactive}
          onPress={() => handleCountryPress(feature, countryState)}
        />
      );
    });
  };

  const convertCoordinatesToMapFormat = (geometry: any) => {
    // Convert GeoJSON coordinates to React Native Maps format
    if (geometry.type === 'Polygon') {
      return geometry.coordinates[0].map((coord: number[]) => ({
        latitude: coord[1],
        longitude: coord[0],
      }));
    } else if (geometry.type === 'MultiPolygon') {
      // For MultiPolygon, return the largest polygon
      const polygons = geometry.coordinates.map((poly: number[][][]) =>
        poly[0].map((coord: number[]) => ({
          latitude: coord[1],
          longitude: coord[0],
        }))
      );
      
      // Return the polygon with the most points (likely the main landmass)
      return polygons.reduce((prev: any[], current: any[]) => 
        current.length > prev.length ? current : prev
      );
    }
    
    return [];
  };

  const generateStateColor = (stateId: string): string => {
    // Simple color generation based on state ID
    const colors = [
      '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
      '#06b6d4', '#ec4899', '#84cc16', '#f97316', '#6366f1'
    ];
    
    const index = parseInt(stateId) % colors.length;
    return colors[index];
  };

  const isCountryInWar = (countryCode: string): boolean => {
    return wars.some(war => 
      war.attackerRegion?.countryCode === countryCode || 
      war.defenderRegion?.countryCode === countryCode
    );
  };

  const isCountryAvailableTarget = (countryCode: string): boolean => {
    return availableTargets.some(target => target.countryCode === countryCode);
  };

  const handleCountryPress = (feature: GeoJSONFeature, countryState: any) => {
    const countryName = feature.properties.NAME;
    const countryCode = feature.properties.ISO_A2;

    if (mapMode === 'targets' && countryState && onRegionCheck) {
      // In target mode, check for available targets
      const region = countryState.regions.find((r: any) => r.countryCode === countryCode);
      if (region) {
        onRegionCheck(region.id);
      }
      return;
    }

    if (countryState && onStateSelect) {
      onStateSelect(countryState);
    } else if (onRegionSelect) {
      // For unclaimed territories
      onRegionSelect({
        id: countryCode,
        name: countryName,
        countryCode: countryCode,
      });
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, { height }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3f87ff" />
          <Text style={styles.loadingText}>Loading world map...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { height }]}>
      <Map
        onRegionSelect={onRegionSelect}
        onStateSelect={onStateSelect}
        onRegionCheck={onRegionCheck}
        mapMode={mapMode}
        wars={wars}
        availableTargets={availableTargets}
        showControls={showControls}
        interactive={interactive}
        height={height}
        states={states}
        geoJsonData={geoJsonData}
        loading={loading}
        mapReady={mapReady}
        mapRegion={mapRegion}
        onMapReady={() => setMapReady(true)}
        onRegionChangeComplete={setMapRegion}
        convertGeoJSONToPolygons={convertGeoJSONToPolygons}
      />

      {/* Map Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#3b82f6' }]} />
          <Text style={styles.legendText}>Claimed Territory</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#2d3748' }]} />
          <Text style={styles.legendText}>Unclaimed Territory</Text>
        </View>
        {mapMode === 'wars' && (
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: '#ef4444' }]} />
            <Text style={styles.legendText}>At War</Text>
          </View>
        )}
        {mapMode === 'targets' && (
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: '#f59e0b' }]} />
            <Text style={styles.legendText}>Available Target</Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#111827',
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#3f87ff',
    fontSize: 16,
    marginTop: 10,
  },
  legend: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    backgroundColor: '#1f2937',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#374151',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    color: '#ffffff',
    fontSize: 12,
  },
  warMarker: {
    backgroundColor: '#ef4444',
    borderRadius: 12,
    padding: 4,
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  warMarkerText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default GeographicalMap;