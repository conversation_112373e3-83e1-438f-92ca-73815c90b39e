import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  ActivityIndicator,
  Animated,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { MessageCircle, Send, X, Users, Wifi, WifiOff } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { chatService } from '../services/chatService';
import Toast from 'react-native-toast-message';

interface Props {
  isVisible: boolean;
  onToggle: () => void;
}

interface ChatMessage {
  id: string;
  content: string;
  sender: {
    id: number;
    username: string;
  };
  createdAt: string;
  isOwn?: boolean;
}

interface TypingUser {
  id: number;
  username: string;
}

const TypingIndicator: React.FC<{ users: TypingUser[] }> = ({ users }) => {
  const animatedValues = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;

  useEffect(() => {
    if (users.length > 0) {
      const animations = animatedValues.map((value, index) =>
        Animated.loop(
          Animated.sequence([
            Animated.delay(index * 200),
            Animated.timing(value, {
              toValue: 1,
              duration: 600,
              useNativeDriver: true,
            }),
            Animated.timing(value, {
              toValue: 0,
              duration: 600,
              useNativeDriver: true,
            }),
          ])
        )
      );
      
      Animated.parallel(animations).start();
    }
  }, [users.length, animatedValues]);

  if (users.length === 0) return null;

  const displayText = users.length === 1 
    ? `${users[0].username} is typing...`
    : `${users.length} users are typing...`;

  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: '#1F2937',
    }}>
      <View style={{ flexDirection: 'row', marginRight: 8 }}>
        {animatedValues.map((value, index) => (
          <Animated.View
            key={index}
            style={{
              width: 6,
              height: 6,
              borderRadius: 3,
              backgroundColor: '#3f87ff',
              marginHorizontal: 2,
              opacity: value,
              transform: [{
                scale: value.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1.2],
                }),
              }],
            }}
          />
        ))}
      </View>
      <Text style={{ color: '#9CA3AF', fontSize: 12, fontStyle: 'italic' }}>
        {displayText}
      </Text>
    </View>
  );
};

const ConnectionStatus: React.FC<{ connected: boolean }> = ({ connected }) => (
  <View style={{
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 4,
    backgroundColor: connected ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)',
    borderRadius: 4,
  }}>
    {connected ? (
      <Wifi width={12} height={12} color="#10B981" />
    ) : (
      <WifiOff width={12} height={12} color="#EF4444" />
    )}
    <Text style={{
      color: connected ? '#10B981' : '#EF4444',
      fontSize: 10,
      marginLeft: 4,
      fontWeight: '500',
    }}>
      {connected ? 'Connected' : 'Disconnected'}
    </Text>
  </View>
);

export const EnhancedChatWidget: React.FC<Props> = ({ isVisible, onToggle }) => {
  const { user } = useAuthStore();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [connected, setConnected] = useState(true);
  const [onlineCount, setOnlineCount] = useState(0);
  
  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (isVisible) {
      loadMessages();
      // Simulate connection status check
      checkConnection();
    } else {
      loadUnreadCount();
    }
  }, [isVisible]);

  useEffect(() => {
    loadUnreadCount();
    
    // Simulate online users count
    setOnlineCount(Math.floor(Math.random() * 50) + 10);
  }, []);

  const checkConnection = () => {
    // Simulate connection check
    setConnected(Math.random() > 0.1); // 90% chance of being connected
  };

  const loadMessages = async () => {
    setLoading(true);
    try {
      const data = await chatService.getGeneralChatMessages({ limit: 50 });
      if (Array.isArray(data)) {
        const processedMessages = data.map(msg => ({
          ...msg,
          isOwn: msg.sender.id === user?.id,
        })).reverse();
        setMessages(processedMessages);
        setUnreadCount(0);
        await chatService.markGeneralChatAsRead();
        
        // Scroll to bottom
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      setConnected(false);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load chat messages',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    try {
      const count = await chatService.getGeneralChatUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    setSending(true);
    try {
      const message = await chatService.sendGeneralChatMessage(newMessage.trim());
      const processedMessage = {
        ...message,
        isOwn: true,
      };
      setMessages(prev => [...prev, processedMessage]);
      setNewMessage('');
      setUnreadCount(0);
      
      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error: any) {
      setConnected(false);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to send message',
      });
    } finally {
      setSending(false);
    }
  };

  const handleTyping = (text: string) => {
    setNewMessage(text);
    
    // Simulate typing indicator
    if (text.length > 0) {
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Set a new timeout to stop typing indicator
      typingTimeoutRef.current = setTimeout(() => {
        // In a real implementation, this would send a "stop typing" event
      }, 1000);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'now';
    if (minutes < 60) return `${minutes}m`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h`;
    
    const days = Math.floor(hours / 24);
    return `${days}d`;
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <View style={{
      marginVertical: 4,
      marginHorizontal: 16,
      alignItems: item.isOwn ? 'flex-end' : 'flex-start',
    }}>
      <View style={{
        backgroundColor: item.isOwn ? '#3f87ff' : '#374151',
        borderRadius: 12,
        paddingHorizontal: 12,
        paddingVertical: 8,
        maxWidth: '80%',
      }}>
        {!item.isOwn && (
          <Text style={{
            color: '#9CA3AF',
            fontSize: 12,
            fontWeight: '500',
            marginBottom: 2,
          }}>
            {item.sender.username}
          </Text>
        )}
        <Text style={{
          color: '#ffffff',
          fontSize: 14,
          lineHeight: 20,
        }}>
          {item.content}
        </Text>
        <Text style={{
          color: item.isOwn ? 'rgba(255, 255, 255, 0.7)' : '#9CA3AF',
          fontSize: 10,
          marginTop: 4,
          textAlign: 'right',
        }}>
          {formatTime(item.createdAt)}
        </Text>
      </View>
    </View>
  );

  if (!isVisible) {
    return (
      <TouchableOpacity
        onPress={onToggle}
        style={{
          position: 'absolute',
          bottom: 80,
          right: 16,
          backgroundColor: '#3f87ff',
          borderRadius: 28,
          width: 56,
          height: 56,
          justifyContent: 'center',
          alignItems: 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 8,
        }}
      >
        <MessageCircle width={24} height={24} color="#ffffff" />
        {unreadCount > 0 && (
          <View style={{
            position: 'absolute',
            top: -4,
            right: -4,
            backgroundColor: '#EF4444',
            borderRadius: 12,
            minWidth: 24,
            height: 24,
            justifyContent: 'center',
            alignItems: 'center',
            borderWidth: 2,
            borderColor: '#111827',
          }}>
            <Text style={{
              color: '#ffffff',
              fontSize: 12,
              fontWeight: 'bold',
            }}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{
        position: 'absolute',
        bottom: 80,
        right: 16,
        width: 320,
        height: 400,
        backgroundColor: '#111827',
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        elevation: 12,
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#374151',
        backgroundColor: '#1F2937',
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
          <MessageCircle width={20} height={20} color="#3f87ff" />
          <Text style={{
            color: '#ffffff',
            fontSize: 16,
            fontWeight: '600',
            marginLeft: 8,
            flex: 1,
          }}>
            General Chat
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 12 }}>
            <Users width={14} height={14} color="#9CA3AF" />
            <Text style={{
              color: '#9CA3AF',
              fontSize: 12,
              marginLeft: 4,
            }}>
              {onlineCount}
            </Text>
          </View>
          <ConnectionStatus connected={connected} />
        </View>
        <TouchableOpacity
          onPress={onToggle}
          style={{
            padding: 4,
            borderRadius: 4,
            marginLeft: 8,
          }}
        >
          <X width={20} height={20} color="#9CA3AF" />
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <View style={{ flex: 1 }}>
        {loading ? (
          <View style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <ActivityIndicator size="large" color="#3f87ff" />
            <Text style={{
              color: '#9CA3AF',
              fontSize: 14,
              marginTop: 8,
            }}>
              Loading messages...
            </Text>
          </View>
        ) : (
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            style={{ flex: 1 }}
            onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
          />
        )}
      </View>

      {/* Typing Indicator */}
      <TypingIndicator users={typingUsers} />

      {/* Input */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderTopWidth: 1,
        borderTopColor: '#374151',
        backgroundColor: '#1F2937',
      }}>
        <TextInput
          value={newMessage}
          onChangeText={handleTyping}
          placeholder="Type a message..."
          placeholderTextColor="#9CA3AF"
          style={{
            flex: 1,
            backgroundColor: '#374151',
            borderRadius: 20,
            paddingHorizontal: 16,
            paddingVertical: 8,
            color: '#ffffff',
            maxHeight: 100,
          }}
          multiline
          editable={connected}
        />
        <TouchableOpacity
          onPress={sendMessage}
          disabled={sending || !newMessage.trim() || !connected}
          style={{
            backgroundColor: sending || !newMessage.trim() || !connected ? '#374151' : '#3f87ff',
            borderRadius: 20,
            padding: 8,
            marginLeft: 8,
          }}
        >
          {sending ? (
            <ActivityIndicator size="small" color="#ffffff" />
          ) : (
            <Send width={20} height={20} color="#ffffff" />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};