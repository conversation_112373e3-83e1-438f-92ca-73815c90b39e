import { io, Socket } from 'socket.io-client';
import * as SecureStore from 'expo-secure-store';
import Constants from 'expo-constants';
import { Message } from '../types/chat';

interface ChatWebSocketConfig {
  MAX_RECONNECT_ATTEMPTS: number;
  RECONNECT_DELAY_BASE: number;
  RECONNECT_DELAY_MAX: number;
  PING_INTERVAL: number;
  HEARTBEAT_TIMEOUT: number;
  TYPING_TIMEOUT: number;
  TYPING_DEBOUNCE_DELAY: number;
  MESSAGE_RATE_LIMIT: number;
  TYPING_RATE_LIMIT: number;
  DEBUG_ENABLED: boolean;
}

const CHAT_CONFIG: ChatWebSocketConfig = {
  MAX_RECONNECT_ATTEMPTS: 3,
  RECONNECT_DELAY_BASE: 2000,
  RECONNECT_DELAY_MAX: 10000,
  PING_INTERVAL: 30000,
  HEARTBEAT_TIMEOUT: 5000,
  TYPING_TIMEOUT: 3000,
  TYPING_DEBOUNCE_DELAY: 300,
  MESSAGE_RATE_LIMIT: 10, // messages per minute
  TYPING_RATE_LIMIT: 60, // typing events per minute
  DEBUG_ENABLED: __DEV__,
};

interface TypingUser {
  userId: number;
  username: string;
  timeout: NodeJS.Timeout;
}

interface MessageRate {
  count: number;
  resetTime: number;
}

interface ChatEventCallbacks {
  onMessage?: (message: Message) => void;
  onMessagesRead?: (data: { chatId: string; readBy: number; readAt: string }) => void;
  onTyping?: (data: { chatId: string; userId: number; username: string; isTyping: boolean }) => void;
  onConnectionChange?: (connected: boolean) => void;
  onError?: (error: any) => void;
}

class ChatWebSocketService {
  private socket: Socket | null = null;
  private isConnecting = false;
  private isConnected = false;
  private reconnectAttempts = 0;
  private callbacks: ChatEventCallbacks = {};
  private typingUsers = new Map<string, Map<number, TypingUser>>();
  private typingDebounceTimeouts = new Map<string, NodeJS.Timeout>();
  private messageRates = new Map<string, MessageRate>();
  private typingRates = new Map<string, MessageRate>();
  private pingInterval: NodeJS.Timeout | null = null;
  private lastPongTime = 0;

  private log(message: string, ...args: any[]) {
    if (CHAT_CONFIG.DEBUG_ENABLED) {
      console.log(`[ChatWebSocket] ${message}`, ...args);
    }
  }

  private error(message: string, ...args: any[]) {
    console.error(`[ChatWebSocket] ${message}`, ...args);
    this.callbacks.onError?.(new Error(message));
  }

  public setCallbacks(callbacks: ChatEventCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  private async getAuthToken(): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync('access_token');
    } catch (error) {
      this.error('Failed to get auth token:', error);
      return null;
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      this.log('Connected to chat server');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.callbacks.onConnectionChange?.(true);
      this.startHeartbeat();
    });

    this.socket.on('disconnect', (reason) => {
      this.log('Disconnected from chat server:', reason);
      this.isConnected = false;
      this.callbacks.onConnectionChange?.(false);
      this.stopHeartbeat();
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect
        return;
      }
    });

    this.socket.on('connect_error', (error) => {
      this.error('Connection error:', error);
      this.isConnected = false;
      this.callbacks.onConnectionChange?.(false);
    });

    this.socket.on('new_message', (message: Message) => {
      this.log('New message received:', message);
      this.callbacks.onMessage?.(message);
    });

    this.socket.on('messages_read', (data: { chatId: string; readBy: number; readAt: string }) => {
      this.log('Messages read:', data);
      this.callbacks.onMessagesRead?.(data);
    });

    this.socket.on('user_typing', (data: { chatId: string; userId: number; username: string; isTyping: boolean }) => {
      this.handleTypingEvent(data);
    });

    this.socket.on('pong', () => {
      this.lastPongTime = Date.now();
      this.log('Pong received');
    });

    this.socket.on('error', (error) => {
      this.error('Socket error:', error);
    });
  }

  private handleTypingEvent(data: { chatId: string; userId: number; username: string; isTyping: boolean }) {
    const { chatId, userId, username, isTyping } = data;
    
    if (!this.typingUsers.has(chatId)) {
      this.typingUsers.set(chatId, new Map());
    }
    
    const chatTyping = this.typingUsers.get(chatId)!;
    
    if (isTyping) {
      // Clear existing timeout
      const existingUser = chatTyping.get(userId);
      if (existingUser?.timeout) {
        clearTimeout(existingUser.timeout);
      }
      
      // Set new typing timeout
      const timeout = setTimeout(() => {
        chatTyping.delete(userId);
        this.callbacks.onTyping?.({ chatId, userId, username, isTyping: false });
      }, CHAT_CONFIG.TYPING_TIMEOUT);
      
      chatTyping.set(userId, { userId, username, timeout });
      this.callbacks.onTyping?.({ chatId, userId, username, isTyping: true });
    } else {
      const existingUser = chatTyping.get(userId);
      if (existingUser?.timeout) {
        clearTimeout(existingUser.timeout);
      }
      chatTyping.delete(userId);
      this.callbacks.onTyping?.({ chatId, userId, username, isTyping: false });
    }
  }

  private startHeartbeat() {
    this.stopHeartbeat();
    this.pingInterval = setInterval(() => {
      if (this.socket?.connected) {
        this.socket.emit('ping');
        
        // Check if we missed a pong
        setTimeout(() => {
          const timeSinceLastPong = Date.now() - this.lastPongTime;
          if (timeSinceLastPong > CHAT_CONFIG.HEARTBEAT_TIMEOUT && this.socket?.connected) {
            this.log('Heartbeat timeout, disconnecting');
            this.socket.disconnect();
          }
        }, CHAT_CONFIG.HEARTBEAT_TIMEOUT);
      }
    }, CHAT_CONFIG.PING_INTERVAL);
  }

  private stopHeartbeat() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private checkRateLimit(rateLimitMap: Map<string, MessageRate>, key: string, limit: number): boolean {
    const now = Date.now();
    const minute = 60 * 1000;
    
    const rate = rateLimitMap.get(key) || { count: 0, resetTime: now + minute };
    
    if (now > rate.resetTime) {
      rate.count = 0;
      rate.resetTime = now + minute;
    }
    
    if (rate.count >= limit) {
      return false;
    }
    
    rate.count++;
    rateLimitMap.set(key, rate);
    return true;
  }

  public async connect(): Promise<boolean> {
    if (this.isConnecting || this.isConnected) {
      return this.isConnected;
    }

    this.isConnecting = true;

    try {
      const token = await this.getAuthToken();
      if (!token) {
        throw new Error('No auth token available');
      }

      const baseUrl = Constants.expoConfig?.extra?.apiBaseUrl || 'http://localhost:3000';
      
      this.socket = io(`${baseUrl}/chat`, {
        auth: { token },
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionAttempts: CHAT_CONFIG.MAX_RECONNECT_ATTEMPTS,
        reconnectionDelay: CHAT_CONFIG.RECONNECT_DELAY_BASE,
        reconnectionDelayMax: CHAT_CONFIG.RECONNECT_DELAY_MAX,
        timeout: 20000,
      });

      this.setupEventListeners();
      
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          this.isConnecting = false;
          resolve(false);
        }, 10000);

        this.socket?.on('connect', () => {
          clearTimeout(timeout);
          this.isConnecting = false;
          resolve(true);
        });

        this.socket?.on('connect_error', () => {
          clearTimeout(timeout);
          this.isConnecting = false;
          resolve(false);
        });
      });
    } catch (error) {
      this.error('Failed to connect:', error);
      this.isConnecting = false;
      return false;
    }
  }

  public disconnect() {
    this.log('Disconnecting from chat server');
    this.stopHeartbeat();
    
    // Clear all typing timeouts
    this.typingUsers.forEach((chatTyping) => {
      chatTyping.forEach((user) => {
        if (user.timeout) {
          clearTimeout(user.timeout);
        }
      });
    });
    this.typingUsers.clear();
    
    // Clear debounce timeouts
    this.typingDebounceTimeouts.forEach((timeout) => {
      clearTimeout(timeout);
    });
    this.typingDebounceTimeouts.clear();

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
    this.callbacks.onConnectionChange?.(false);
  }

  public sendMessage(chatId: string, content: string): boolean {
    if (!this.socket?.connected) {
      this.error('Cannot send message: not connected');
      return false;
    }

    if (!this.checkRateLimit(this.messageRates, chatId, CHAT_CONFIG.MESSAGE_RATE_LIMIT)) {
      this.error('Message rate limit exceeded');
      return false;
    }

    this.socket.emit('send_message', { chatId, content });
    return true;
  }

  public markAsRead(chatId: string): boolean {
    if (!this.socket?.connected) {
      this.error('Cannot mark as read: not connected');
      return false;
    }

    this.socket.emit('mark_read', { chatId });
    return true;
  }

  public sendTypingStart(chatId: string): boolean {
    if (!this.socket?.connected) {
      return false;
    }

    if (!this.checkRateLimit(this.typingRates, chatId, CHAT_CONFIG.TYPING_RATE_LIMIT)) {
      return false;
    }

    // Debounce typing events
    const existingTimeout = this.typingDebounceTimeouts.get(chatId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    this.socket.emit('typing_start', { chatId });

    // Auto-stop typing after timeout
    const timeout = setTimeout(() => {
      this.sendTypingStop(chatId);
    }, CHAT_CONFIG.TYPING_TIMEOUT);
    
    this.typingDebounceTimeouts.set(chatId, timeout);
    return true;
  }

  public sendTypingStop(chatId: string): boolean {
    if (!this.socket?.connected) {
      return false;
    }

    const timeout = this.typingDebounceTimeouts.get(chatId);
    if (timeout) {
      clearTimeout(timeout);
      this.typingDebounceTimeouts.delete(chatId);
    }

    this.socket.emit('typing_stop', { chatId });
    return true;
  }

  public getTypingUsers(chatId: string): TypingUser[] {
    const chatTyping = this.typingUsers.get(chatId);
    if (!chatTyping) return [];
    
    return Array.from(chatTyping.values());
  }

  public isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  public getConnectionState() {
    return {
      connected: this.isConnected,
      connecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
    };
  }
}

// Export singleton instance
export const chatWebSocketService = new ChatWebSocketService();