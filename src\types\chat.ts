// Core Types
export interface User {
  id: number;
  username: string;
  level: number;
}

export interface Chat {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  content: string;
  type: 'text' | 'system';
  sender: User;
  readStatuses: ReadStatus[];
  isRead: boolean;
  createdAt: string;
}

export interface ReadStatus {
  userId: number;
  readAt: string;
}

// API Request/Response Types
export interface CreateChatDto {
  type: 'direct' | 'group';
  participantIds: number[];
  name?: string;
  description?: string;
}

export interface SendMessageDto {
  content: string;
  type: 'text' | 'system';
}

export interface PaginatedChatsResponse {
  chats: Chat[];
  hasMore: boolean;
  nextCursor?: string;
}

export interface PaginatedMessagesResponse {
  messages: Message[];
  hasMore: boolean;
  nextCursor?: string;
}

// Chat Store State
export interface ChatState {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  
  // Chats data
  chats: Chat[];
  chatsLoading: boolean;
  chatsError: string | null;
  chatsHasMore: boolean;
  chatsCursor?: string;
  
  // Messages data
  messages: Record<string, Message[]>;
  messagesLoading: Record<string, boolean>;
  messagesError: Record<string, string | null>;
  messagesHasMore: Record<string, boolean>;
  messagesCursor: Record<string, string | undefined>;
  
  // UI state
  activeChatId: string | null;
  typingUsers: Record<string, any[]>;
  
  // Unread counts
  totalUnreadCount: number;
}

// Utility Types
export type ChatType = 'direct' | 'group' | 'general';
export type MessageType = 'text' | 'system';
export type ConnectionStatus = 'connected' | 'connecting' | 'disconnected' | 'error';