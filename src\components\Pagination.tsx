import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  RefreshControl,
} from 'react-native';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react-native';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  maxVisiblePages?: number;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showFirstLast = true,
  maxVisiblePages = 5,
}) => {
  const getVisiblePages = () => {
    const pages: (number | string)[] = [];
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const half = Math.floor(maxVisiblePages / 2);
      let start = Math.max(1, currentPage - half);
      let end = Math.min(totalPages, start + maxVisiblePages - 1);
      
      // Adjust start if we're near the end
      if (end === totalPages) {
        start = Math.max(1, end - maxVisiblePages + 1);
      }
      
      // Add first page and ellipsis if needed
      if (start > 1) {
        pages.push(1);
        if (start > 2) {
          pages.push('...');
        }
      }
      
      // Add visible pages
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      // Add ellipsis and last page if needed
      if (end < totalPages) {
        if (end < totalPages - 1) {
          pages.push('...');
        }
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();

  const renderPageButton = (page: number | string, index: number) => {
    const isEllipsis = page === '...';
    const isActive = page === currentPage;
    
    return (
      <TouchableOpacity
        key={index}
        onPress={() => !isEllipsis && typeof page === 'number' && onPageChange(page)}
        disabled={isEllipsis || isActive}
        style={{
          paddingHorizontal: 12,
          paddingVertical: 8,
          marginHorizontal: 2,
          borderRadius: 6,
          backgroundColor: isActive ? '#3f87ff' : 'transparent',
          borderWidth: 1,
          borderColor: isActive ? '#3f87ff' : '#4B5563',
          minWidth: 40,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {isEllipsis ? (
          <MoreHorizontal width={16} height={16} color="#9CA3AF" />
        ) : (
          <Text style={{
            color: isActive ? '#ffffff' : '#9CA3AF',
            fontSize: 14,
            fontWeight: isActive ? '600' : '400',
          }}>
            {page}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  if (totalPages <= 1) return null;

  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 16,
    }}>
      {/* Previous Button */}
      <TouchableOpacity
        onPress={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        style={{
          paddingHorizontal: 12,
          paddingVertical: 8,
          marginRight: 8,
          borderRadius: 6,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: '#4B5563',
          opacity: currentPage === 1 ? 0.5 : 1,
          flexDirection: 'row',
          alignItems: 'center',
        }}
      >
        <ChevronLeft width={16} height={16} color="#9CA3AF" />
        <Text style={{ color: '#9CA3AF', fontSize: 14, marginLeft: 4 }}>
          Prev
        </Text>
      </TouchableOpacity>

      {/* Page Numbers */}
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        {visiblePages.map((page, index) => renderPageButton(page, index))}
      </View>

      {/* Next Button */}
      <TouchableOpacity
        onPress={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        style={{
          paddingHorizontal: 12,
          paddingVertical: 8,
          marginLeft: 8,
          borderRadius: 6,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: '#4B5563',
          opacity: currentPage === totalPages ? 0.5 : 1,
          flexDirection: 'row',
          alignItems: 'center',
        }}
      >
        <Text style={{ color: '#9CA3AF', fontSize: 14, marginRight: 4 }}>
          Next
        </Text>
        <ChevronRight width={16} height={16} color="#9CA3AF" />
      </TouchableOpacity>
    </View>
  );
};

interface InfiniteScrollListProps<T> {
  data: T[];
  renderItem: ({ item, index }: { item: T; index: number }) => React.ReactElement;
  onEndReached: () => void;
  onRefresh?: () => void;
  loading?: boolean;
  refreshing?: boolean;
  hasMore?: boolean;
  keyExtractor?: (item: T, index: number) => string;
  ListEmptyComponent?: React.ComponentType<any> | React.ReactElement;
  ListHeaderComponent?: React.ComponentType<any> | React.ReactElement;
  ListFooterComponent?: React.ComponentType<any> | React.ReactElement;
  onEndReachedThreshold?: number;
  style?: any;
}

export function InfiniteScrollList<T>({
  data,
  renderItem,
  onEndReached,
  onRefresh,
  loading = false,
  refreshing = false,
  hasMore = true,
  keyExtractor,
  ListEmptyComponent,
  ListHeaderComponent,
  ListFooterComponent,
  onEndReachedThreshold = 0.1,
  style,
}: InfiniteScrollListProps<T>) {
  const renderFooter = () => {
    if (!loading || !hasMore) return ListFooterComponent || null;
    
    return (
      <View style={{
        paddingVertical: 20,
        alignItems: 'center',
      }}>
        <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
          Loading more...
        </Text>
      </View>
    );
  };

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor || ((item: any, index) => index.toString())}
      onEndReached={hasMore ? onEndReached : undefined}
      onEndReachedThreshold={onEndReachedThreshold}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#3f87ff"
            colors={['#3f87ff']}
          />
        ) : undefined
      }
      ListEmptyComponent={ListEmptyComponent}
      ListHeaderComponent={ListHeaderComponent}
      ListFooterComponent={renderFooter}
      showsVerticalScrollIndicator={false}
      style={style}
    />
  );
}

interface PaginatedListProps<T> {
  data: T[];
  renderItem: ({ item, index }: { item: T; index: number }) => React.ReactElement;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onRefresh?: () => void;
  loading?: boolean;
  refreshing?: boolean;
  keyExtractor?: (item: T, index: number) => string;
  ListEmptyComponent?: React.ComponentType<any> | React.ReactElement;
  ListHeaderComponent?: React.ComponentType<any> | React.ReactElement;
  style?: any;
}

export function PaginatedList<T>({
  data,
  renderItem,
  currentPage,
  totalPages,
  onPageChange,
  onRefresh,
  loading = false,
  refreshing = false,
  keyExtractor,
  ListEmptyComponent,
  ListHeaderComponent,
  style,
}: PaginatedListProps<T>) {
  return (
    <View style={[{ flex: 1 }, style]}>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor || ((item: any, index) => index.toString())}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#3f87ff"
              colors={['#3f87ff']}
            />
          ) : undefined
        }
        ListEmptyComponent={ListEmptyComponent}
        ListHeaderComponent={ListHeaderComponent}
        showsVerticalScrollIndicator={false}
        style={{ flex: 1 }}
      />
      
      {!loading && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={onPageChange}
        />
      )}
    </View>
  );
}