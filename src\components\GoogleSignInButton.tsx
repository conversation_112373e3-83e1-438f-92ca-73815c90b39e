import React, { useEffect } from 'react';
import { TouchableOpacity, Text, View, Platform } from 'react-native';
import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import Constants from 'expo-constants';
import { useAuthStore } from '../store/useAuthStore';
import { theme } from '../styles/theme';
import Toast from 'react-native-toast-message';

WebBrowser.maybeCompleteAuthSession();

interface Props {
  onSuccess?: () => void;
  onError?: (error: any) => void;
}

export const GoogleSignInButton: React.FC<Props> = ({ onSuccess, onError }) => {
  const { loginWithGoogle, isLoading } = useAuthStore();

  const discovery = {
    authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
    tokenEndpoint: 'https://www.googleapis.com/oauth2/v4/token',
    revocationEndpoint: 'https://oauth2.googleapis.com/revoke',
  };

  // Use web client ID for both platforms
  const clientId = Constants.expoConfig?.extra?.googleClientIdAndroid || '';

  const [request, response, promptAsync] = AuthSession.useAuthRequest(
    {
      clientId,
      scopes: ['openid', 'profile', 'email'],
      responseType: AuthSession.ResponseType.IdToken,
      redirectUri: AuthSession.makeRedirectUri({
        scheme: 'warfront-nations',
      }),
      usePKCE: false,
    },
    discovery
  );

  useEffect(() => {
    if (response?.type === 'success') {
      handleGoogleResponse(response);
    } else if (response?.type === 'error') {
      console.error('Google Sign-In error:', response.error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to sign in with Google',
      });
      onError?.(response.error);
    }
  }, [response]);

  const handleGoogleResponse = async (response: AuthSession.AuthSessionResult) => {
    try {
      if (response.type === 'success' && response.params.id_token) {
        await loginWithGoogle(response.params.id_token);
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Successfully logged in with Google!',
        });
        onSuccess?.();
      }
    } catch (error: any) {
      console.error('Google Sign-In error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to sign in with Google',
      });
      onError?.(error);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await promptAsync();
    } catch (error: any) {
      console.error('Google Sign-In prompt error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to open Google Sign-In',
      });
      onError?.(error);
    }
  };

  return (
    <TouchableOpacity
      onPress={handleGoogleSignIn}
      disabled={isLoading || !request}
      style={{
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: '#1C1C1E',
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderRadius: theme.borderRadius.medium,
        opacity: isLoading || !request ? 0.5 : 1,
        ...theme.shadows.small,
      }}
    >
      {/* Google Icon */}
      <View style={{
        width: 20,
        height: 20,
        marginRight: 12,
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#4285F4',
          fontFamily: Platform.OS === 'ios' ? 'Arial' : 'sans-serif'
        }}>
          G
        </Text>
      </View>

      <Text style={{
        color: '#ffffff',
        fontSize: 16,
        fontWeight: '500',
      }}>
        {isLoading ? 'Signing in...' : 'Continue with Google'}
      </Text>
    </TouchableOpacity>
  );
};