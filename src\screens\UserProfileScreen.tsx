import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { 
  User, 
  Crown, 
  Dumbbell, 
  Brain, 
  Shield, 
  Mail, 
  DollarSign,
  Flag,
  Users,
  MapPin,
  BarChart3,
  TrendingUp
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { userService } from '../services/userService';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Toast from 'react-native-toast-message';
import { UserProfile } from '../types/user';

interface Props {
  navigation: any;
  route: any;
}


interface WarStats {
  totalParticipations: number;
  totalDamage: number;
  totalKills: number;
  warsWon: number;
  warsLost: number;
  efficiency: number;
}

export const UserProfileScreen: React.FC<Props> = ({ navigation, route }) => {
  const { user: currentUser } = useAuthStore();
  const userId = route?.params?.userId;
  useAuthGuard({ navigation });

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [warStats, setWarStats] = useState<WarStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [transferAmount, setTransferAmount] = useState('100');
  const [transferring, setTransferring] = useState(false);

  const isOwnProfile = currentUser?.id && userId && currentUser.id.toString() === userId.toString();

  useEffect(() => {
    if (userId) {
      fetchUserProfile();
    }
  }, [userId]);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const [profileData, statsData] = await Promise.all([
        userService.getUserById(userId),
        userService.getUserWarAnalytics(userId).catch(() => null)
      ]);
            
      setProfile(profileData);
      setWarStats(statsData);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load user profile'
      });
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchUserProfile();
    setRefreshing(false);
  };

  const handleTransferMoney = async () => {
    const amount = parseFloat(transferAmount);
    if (amount <= 0 || isNaN(amount)) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please enter a valid amount'
      });
      return;
    }

    setTransferring(true);
    try {
      await userService.sendMoney({
        fromUserId: currentUser.id,
        toUserId: userId,
        amount: amount
      });

      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Money sent successfully!'
      });
      setShowTransferModal(false);
      setTransferAmount('100');
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.response?.data?.message || 'Failed to send money'
      });
    } finally {
      setTransferring(false);
    }
  };

  const handleSendMessage = () => {
    // Navigate to chat or messaging screen
    navigation.navigate('ChatList', { createChatWith: userId });
  };

  if (loading) {
    return (
      <View style={{ flex: 1, backgroundColor: '#111827', justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#3f87ff" />
        <Text style={{ color: '#3f87ff', fontSize: 18, marginTop: 16 }}>Loading profile...</Text>
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={{ flex: 1, backgroundColor: '#111827', justifyContent: 'center', alignItems: 'center', padding: 16 }}>
        <Text style={{ color: '#EF4444', fontSize: 20, marginBottom: 16, textAlign: 'center' }}>
          User not found
        </Text>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            backgroundColor: '#3f87ff',
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 8,
          }}
        >
          <Text style={{ color: '#ffffff', fontSize: 16 }}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#111827' }}>
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Own Profile Notice */}
        {isOwnProfile && (
          <View style={{
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderBottomWidth: 1,
            borderBottomColor: 'rgba(59, 130, 246, 0.3)',
            padding: 16,
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text style={{ color: '#ffffff', fontSize: 14 }}>
                This is your public profile that others can see.
              </Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('ProfileScreen')}
                style={{
                  backgroundColor: '#3f87ff',
                  paddingHorizontal: 12,
                  paddingVertical: 6,
                  borderRadius: 8,
                }}
              >
                <Text style={{ color: '#ffffff', fontSize: 12, fontWeight: '600' }}>
                  Edit Profile
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Profile Header */}
        <View style={{ padding: 16, paddingTop: isOwnProfile ? 16 : 40 }}>
          <View style={{
            backgroundColor: 'rgba(31, 41, 55, 0.7)',
            borderRadius: 16,
            padding: 24,
            marginBottom: 20,
          }}>
            {/* Avatar and Basic Info */}
            <View style={{ alignItems: 'center', marginBottom: 24 }}>
              <View style={{ position: 'relative', marginBottom: 16 }}>
                <View style={{
                  width: 100,
                  height: 100,
                  borderRadius: 50,
                  backgroundColor: 'rgba(59, 130, 246, 0.2)',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderWidth: 2,
                  borderColor: 'rgba(59, 130, 246, 0.3)',
                }}>
                  {profile.avatarUrl ? (
                    // Note: You would need to use Image component here for actual avatar
                    <User width={50} height={50} color="#3f87ff" />
                  ) : (
                    <Text style={{ color: '#3f87ff', fontSize: 36, fontWeight: 'bold' }}>
                      {profile.username.charAt(0).toUpperCase()}
                    </Text>
                  )}
                </View>
                
                {profile.isPremium && (
                  <View style={{
                    position: 'absolute',
                    top: -4,
                    right: -4,
                    backgroundColor: '#F59E0B',
                    borderRadius: 16,
                    width: 32,
                    height: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <Crown width={18} height={18} color="#FFF" />
                  </View>
                )}
              </View>

              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ffffff', marginBottom: 8 }}>
                {profile.username}
              </Text>
              
              <Text style={{ color: '#9CA3AF', fontSize: 14, marginBottom: 16 }}>
                Member since {new Date(profile.createdAt).toLocaleDateString()}
              </Text>

              {profile.aboutMe && (
                <Text style={{ color: '#ffffff', fontSize: 16, textAlign: 'center', marginBottom: 20 }}>
                  {profile.aboutMe}
                </Text>
              )}

              {/* Action Buttons - Only show for other users' profiles */}
              {!isOwnProfile && (
                <View style={{ flexDirection: 'row', gap: 12, marginTop: 8 }}>
                  <TouchableOpacity
                    onPress={handleSendMessage}
                    style={{
                      backgroundColor: '#3f87ff',
                      paddingHorizontal: 16,
                      paddingVertical: 10,
                      borderRadius: 8,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}
                  >
                    <Mail width={18} height={18} color="#ffffff" />
                    <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '600', marginLeft: 6 }}>
                      Message
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => setShowTransferModal(true)}
                    style={{
                      backgroundColor: '#10B981',
                      paddingHorizontal: 16,
                      paddingVertical: 10,
                      borderRadius: 8,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}
                  >
                    <DollarSign width={18} height={18} color="#ffffff" />
                    <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '600', marginLeft: 6 }}>
                      Send Money
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            {/* Stats Cards */}
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 12 }}>
              {/* Level */}
              <View style={{
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                borderRadius: 12,
                padding: 16,
                flex: 1,
                minWidth: '45%',
                alignItems: 'center',
              }}>
                <TrendingUp width={24} height={24} color="#3f87ff" />
                <Text style={{ color: '#9CA3AF', fontSize: 12, marginTop: 8 }}>Level</Text>
                <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold' }}>
                  {profile.level}
                </Text>
              </View>

              {/* Strength */}
              <View style={{
                backgroundColor: 'rgba(239, 68, 68, 0.2)',
                borderRadius: 12,
                padding: 16,
                flex: 1,
                minWidth: '45%',
                alignItems: 'center',
              }}>
                <Dumbbell width={24} height={24} color="#EF4444" />
                <Text style={{ color: '#9CA3AF', fontSize: 12, marginTop: 8 }}>Strength</Text>
                <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold' }}>
                  {profile.strength}
                </Text>
              </View>

              {/* Intelligence */}
              <View style={{
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                borderRadius: 12,
                padding: 16,
                flex: 1,
                minWidth: '45%',
                alignItems: 'center',
              }}>
                <Brain width={24} height={24} color="#3f87ff" />
                <Text style={{ color: '#9CA3AF', fontSize: 12, marginTop: 8 }}>Intelligence</Text>
                <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold' }}>
                  {profile.intelligence}
                </Text>
              </View>

              {/* Endurance */}
              <View style={{
                backgroundColor: 'rgba(16, 185, 129, 0.2)',
                borderRadius: 12,
                padding: 16,
                flex: 1,
                minWidth: '45%',
                alignItems: 'center',
              }}>
                <Shield width={24} height={24} color="#10B981" />
                <Text style={{ color: '#9CA3AF', fontSize: 12, marginTop: 8 }}>Endurance</Text>
                <Text style={{ color: '#ffffff', fontSize: 20, fontWeight: 'bold' }}>
                  {profile.endurance}
                </Text>
              </View>
            </View>
          </View>

          {/* Additional Information */}
          <View style={{ gap: 16 }}>
            {/* State Information */}
            {profile.region?.state && (
              <View style={{
                backgroundColor: '#1F2937',
                borderRadius: 12,
                padding: 20,
              }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                  <Flag width={24} height={24} color="#EF4444" />
                  <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginLeft: 12 }}>
                    State
                  </Text>
                </View>

                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                  <View style={{
                    width: 48,
                    height: 48,
                    borderRadius: 12,
                    backgroundColor: 'rgba(239, 68, 68, 0.2)',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <Text style={{ color: '#EF4444', fontSize: 20, fontWeight: 'bold' }}>
                      {profile.region.state.name.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                  <View style={{ marginLeft: 12 }}>
                    <Text style={{ fontSize: 16, fontWeight: '500', color: '#ffffff' }}>
                      {profile.region.state.name}
                    </Text>
                    <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
                      Leader: {profile.region.state.leader?.username || 'Unknown'}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  onPress={() => navigation.navigate('StateBudget', { stateId: profile.region.state.id })}
                  style={{
                    backgroundColor: '#3f87ff',
                    paddingVertical: 12,
                    borderRadius: 8,
                    alignItems: 'center',
                  }}
                >
                  <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
                    View State Details
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Region Information */}
            {profile.region && (
              <View style={{
                backgroundColor: '#1F2937',
                borderRadius: 12,
                padding: 20,
              }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                  <MapPin width={24} height={24} color="#10B981" />
                  <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginLeft: 12 }}>
                    Region
                  </Text>
                </View>

                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                  <View style={{
                    width: 48,
                    height: 48,
                    borderRadius: 12,
                    backgroundColor: 'rgba(16, 185, 129, 0.2)',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <Text style={{ color: '#10B981', fontSize: 20, fontWeight: 'bold' }}>
                      {profile.region.name.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                  <View style={{ marginLeft: 12 }}>
                    <Text style={{ fontSize: 16, fontWeight: '500', color: '#ffffff' }}>
                      {profile.region.name}
                    </Text>
                    <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
                      Population: {profile.region.population?.toLocaleString() || 'N/A'}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  onPress={() => navigation.navigate('RegionDetail', { regionId: profile.region.id })}
                  style={{
                    backgroundColor: '#3f87ff',
                    paddingVertical: 12,
                    borderRadius: 8,
                    alignItems: 'center',
                  }}
                >
                  <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
                    View Region Details
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Party Information */}
            {(profile.leadingParty || profile.memberOfParty) && (
              <View style={{
                backgroundColor: '#1F2937',
                borderRadius: 12,
                padding: 20,
              }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                  <Users width={24} height={24} color="#8B5CF6" />
                  <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginLeft: 12 }}>
                    Party
                  </Text>
                </View>

                {(() => {
                  const party = profile.leadingParty || profile.memberOfParty;
                  const isLeader = !!profile.leadingParty;
                  
                  return (
                    <>
                      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                        <View style={{
                          width: 48,
                          height: 48,
                          borderRadius: 12,
                          backgroundColor: 'rgba(139, 92, 246, 0.2)',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                          <Text style={{ color: '#8B5CF6', fontSize: 20, fontWeight: 'bold' }}>
                            {party.name.charAt(0).toUpperCase()}
                          </Text>
                        </View>
                        <View style={{ marginLeft: 12 }}>
                          <Text style={{ fontSize: 16, fontWeight: '500', color: '#ffffff' }}>
                            {party.name} {isLeader && '(Leader)'}
                          </Text>
                          <Text style={{ color: '#9CA3AF', fontSize: 14 }}>
                            Members: {party.membersCount || 1}
                          </Text>
                        </View>
                      </View>

                      <TouchableOpacity
                        onPress={() => navigation.navigate('PartyDetail', { partyId: party.id })}
                        style={{
                          backgroundColor: '#3f87ff',
                          paddingVertical: 12,
                          borderRadius: 8,
                          alignItems: 'center',
                        }}
                      >
                        <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
                          View Party Details
                        </Text>
                      </TouchableOpacity>
                    </>
                  );
                })()}
              </View>
            )}

            {/* War Statistics */}
            {warStats && (
              <View style={{
                backgroundColor: '#1F2937',
                borderRadius: 12,
                padding: 20,
              }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                  <BarChart3 width={24} height={24} color="#F59E0B" />
                  <Text style={{ fontSize: 18, fontWeight: '600', color: '#ffffff', marginLeft: 12 }}>
                    War Statistics
                  </Text>
                </View>

                <View style={{ gap: 12 }}>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ color: '#9CA3AF' }}>Total Participations:</Text>
                    <Text style={{ color: '#ffffff', fontWeight: '600' }}>{warStats.totalParticipations}</Text>
                  </View>
                  
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ color: '#9CA3AF' }}>Total Damage:</Text>
                    <Text style={{ color: '#EF4444', fontWeight: '600' }}>
                      {warStats.totalDamage.toLocaleString()}
                    </Text>
                  </View>
                  
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ color: '#9CA3AF' }}>Wars Won:</Text>
                    <Text style={{ color: '#10B981', fontWeight: '600' }}>{warStats.warsWon}</Text>
                  </View>
                  
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ color: '#9CA3AF' }}>Wars Lost:</Text>
                    <Text style={{ color: '#EF4444', fontWeight: '600' }}>{warStats.warsLost}</Text>
                  </View>
                  
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ color: '#9CA3AF' }}>Efficiency:</Text>
                    <Text style={{ color: '#F59E0B', fontWeight: '600' }}>
                      {warStats.efficiency.toFixed(1)}%
                    </Text>
                  </View>
                </View>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Transfer Money Modal */}
      <Modal visible={showTransferModal} transparent animationType="slide">
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 16,
        }}>
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 24,
            width: '100%',
            maxWidth: 400,
          }}>
            <Text style={{ fontSize: 20, fontWeight: '600', color: '#ffffff', marginBottom: 20 }}>
              Send Money to {profile?.username}
            </Text>

            <View style={{ marginBottom: 20 }}>
              <Text style={{ color: '#9CA3AF', fontSize: 16, marginBottom: 8 }}>Amount</Text>
              <TextInput
                value={transferAmount}
                onChangeText={setTransferAmount}
                style={{
                  backgroundColor: '#374151',
                  borderWidth: 1,
                  borderColor: '#4B5563',
                  borderRadius: 8,
                  padding: 12,
                  color: '#ffffff',
                  fontSize: 16,
                }}
                placeholder="Enter amount"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
              />
            </View>

            <View style={{ flexDirection: 'row', gap: 12 }}>
              <TouchableOpacity
                onPress={() => setShowTransferModal(false)}
                style={{
                  flex: 1,
                  backgroundColor: '#4B5563',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                }}
              >
                <Text style={{ color: '#ffffff', fontSize: 16 }}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                onPress={handleTransferMoney}
                disabled={transferring}
                style={{
                  flex: 1,
                  backgroundColor: transferring ? '#065F46' : '#10B981',
                  paddingVertical: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                }}
              >
                {transferring && <ActivityIndicator size="small" color="#ffffff" style={{ marginRight: 8 }} />}
                <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
                  {transferring ? 'Sending...' : 'Send Money'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};