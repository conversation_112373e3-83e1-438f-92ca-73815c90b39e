import * as Network from 'expo-network';
import * as SecureStore from 'expo-secure-store';
import { api } from './api';

interface QueuedRequest {
  id: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: any;
  headers?: Record<string, string>;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

interface CachedData {
  data: any;
  timestamp: number;
  expiresAt: number;
}

class OfflineService {
  private isOnline = true;
  private requestQueue: QueuedRequest[] = [];
  private readonly QUEUE_KEY = 'offline_request_queue';
  private readonly MAX_CACHE_SIZE = 100; // Maximum number of cached items
  private readonly DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private networkCheckInterval: NodeJS.Timeout | null = null;
  
  private listeners: {
    onConnectionChange?: (isOnline: boolean) => void;
    onQueueUpdate?: (queueSize: number) => void;
  } = {};

  async initialize(): Promise<void> {
    // Load existing queue from storage
    this.loadQueue();

    // Set up network state monitoring
    this.startNetworkMonitoring();

    // Get initial network state
    await this.checkNetworkState();

    console.log('Offline service initialized');
  }

  private startNetworkMonitoring() {
    // Check network state every 5 seconds
    this.networkCheckInterval = setInterval(async () => {
      await this.checkNetworkState();
    }, 5000);
  }

  private async checkNetworkState() {
    try {
      const wasOnline = this.isOnline;
      const networkState = await Network.getNetworkStateAsync();
      this.isOnline = networkState.isConnected === true && networkState.isInternetReachable === true;

      if (!wasOnline && this.isOnline) {
        // Connection restored, process queued requests
        console.log('Connection restored, processing queued requests');
        this.processQueue();
      }

      this.listeners.onConnectionChange?.(this.isOnline);
    } catch (error) {
      console.error('Error checking network state:', error);
    }
  }

  setListeners(listeners: {
    onConnectionChange?: (isOnline: boolean) => void;
    onQueueUpdate?: (queueSize: number) => void;
  }) {
    this.listeners = listeners;
  }

  isConnected(): boolean {
    return this.isOnline;
  }

  // Cache management
  async setCached(key: string, data: any, ttlMs: number = this.DEFAULT_CACHE_TTL): Promise<void> {
    const cachedItem: CachedData = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttlMs,
    };

    try {
      await SecureStore.setItemAsync(`cache_${key}`, JSON.stringify(cachedItem));
      await this.cleanupOldCache();
    } catch (error) {
      console.error('Failed to cache data:', error);
    }
  }

  getCached<T = any>(key: string): T | null {
    try {
      const cached = this.storage.getString(`cache_${key}`);
      if (!cached) return null;

      const cachedItem: CachedData = JSON.parse(cached);
      
      // Check if expired
      if (Date.now() > cachedItem.expiresAt) {
        this.storage.delete(`cache_${key}`);
        return null;
      }

      return cachedItem.data as T;
    } catch (error) {
      console.error('Failed to get cached data:', error);
      return null;
    }
  }

  clearCache(keyPrefix?: string): void {
    try {
      const keys = this.storage.getAllKeys();
      keys.forEach(key => {
        if (key.startsWith('cache_')) {
          if (!keyPrefix || key.startsWith(`cache_${keyPrefix}`)) {
            this.storage.delete(key);
          }
        }
      });
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }

  private cleanupOldCache(): void {
    try {
      const keys = this.storage.getAllKeys().filter(key => key.startsWith('cache_'));
      
      if (keys.length <= this.MAX_CACHE_SIZE) return;

      // Get all cached items with timestamps
      const cachedItems = keys
        .map(key => {
          const cached = this.storage.getString(key);
          if (cached) {
            const item: CachedData = JSON.parse(cached);
            return { key, timestamp: item.timestamp };
          }
          return null;
        })
        .filter(Boolean) as { key: string; timestamp: number }[];

      // Sort by timestamp (oldest first) and remove excess items
      cachedItems
        .sort((a, b) => a.timestamp - b.timestamp)
        .slice(0, cachedItems.length - this.MAX_CACHE_SIZE + 10) // Keep some buffer
        .forEach(item => this.storage.delete(item.key));
    } catch (error) {
      console.error('Failed to cleanup cache:', error);
    }
  }

  // Request queue management
  queueRequest(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    url: string,
    data?: any,
    headers?: Record<string, string>,
    maxRetries: number = 3
  ): string {
    const requestId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const queuedRequest: QueuedRequest = {
      id: requestId,
      method,
      url,
      data,
      headers,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries,
    };

    this.requestQueue.push(queuedRequest);
    this.saveQueue();
    this.listeners.onQueueUpdate?.(this.requestQueue.length);

    // Try to process immediately if online
    if (this.isOnline) {
      this.processQueue();
    }

    return requestId;
  }

  private async processQueue(): Promise<void> {
    if (!this.isOnline || this.requestQueue.length === 0) {
      return;
    }

    const requestsToProcess = [...this.requestQueue];
    
    for (const request of requestsToProcess) {
      try {
        await this.executeRequest(request);
        
        // Remove successful request from queue
        this.requestQueue = this.requestQueue.filter(r => r.id !== request.id);
        
      } catch (error) {
        console.error(`Failed to execute queued request ${request.id}:`, error);
        
        // Increment retry count
        const queuedRequest = this.requestQueue.find(r => r.id === request.id);
        if (queuedRequest) {
          queuedRequest.retryCount++;
          
          // Remove if max retries exceeded
          if (queuedRequest.retryCount >= queuedRequest.maxRetries) {
            console.log(`Removing request ${request.id} after ${queuedRequest.retryCount} failed attempts`);
            this.requestQueue = this.requestQueue.filter(r => r.id !== request.id);
          }
        }
      }
    }

    this.saveQueue();
    this.listeners.onQueueUpdate?.(this.requestQueue.length);
  }

  private async executeRequest(request: QueuedRequest): Promise<void> {
    const config = {
      method: request.method,
      url: request.url,
      data: request.data,
      headers: request.headers,
    };

    await api.request(config);
  }

  private loadQueue(): void {
    try {
      const queueData = this.storage.getString(this.QUEUE_KEY);
      if (queueData) {
        this.requestQueue = JSON.parse(queueData);
        
        // Remove very old requests (older than 24 hours)
        const cutoff = Date.now() - 24 * 60 * 60 * 1000;
        this.requestQueue = this.requestQueue.filter(r => r.timestamp > cutoff);
        
        this.saveQueue();
      }
    } catch (error) {
      console.error('Failed to load request queue:', error);
      this.requestQueue = [];
    }
  }

  private saveQueue(): void {
    try {
      this.storage.set(this.QUEUE_KEY, JSON.stringify(this.requestQueue));
    } catch (error) {
      console.error('Failed to save request queue:', error);
    }
  }

  getQueueSize(): number {
    return this.requestQueue.length;
  }

  clearQueue(): void {
    this.requestQueue = [];
    this.storage.delete(this.QUEUE_KEY);
    this.listeners.onQueueUpdate?.(0);
  }

  // Enhanced API methods with offline support
  async get<T = any>(url: string, cacheKey?: string, cacheTTL?: number): Promise<T> {
    // Try cache first
    if (cacheKey) {
      const cached = this.getCached<T>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    if (!this.isOnline) {
      throw new Error('No internet connection and no cached data available');
    }

    try {
      const response = await api.get(url);
      
      // Cache successful response
      if (cacheKey) {
        this.setCached(cacheKey, response.data, cacheTTL);
      }
      
      return response.data;
    } catch (error) {
      // Return cached data as fallback even if expired
      if (cacheKey) {
        const cached = this.storage.getString(`cache_${cacheKey}`);
        if (cached) {
          const cachedItem: CachedData = JSON.parse(cached);
          console.log('Using expired cache as fallback');
          return cachedItem.data;
        }
      }
      throw error;
    }
  }

  async post<T = any>(url: string, data?: any, headers?: Record<string, string>): Promise<T> {
    if (!this.isOnline) {
      // Queue the request for later
      this.queueRequest('POST', url, data, headers);
      throw new Error('No internet connection - request queued for later');
    }

    return (await api.post(url, data, { headers })).data;
  }

  async put<T = any>(url: string, data?: any, headers?: Record<string, string>): Promise<T> {
    if (!this.isOnline) {
      this.queueRequest('PUT', url, data, headers);
      throw new Error('No internet connection - request queued for later');
    }

    return (await api.put(url, data, { headers })).data;
  }

  async delete<T = any>(url: string, headers?: Record<string, string>): Promise<T> {
    if (!this.isOnline) {
      this.queueRequest('DELETE', url, undefined, headers);
      throw new Error('No internet connection - request queued for later');
    }

    return (await api.delete(url, { headers })).data;
  }

  // Specific cache methods for different data types
  getCachedChats(): any[] {
    return this.getCached('chats') || [];
  }

  setCachedChats(chats: any[], ttl?: number): void {
    this.setCached('chats', chats, ttl);
  }

  getCachedWars(): any[] {
    return this.getCached('wars') || [];
  }

  setCachedWars(wars: any[], ttl?: number): void {
    this.setCached('wars', wars, ttl);
  }

  getCachedUser(): any | null {
    return this.getCached('user');
  }

  setCachedUser(user: any, ttl?: number): void {
    this.setCached('user', user, ttl);
  }

  // Sync status
  getSyncStatus(): {
    isOnline: boolean;
    queueSize: number;
    lastSync: number | null;
  } {
    return {
      isOnline: this.isOnline,
      queueSize: this.requestQueue.length,
      lastSync: this.storage.getNumber('last_sync') || null,
    };
  }

  markSyncComplete(): void {
    this.storage.set('last_sync', Date.now());
  }

  // Force sync
  async forceSync(): Promise<void> {
    if (this.isOnline) {
      await this.processQueue();
      this.markSyncComplete();
    }
  }
}

// Export singleton instance
export const offlineService = new OfflineService();