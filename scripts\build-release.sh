#!/bin/bash

# Warfront Nations Mobile - Release Build Script
# This script builds the app for production deployment

set -e

echo "🚀 Starting Warfront Nations Mobile release build..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

cd "$PROJECT_ROOT"

# Ensure we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: package.json not found. Make sure you're running this from the project root.${NC}"
    exit 1
fi

echo -e "${BLUE}📁 Project root: $PROJECT_ROOT${NC}"

# Clean previous builds
echo -e "${YELLOW}🧹 Cleaning previous builds...${NC}"
rm -rf android/app/build
rm -rf node_modules/.cache
rm -rf /tmp/react-*

# Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
npm ci --production=false

# Run tests if they exist
if [ -d "src/__tests__" ] || [ -f "jest.config.js" ]; then
    echo -e "${YELLOW}🧪 Running tests...${NC}"
    npm test -- --watchAll=false --coverage=false
fi

# Lint the code
if command -v npx &> /dev/null && npm list eslint &> /dev/null; then
    echo -e "${YELLOW}🔍 Linting code...${NC}"
    npx eslint src/ --ext .js,.jsx,.ts,.tsx
fi

# TypeScript check
if [ -f "tsconfig.json" ]; then
    echo -e "${YELLOW}📝 TypeScript type checking...${NC}"
    npx tsc --noEmit
fi

# Build for different flavors and environments
build_variant() {
    local flavor=$1
    local buildType=$2
    local outputName="$3"
    
    echo -e "${BLUE}🔨 Building $flavor $buildType...${NC}"
    
    cd android
    
    # Clean gradle cache
    ./gradlew clean
    
    # Build the APK/AAB
    if [ "$buildType" = "release" ]; then
        echo -e "${YELLOW}📱 Building signed release APK and AAB...${NC}"
        ./gradlew assemble${flavor^}Release
        ./gradlew bundle${flavor^}Release
        
        # Copy outputs to a convenient location
        mkdir -p "../release-builds"
        
        if [ -f "app/build/outputs/apk/${flavor}/release/app-${flavor}-release.apk" ]; then
            cp "app/build/outputs/apk/${flavor}/release/app-${flavor}-release.apk" "../release-builds/${outputName}.apk"
            echo -e "${GREEN}✅ APK built: release-builds/${outputName}.apk${NC}"
        fi
        
        if [ -f "app/build/outputs/bundle/${flavor}Release/app-${flavor}-release.aab" ]; then
            cp "app/build/outputs/bundle/${flavor}Release/app-${flavor}-release.aab" "../release-builds/${outputName}.aab"
            echo -e "${GREEN}✅ AAB built: release-builds/${outputName}.aab${NC}"
        fi
    else
        ./gradlew assemble${flavor^}${buildType^}
        
        mkdir -p "../debug-builds"
        if [ -f "app/build/outputs/apk/${flavor}/${buildType}/app-${flavor}-${buildType}.apk" ]; then
            cp "app/build/outputs/apk/${flavor}/${buildType}/app-${flavor}-${buildType}.apk" "../debug-builds/${outputName}.apk"
            echo -e "${GREEN}✅ Debug APK built: debug-builds/${outputName}.apk${NC}"
        fi
    fi
    
    cd ..
}

# Check if keystore is configured for release builds
if [ ! -f "android/gradle.properties" ] || ! grep -q "WARFRONT_UPLOAD_STORE_FILE" android/gradle.properties; then
    echo -e "${YELLOW}⚠️  Warning: Release keystore not configured. Only debug builds will work.${NC}"
    echo -e "${YELLOW}   To configure release signing, add the following to android/gradle.properties:${NC}"
    echo -e "${YELLOW}   WARFRONT_UPLOAD_STORE_FILE=your-keystore.keystore${NC}"
    echo -e "${YELLOW}   WARFRONT_UPLOAD_KEY_ALIAS=your-key-alias${NC}"
    echo -e "${YELLOW}   WARFRONT_UPLOAD_STORE_PASSWORD=your-store-password${NC}"
    echo -e "${YELLOW}   WARFRONT_UPLOAD_KEY_PASSWORD=your-key-password${NC}"
    echo ""
fi

# Build different variants
echo -e "${BLUE}🏗️  Building application variants...${NC}"

# Free version - staging
echo -e "${BLUE}Building Free Staging...${NC}"
build_variant "free" "staging" "warfront-nations-free-staging"

# Premium version - staging  
echo -e "${BLUE}Building Premium Staging...${NC}"
build_variant "premium" "staging" "warfront-nations-premium-staging"

# Check if release keystore is available
if grep -q "WARFRONT_UPLOAD_STORE_FILE" android/gradle.properties 2>/dev/null; then
    # Free version - release
    echo -e "${BLUE}Building Free Release...${NC}"
    build_variant "free" "release" "warfront-nations-free-release"
    
    # Premium version - release
    echo -e "${BLUE}Building Premium Release...${NC}" 
    build_variant "premium" "release" "warfront-nations-premium-release"
else
    echo -e "${YELLOW}⚠️  Skipping release builds - keystore not configured${NC}"
fi

# Generate build info
echo -e "${YELLOW}📋 Generating build info...${NC}"
BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
APP_VERSION=$(grep '"version"' package.json | cut -d'"' -f4)

cat > build-info.json << EOF
{
  "buildTime": "$BUILD_TIME",
  "gitCommit": "$GIT_COMMIT",
  "gitBranch": "$GIT_BRANCH",
  "appVersion": "$APP_VERSION",
  "platform": "android",
  "buildType": "release"
}
EOF

echo -e "${GREEN}✅ Build completed successfully!${NC}"
echo -e "${GREEN}📱 APKs and AABs are available in the release-builds/ directory${NC}"
echo ""
echo -e "${BLUE}📋 Build Summary:${NC}"
echo -e "   App Version: $APP_VERSION"
echo -e "   Git Commit: $GIT_COMMIT"
echo -e "   Git Branch: $GIT_BRANCH"
echo -e "   Build Time: $BUILD_TIME"
echo ""
echo -e "${BLUE}🚀 Next steps:${NC}"
echo -e "   1. Test the APKs on physical devices"
echo -e "   2. Upload AAB files to Google Play Console"
echo -e "   3. Create release notes"
echo -e "   4. Submit for review"

# Optional: Open release-builds directory
if command -v explorer.exe &> /dev/null; then
    explorer.exe release-builds
elif command -v open &> /dev/null; then
    open release-builds
fi