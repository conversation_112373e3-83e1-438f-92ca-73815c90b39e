import { Party } from './party';
import { User } from './user';
import { Factory } from './factory';
import { State } from './state';

export enum RegionStatus {
  INDEPENDENT = 'independent',
  STATE_MEMBER = 'state_member',
  AUTONOMY = 'autonomy',
}

export interface Region {
  id: string;
  name: string;
  countryCode?: string;
  parliamentCreatedAt?: Date;
  autonomyParliamentCreatedAt?: Date;
  initialAttackDamage: number;
  initialDefendDamage: number;
  pollution: number;
  taxRate: number;
  marketTaxes: number;
  factoryOutputTaxes?: Record<string, number>;
  population: number;
  seaAccess: boolean;
  resources?: {
    gold?: { current: number; max: number };
    oil?: { current: number; max: number };
    ore?: { current: number; max: number };
    uranium?: { current: number; max: number };
    diamonds?: { current: number; max: number };
  };
  healthIndex: number;
  militaryIndex: number;
  educationIndex: number;
  developmentIndex: number;
  residencyForWork: boolean;
  lastRevolution?: Date;
  lastCoup?: Date;
  bordersWith?: string[];
  topRating: number;
  status: RegionStatus;
  state?: State;
  parties?: Party[];
  users?: User[];
  factories?: Factory[];
  createdAt: Date;
  updatedAt: Date;
}