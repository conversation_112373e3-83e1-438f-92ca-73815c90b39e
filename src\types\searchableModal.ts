import { ReactNode } from 'react';

export interface SearchableItem {
  id: string | number;
  name?: string;
  username?: string;
  title?: string;
  description?: string;
  location?: string;
}

export interface Region extends SearchableItem {
  name: string;
  population?: number;
  status?: string;
  location?: string;
  state?: {
    id: string | number;
    name: string;
  };
}

export interface Player extends SearchableItem {
  username: string;
  level?: number;
  strength?: number;
  intelligence?: number;
  endurance?: number;
  isActive?: boolean;
}

export interface SortOption {
  key: string;
  label: string;
  getValue?: (item: SearchableItem) => any;
}

export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

export type SortFunction = (items: SearchableItem[], sortConfig: SortConfig) => SearchableItem[];

export interface SearchableModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  icon?: React.ComponentType<{ width?: number; height?: number; color?: string }>;
  data: SearchableItem[];
  loading?: boolean;
  onItemClick?: (item: SearchableItem) => void;
  renderItem?: (item: SearchableItem) => ReactNode;
  searchPlaceholder?: string;
  searchFilter?: (items: SearchableItem[], searchTerm: string) => SearchableItem[];
  sortOptions?: SortOption[];
  defaultSort?: SortConfig;
  sortFunction?: SortFunction;
}

export type SearchFilterFunction = (items: SearchableItem[], searchTerm: string) => SearchableItem[];