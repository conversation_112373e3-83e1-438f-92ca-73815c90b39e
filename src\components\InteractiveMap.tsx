import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  Dimensions,
  PanResponder,
  Animated,
  Alert,
} from 'react-native';
import Svg, { Path, G, Circle, Text as SvgText } from 'react-native-svg';
import { MapPin, ZoomIn, ZoomOut, Maximize, Search, Info } from 'lucide-react-native';
import { stateService } from '../services/stateService';
import { generateStateColor, lightenColor, darkenColor } from '../utils/colorGenerator';
import SearchableModal from './SearchableModal';
import { LoadingState } from './ProgressIndicator';
import Toast from 'react-native-toast-message';

interface Region {
  id: string;
  name: string;
  population?: number;
  countryCode?: string;
  coordinates?: { lat: number; lng: number };
  state?: {
    id: string;
    name: string;
  };
}

interface MapState {
  id: string;
  name: string;
  regions: Region[];
  leader?: {
    username: string;
  };
}

interface Props {
  onRegionSelect?: (region: Region) => void;
  onStateSelect?: (state: MapState) => void;
  onRegionCheck?: (regionId: string) => void;
  mapMode?: 'political' | 'wars' | 'targets';
  wars?: any[];
  availableTargets?: any[];
  showControls?: boolean;
  interactive?: boolean;
  height?: number;
}

// Simplified world map data - major regions/countries represented as points
const WORLD_REGIONS = [
  // North America
  { id: 'US', name: 'United States', x: 150, y: 120, size: 'large' },
  { id: 'CA', name: 'Canada', x: 140, y: 80, size: 'large' },
  { id: 'MX', name: 'Mexico', x: 130, y: 160, size: 'medium' },
  
  // South America
  { id: 'BR', name: 'Brazil', x: 190, y: 250, size: 'large' },
  { id: 'AR', name: 'Argentina', x: 180, y: 320, size: 'medium' },
  { id: 'PE', name: 'Peru', x: 160, y: 260, size: 'small' },
  { id: 'CO', name: 'Colombia', x: 170, y: 200, size: 'small' },
  
  // Europe
  { id: 'GB', name: 'United Kingdom', x: 260, y: 100, size: 'medium' },
  { id: 'FR', name: 'France', x: 270, y: 110, size: 'medium' },
  { id: 'DE', name: 'Germany', x: 280, y: 100, size: 'medium' },
  { id: 'IT', name: 'Italy', x: 285, y: 120, size: 'medium' },
  { id: 'ES', name: 'Spain', x: 255, y: 125, size: 'medium' },
  { id: 'RU', name: 'Russia', x: 320, y: 90, size: 'large' },
  { id: 'PL', name: 'Poland', x: 290, y: 95, size: 'small' },
  { id: 'NO', name: 'Norway', x: 280, y: 70, size: 'small' },
  { id: 'SE', name: 'Sweden', x: 285, y: 75, size: 'small' },
  
  // Asia
  { id: 'CN', name: 'China', x: 380, y: 130, size: 'large' },
  { id: 'IN', name: 'India', x: 350, y: 170, size: 'large' },
  { id: 'JP', name: 'Japan', x: 420, y: 130, size: 'medium' },
  { id: 'KR', name: 'South Korea', x: 410, y: 125, size: 'small' },
  { id: 'TH', name: 'Thailand', x: 370, y: 190, size: 'small' },
  { id: 'VN', name: 'Vietnam', x: 375, y: 185, size: 'small' },
  { id: 'ID', name: 'Indonesia', x: 390, y: 220, size: 'medium' },
  { id: 'MY', name: 'Malaysia', x: 380, y: 205, size: 'small' },
  { id: 'PH', name: 'Philippines', x: 410, y: 190, size: 'small' },
  
  // Africa
  { id: 'EG', name: 'Egypt', x: 300, y: 160, size: 'medium' },
  { id: 'NG', name: 'Nigeria', x: 270, y: 200, size: 'medium' },
  { id: 'ZA', name: 'South Africa', x: 305, y: 290, size: 'medium' },
  { id: 'KE', name: 'Kenya', x: 315, y: 220, size: 'small' },
  { id: 'MA', name: 'Morocco', x: 255, y: 150, size: 'small' },
  { id: 'ET', name: 'Ethiopia', x: 320, y: 200, size: 'small' },
  
  // Oceania
  { id: 'AU', name: 'Australia', x: 420, y: 290, size: 'large' },
  { id: 'NZ', name: 'New Zealand', x: 450, y: 320, size: 'small' },
  
  // Middle East
  { id: 'SA', name: 'Saudi Arabia', x: 325, y: 175, size: 'medium' },
  { id: 'TR', name: 'Turkey', x: 305, y: 125, size: 'medium' },
  { id: 'IR', name: 'Iran', x: 340, y: 150, size: 'medium' },
  { id: 'IL', name: 'Israel', x: 310, y: 155, size: 'small' },
  { id: 'AE', name: 'UAE', x: 340, y: 180, size: 'small' },
];

const MAP_WIDTH = 480;
const MAP_HEIGHT = 360;

export const InteractiveMap: React.FC<Props> = ({
  onRegionSelect,
  onStateSelect,
  onRegionCheck,
  mapMode = 'political',
  wars = [],
  availableTargets = [],
  showControls = true,
  interactive = true,
  height = 300,
}) => {
  const [states, setStates] = useState<MapState[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRegion, setSelectedRegion] = useState<Region | null>(null);
  const [showRegionModal, setShowRegionModal] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  
  const stateColorMap = useRef(new Map()).current;
  const { width: screenWidth } = Dimensions.get('window');
  const mapScale = Math.min(screenWidth / MAP_WIDTH, height / MAP_HEIGHT);
  
  const panRef = useRef(new Animated.ValueXY()).current;
  const zoomRef = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    fetchMapData();
  }, []);

  const fetchMapData = async () => {
    try {
      setLoading(true);
      const stateData = await stateService.getAllStates();
      setStates(stateData);
    } catch (error) {
      console.error('Error fetching map data:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load map data',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStateColor = (stateId: string): string => {
    if (!stateColorMap.has(stateId)) {
      stateColorMap.set(stateId, generateStateColor(stateColorMap.size));
    }
    return stateColorMap.get(stateId)!;
  };

  const findStateAndRegionByCountryCode = (countryCode: string) => {
    for (const state of states) {
      const region = state.regions.find(r => r.countryCode === countryCode);
      if (region) {
        return { state, region };
      }
    }
    return null;
  };

  const isRegionInWar = (countryCode: string) => {
    return wars.some(war => 
      war.attackerRegion?.countryCode === countryCode || 
      war.defenderRegion?.countryCode === countryCode
    );
  };

  const isRegionAvailableTarget = (countryCode: string) => {
    return availableTargets.some(target => target.countryCode === countryCode);
  };

  const getCircleSize = (regionSize: string, zoom: number): number => {
    const baseSize = regionSize === 'large' ? 12 : regionSize === 'medium' ? 8 : 6;
    return baseSize * zoom;
  };

  const handleRegionPress = (regionData: any) => {
    const stateAndRegion = findStateAndRegionByCountryCode(regionData.id);
    
    if (mapMode === 'targets' && stateAndRegion && onRegionCheck) {
      // In target mode, check for available targets
      onRegionCheck(stateAndRegion.region.id);
      Toast.show({
        type: 'info',
        text1: 'Checking Targets',
        text2: `Loading available targets from ${regionData.name}`,
      });
      return;
    }
    
    if (stateAndRegion) {
      setSelectedRegion(stateAndRegion.region);
      setShowRegionModal(true);
    } else {
      Alert.alert(
        regionData.name,
        'This territory is currently unclaimed.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(zoom * 1.5, 3);
    setZoom(newZoom);
    Animated.timing(zoomRef, {
      toValue: newZoom,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom / 1.5, 0.5);
    setZoom(newZoom);
    Animated.timing(zoomRef, {
      toValue: newZoom,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const resetView = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
    Animated.parallel([
      Animated.timing(zoomRef, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(panRef, {
        toValue: { x: 0, y: 0 },
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => interactive,
    onMoveShouldSetPanResponder: () => interactive,
    onPanResponderMove: (_, gesture) => {
      panRef.setValue({
        x: pan.x + gesture.dx,
        y: pan.y + gesture.dy,
      });
    },
    onPanResponderRelease: (_, gesture) => {
      const newPan = {
        x: pan.x + gesture.dx,
        y: pan.y + gesture.dy,
      };
      setPan(newPan);
      
      panRef.setOffset(newPan);
      panRef.setValue({ x: 0, y: 0 });
    },
  });

  const renderMapRegion = (regionData: any, index: number) => {
    const stateAndRegion = findStateAndRegionByCountryCode(regionData.id);
    let color = stateAndRegion ? getStateColor(stateAndRegion.state.id) : '#2d3748';
    let opacity = stateAndRegion ? 0.8 : 0.3;
    let strokeColor = '#000000';
    let strokeWidth = 0.5;
    
    // Apply war visualization
    if (mapMode === 'wars' && isRegionInWar(regionData.id)) {
      strokeColor = '#ef4444';
      strokeWidth = 2;
      opacity = 1;
    }
    
    // Apply target visualization  
    if (mapMode === 'targets' && isRegionAvailableTarget(regionData.id)) {
      strokeColor = '#f59e0b';
      strokeWidth = 2;
      opacity = 1;
    }
    
    const size = getCircleSize(regionData.size, zoom);
    
    return (
      <G key={regionData.id}>
        <Circle
          cx={regionData.x}
          cy={regionData.y}
          r={size}
          fill={color}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          opacity={opacity}
          onPress={() => handleRegionPress(regionData)}
        />
        {/* War indicator */}
        {mapMode === 'wars' && isRegionInWar(regionData.id) && (
          <Circle
            cx={regionData.x + size * 0.6}
            cy={regionData.y - size * 0.6}
            r={3}
            fill="#ef4444"
            stroke="#ffffff"
            strokeWidth={1}
          />
        )}
        {/* Target indicator */}
        {mapMode === 'targets' && isRegionAvailableTarget(regionData.id) && (
          <Circle
            cx={regionData.x + size * 0.6}
            cy={regionData.y - size * 0.6}
            r={3}
            fill="#f59e0b"
            stroke="#ffffff"
            strokeWidth={1}
          />
        )}
        {zoom > 1.2 && (
          <SvgText
            x={regionData.x}
            y={regionData.y + size + 12}
            fontSize={Math.min(10, 8 * zoom)}
            fill="#ffffff"
            textAnchor="middle"
            opacity={0.9}
          >
            {regionData.name}
          </SvgText>
        )}
      </G>
    );
  };

  const searchableRegions = states.flatMap(state =>
    state.regions.map(region => ({
      id: region.id,
      name: region.name,
      description: `${state.name} • Population: ${region.population?.toLocaleString() || 'Unknown'}`,
      state,
      region,
    }))
  );

  if (loading) {
    return (
      <View style={{ height, justifyContent: 'center', alignItems: 'center', backgroundColor: '#111827' }}>
        <LoadingState message="Loading world map..." />
      </View>
    );
  }

  return (
    <View style={{ height, backgroundColor: '#111827' }}>
      {/* Map Controls */}
      {showControls && (
        <View style={{
          position: 'absolute',
          top: 16,
          right: 16,
          zIndex: 10,
          flexDirection: 'column',
          gap: 8,
        }}>
          <TouchableOpacity
            onPress={() => setShowSearchModal(true)}
            style={{
              backgroundColor: '#1F2937',
              padding: 8,
              borderRadius: 8,
              borderWidth: 1,
              borderColor: '#3f87ff',
            }}
          >
            <Search width={20} height={20} color="#3f87ff" />
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={handleZoomIn}
            style={{
              backgroundColor: '#1F2937',
              padding: 8,
              borderRadius: 8,
              borderWidth: 1,
              borderColor: '#374151',
            }}
          >
            <ZoomIn width={20} height={20} color="#ffffff" />
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={handleZoomOut}
            style={{
              backgroundColor: '#1F2937',
              padding: 8,
              borderRadius: 8,
              borderWidth: 1,
              borderColor: '#374151',
            }}
          >
            <ZoomOut width={20} height={20} color="#ffffff" />
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={resetView}
            style={{
              backgroundColor: '#1F2937',
              padding: 8,
              borderRadius: 8,
              borderWidth: 1,
              borderColor: '#374151',
            }}
          >
            <Maximize width={20} height={20} color="#ffffff" />
          </TouchableOpacity>
        </View>
      )}

      {/* Map Legend */}
      <View style={{
        position: 'absolute',
        bottom: 16,
        left: 16,
        backgroundColor: '#1F2937',
        padding: 12,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#374151',
        zIndex: 10,
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
          <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: '#3f87ff', marginRight: 8 }} />
          <Text style={{ color: '#ffffff', fontSize: 12 }}>Claimed Territory</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: mapMode !== 'political' ? 4 : 0 }}>
          <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: '#2d3748', opacity: 0.3, marginRight: 8 }} />
          <Text style={{ color: '#9CA3AF', fontSize: 12 }}>Unclaimed Territory</Text>
        </View>
        {mapMode === 'wars' && (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: '#ef4444', marginRight: 8 }} />
            <Text style={{ color: '#ef4444', fontSize: 12 }}>At War</Text>
          </View>
        )}
        {mapMode === 'targets' && (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: '#f59e0b', marginRight: 8 }} />
            <Text style={{ color: '#f59e0b', fontSize: 12 }}>Available Target</Text>
          </View>
        )}
      </View>

      {/* Interactive Map */}
      <Animated.View
        style={{
          flex: 1,
          transform: [
            { translateX: panRef.x },
            { translateY: panRef.y },
            { scale: zoomRef },
          ],
        }}
        {...(interactive ? panResponder.panHandlers : {})}
      >
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#0F1419', // Ocean color
        }}>
          <Svg
            width={MAP_WIDTH * mapScale}
            height={MAP_HEIGHT * mapScale}
            viewBox={`0 0 ${MAP_WIDTH} ${MAP_HEIGHT}`}
          >
            {/* Grid lines for reference */}
            <G opacity={0.1}>
              {Array.from({ length: 10 }, (_, i) => (
                <Path
                  key={`v${i}`}
                  d={`M ${(i + 1) * (MAP_WIDTH / 10)} 0 L ${(i + 1) * (MAP_WIDTH / 10)} ${MAP_HEIGHT}`}
                  stroke="#ffffff"
                  strokeWidth={0.5}
                />
              ))}
              {Array.from({ length: 8 }, (_, i) => (
                <Path
                  key={`h${i}`}
                  d={`M 0 ${(i + 1) * (MAP_HEIGHT / 8)} L ${MAP_WIDTH} ${(i + 1) * (MAP_HEIGHT / 8)}`}
                  stroke="#ffffff"
                  strokeWidth={0.5}
                />
              ))}
            </G>

            {/* Render regions */}
            {WORLD_REGIONS.map((region, index) => renderMapRegion(region, index))}
          </Svg>
        </View>
      </Animated.View>

      {/* Search Modal */}
      <SearchableModal
        isOpen={showSearchModal}
        onClose={() => setShowSearchModal(false)}
        title="Search Regions"
        icon={Search}
        data={searchableRegions}
        onItemClick={(item: any) => {
          setSelectedRegion(item.region);
          setShowRegionModal(true);
          setShowSearchModal(false);
        }}
        searchPlaceholder="Search regions and states..."
        sortOptions={[
          { key: 'name', label: 'Name' },
          { key: 'state', label: 'State', getValue: (item: any) => item.state.name },
          { key: 'population', label: 'Population', getValue: (item: any) => item.region.population || 0 },
        ]}
        defaultSort={{ field: 'name', direction: 'asc' }}
      />

      {/* Region Detail Modal */}
      <Modal
        visible={showRegionModal}
        animationType="slide"
        transparent
        onRequestClose={() => setShowRegionModal(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 16,
        }}>
          <View style={{
            backgroundColor: '#1F2937',
            borderRadius: 12,
            padding: 24,
            width: '100%',
            maxWidth: 400,
          }}>
            {selectedRegion && (
              <>
                <View style={{ alignItems: 'center', marginBottom: 20 }}>
                  <MapPin width={32} height={32} color="#3f87ff" />
                  <Text style={{
                    color: '#ffffff',
                    fontSize: 24,
                    fontWeight: 'bold',
                    marginTop: 8,
                    textAlign: 'center',
                  }}>
                    {selectedRegion.name}
                  </Text>
                  {selectedRegion.state && (
                    <View style={{
                      backgroundColor: getStateColor(selectedRegion.state.id),
                      paddingHorizontal: 12,
                      paddingVertical: 4,
                      borderRadius: 12,
                      marginTop: 8,
                    }}>
                      <Text style={{ color: '#ffffff', fontSize: 14, fontWeight: '600' }}>
                        {selectedRegion.state.name}
                      </Text>
                    </View>
                  )}
                </View>

                <View style={{ marginBottom: 24 }}>
                  <View style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginBottom: 8,
                  }}>
                    <Text style={{ color: '#9CA3AF' }}>Population:</Text>
                    <Text style={{ color: '#ffffff', fontWeight: '600' }}>
                      {selectedRegion.population?.toLocaleString() || 'Unknown'}
                    </Text>
                  </View>
                </View>

                <View style={{ flexDirection: 'row', gap: 12 }}>
                  <TouchableOpacity
                    onPress={() => setShowRegionModal(false)}
                    style={{
                      flex: 1,
                      backgroundColor: '#374151',
                      paddingVertical: 12,
                      borderRadius: 8,
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '500' }}>
                      Close
                    </Text>
                  </TouchableOpacity>
                  
                  {selectedRegion.state && (
                    <TouchableOpacity
                      onPress={() => {
                        if (onStateSelect && selectedRegion.state) {
                          const fullState = states.find(s => s.id === selectedRegion.state!.id);
                          if (fullState) {
                            onStateSelect(fullState);
                          }
                        }
                        setShowRegionModal(false);
                      }}
                      style={{
                        flex: 1,
                        backgroundColor: '#3f87ff',
                        paddingVertical: 12,
                        borderRadius: 8,
                        alignItems: 'center',
                      }}
                    >
                      <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
                        View State
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};