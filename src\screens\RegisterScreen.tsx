import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { Eye, EyeOff, ChevronDown } from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { regionService } from '../services/regionService';
import { GoogleSignInButton } from '../components/GoogleSignInButton';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

export const RegisterScreen: React.FC<Props> = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [regionId, setRegionId] = useState('');
  const [regions, setRegions] = useState<any[]>([]);
  const [showPassword, setShowPassword] = useState(false);
  const { register, isLoading } = useAuthStore();

  useEffect(() => {
    const fetchRegions = async () => {
      try {
        const regionsData = await regionService.getAllRegions();
        setRegions(regionsData);
      } catch (error) {
        console.error('Failed to load regions:', error);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to load regions',
        });
      }
    };

    fetchRegions();
  }, []);

  const handleRegister = async () => {
    if (!username || !email || !password || !regionId) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please fill in all fields',
      });
      return;
    }

    if (password.length < 6) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Password must be at least 6 characters long',
      });
      return;
    }

    try {
      await register(username, email, password);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Account created! Please check your email to verify your account',
      });
      navigation.navigate('Login');
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Registration Failed',
        text2: error.message || 'Please try again',
      });
    }
  };

  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
        backgroundColor: '#0F0F10',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 16,
      }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
        showsVerticalScrollIndicator={false}
        style={{ width: '100%' }}
      >
        <View style={{
          width: '100%',
          maxWidth: 400,
          alignSelf: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: 16,
          padding: 32,
        }}>
          <View style={{ alignItems: 'center', marginBottom: 24 }}>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={{
                fontSize: 14,
                textAlign: 'center',
                color: '#A1A1A1',
                marginBottom: 16,
              }}>
                Back to login page
              </Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => navigation.navigate('Landing')}>
              <Image
                source={require('../../assets/images/wn-logo.png')}
                style={{
                  width: 56,
                  height: 56,
                  marginBottom: 16,
                }}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#ffffff',
              textAlign: 'center',
            }}>
              Register
            </Text>
            <Text style={{
              marginTop: 4,
              fontSize: 14,
              color: '#A1A1A1',
              textAlign: 'center',
            }}>
              Now is the time to begin your legacy.
            </Text>
          </View>

          <View>
            <TextInput
              placeholder="Username"
              placeholderTextColor="#777"
              value={username}
              onChangeText={setUsername}
              autoCapitalize="none"
              autoCorrect={false}
              style={{
                width: '100%',
                marginBottom: 16,
                paddingHorizontal: 16,
                paddingVertical: 12,
                borderRadius: 12,
                backgroundColor: '#1C1C1E',
                color: '#ffffff',
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.1)',
                fontSize: 16,
              }}
            />

            <TextInput
              placeholder="Email"
              placeholderTextColor="#777"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              style={{
                width: '100%',
                marginBottom: 16,
                paddingHorizontal: 16,
                paddingVertical: 12,
                borderRadius: 12,
                backgroundColor: '#1C1C1E',
                color: '#ffffff',
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.1)',
                fontSize: 16,
              }}
            />

            <View style={{ position: 'relative', marginBottom: 16 }}>
              <TextInput
                placeholder="Password"
                placeholderTextColor="#777"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                style={{
                  width: '100%',
                  paddingHorizontal: 16,
                  paddingVertical: 12,
                  paddingRight: 48,
                  borderRadius: 12,
                  backgroundColor: '#1C1C1E',
                  color: '#ffffff',
                  borderWidth: 1,
                  borderColor: 'rgba(255, 255, 255, 0.1)',
                  fontSize: 16,
                }}
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                style={{
                  position: 'absolute',
                  right: 16,
                  top: 14,
                }}
              >
                {showPassword ? (
                  <EyeOff width={20} height={20} color="#888" />
                ) : (
                  <Eye width={20} height={20} color="#888" />
                )}
              </TouchableOpacity>
            </View>

            <View style={{ marginBottom: 16 }}>
              <View style={{
                width: '100%',
                paddingHorizontal: 16,
                paddingVertical: Platform.OS === 'ios' ? 0 : 8,
                borderRadius: 12,
                backgroundColor: '#1C1C1E',
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.1)',
              }}>
                <Picker
                  selectedValue={regionId}
                  onValueChange={(itemValue) => setRegionId(itemValue)}
                  style={{
                    color: '#ffffff',
                    backgroundColor: 'transparent',
                  }}
                  dropdownIconColor="#888"
                  itemStyle={{
                    color: '#ffffff',
                    backgroundColor: '#1C1C1E',
                  }}
                >
                  <Picker.Item
                    label="Select your region"
                    value=""
                    color={Platform.OS === 'ios' ? '#777' : '#777'}
                  />
                  {regions.map((region) => (
                    <Picker.Item
                      key={region.id}
                      label={region.name}
                      value={region.id}
                      color={Platform.OS === 'ios' ? '#ffffff' : '#ffffff'}
                    />
                  ))}
                </Picker>
              </View>
            </View>

            <TouchableOpacity
              onPress={handleRegister}
              disabled={isLoading}
              style={{
                marginTop: 12,
                width: '100%',
                backgroundColor: '#1C1C1E',
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.2)',
                paddingVertical: 12,
                borderRadius: 12,
                opacity: isLoading ? 0.6 : 1,
              }}
            >
              <Text style={{
                color: '#ffffff',
                fontWeight: 'bold',
                textAlign: 'center',
                fontSize: 16,
              }}>
                {isLoading ? 'Creating account...' : 'Register'}
              </Text>
            </TouchableOpacity>

            {/* OAuth Divider */}
            <View style={{
              position: 'relative',
              marginVertical: 24,
              alignItems: 'center',
            }}>
              <View style={{
                position: 'absolute',
                top: '50%',
                left: 0,
                right: 0,
                height: 1,
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              }} />
              <View style={{
                backgroundColor: 'rgba(255, 255, 255, 0.05)',
                paddingHorizontal: 8,
              }}>
                <Text style={{
                  color: '#A1A1A1',
                  textAlign: 'center',
                  fontSize: 14,
                }}>
                  Or
                </Text>
              </View>
            </View>

            {/* Google Sign-In Button */}
            <GoogleSignInButton 
              onSuccess={() => {
                Toast.show({
                  type: 'success',
                  text1: 'Success',
                  text2: 'Account created successfully!',
                });
              }}
            />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

