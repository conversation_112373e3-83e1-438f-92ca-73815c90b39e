import React from 'react';
import {
  View,
  Text,
  Animated,
  ActivityIndicator,
} from 'react-native';

interface ProgressBarProps {
  progress: number; // 0-100
  height?: number;
  backgroundColor?: string;
  progressColor?: string;
  showPercentage?: boolean;
  animated?: boolean;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  height = 8,
  backgroundColor = '#374151',
  progressColor = '#3f87ff',
  showPercentage = false,
  animated = true,
}) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (animated) {
      Animated.timing(animatedValue, {
        toValue: progress,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      animatedValue.setValue(progress);
    }
  }, [progress, animated, animatedValue]);

  return (
    <View style={{ width: '100%' }}>
      <View style={{
        height,
        backgroundColor,
        borderRadius: height / 2,
        overflow: 'hidden',
      }}>
        <Animated.View style={{
          height: '100%',
          backgroundColor: progressColor,
          borderRadius: height / 2,
          width: animatedValue.interpolate({
            inputRange: [0, 100],
            outputRange: ['0%', '100%'],
            extrapolate: 'clamp',
          }),
        }} />
      </View>
      {showPercentage && (
        <Text style={{
          color: '#9CA3AF',
          fontSize: 12,
          textAlign: 'center',
          marginTop: 4,
        }}>
          {Math.round(progress)}%
        </Text>
      )}
    </View>
  );
};

interface CircularProgressProps {
  progress: number; // 0-100
  size?: number;
  strokeWidth?: number;
  backgroundColor?: string;
  progressColor?: string;
  showPercentage?: boolean;
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  progress,
  size = 60,
  strokeWidth = 6,
  backgroundColor = '#374151',
  progressColor = '#3f87ff',
  showPercentage = true,
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <View style={{
      width: size,
      height: size,
      justifyContent: 'center',
      alignItems: 'center',
    }}>
      <View style={{
        position: 'absolute',
        width: size,
        height: size,
        borderRadius: size / 2,
        borderWidth: strokeWidth,
        borderColor: backgroundColor,
      }} />
      <View style={{
        position: 'absolute',
        width: size,
        height: size,
        borderRadius: size / 2,
        borderWidth: strokeWidth,
        borderColor: progressColor,
        borderTopColor: 'transparent',
        borderRightColor: 'transparent',
        borderBottomColor: progress > 50 ? progressColor : 'transparent',
        borderLeftColor: progress > 25 ? progressColor : 'transparent',
        transform: [{ rotate: `${(progress / 100) * 360}deg` }],
      }} />
      {showPercentage && (
        <Text style={{
          color: '#ffffff',
          fontSize: size * 0.2,
          fontWeight: '600',
        }}>
          {Math.round(progress)}%
        </Text>
      )}
    </View>
  );
};

interface LoadingStateProps {
  message?: string;
  type?: 'spinner' | 'dots' | 'pulse';
  color?: string;
  size?: 'small' | 'large';
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  type = 'spinner',
  color = '#3f87ff',
  size = 'large',
}) => {
  const animatedOpacity = React.useRef(new Animated.Value(0.3)).current;

  React.useEffect(() => {
    if (type === 'pulse') {
      const pulse = () => {
        Animated.sequence([
          Animated.timing(animatedOpacity, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(animatedOpacity, {
            toValue: 0.3,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]).start(() => pulse());
      };
      pulse();
    }
  }, [type, animatedOpacity]);

  const renderIndicator = () => {
    switch (type) {
      case 'dots':
        return (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {[0, 1, 2].map((index) => (
              <View
                key={index}
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: color,
                  marginHorizontal: 2,
                  opacity: 0.7,
                }}
              />
            ))}
          </View>
        );
      case 'pulse':
        return (
          <Animated.View style={{
            width: size === 'large' ? 40 : 24,
            height: size === 'large' ? 40 : 24,
            borderRadius: size === 'large' ? 20 : 12,
            backgroundColor: color,
            opacity: animatedOpacity,
          }} />
        );
      default:
        return (
          <ActivityIndicator
            size={size}
            color={color}
          />
        );
    }
  };

  return (
    <View style={{
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 20,
    }}>
      {renderIndicator()}
      <Text style={{
        color: '#9CA3AF',
        fontSize: 16,
        marginTop: 12,
        textAlign: 'center',
      }}>
        {message}
      </Text>
    </View>
  );
};

interface SkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
}) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const shimmer = () => {
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => shimmer());
    };
    shimmer();
  }, [animatedValue]);

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: '#374151',
          borderRadius,
          opacity: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [0.3, 0.7],
          }),
        },
        style,
      ]}
    />
  );
};

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  completedColor?: string;
  activeColor?: string;
  inactiveColor?: string;
  size?: number;
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({
  currentStep,
  totalSteps,
  completedColor = '#10B981',
  activeColor = '#3f87ff',
  inactiveColor = '#374151',
  size = 32,
}) => {
  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    }}>
      {Array.from({ length: totalSteps }, (_, index) => {
        const stepNumber = index + 1;
        const isCompleted = stepNumber < currentStep;
        const isActive = stepNumber === currentStep;
        
        let backgroundColor = inactiveColor;
        if (isCompleted) backgroundColor = completedColor;
        else if (isActive) backgroundColor = activeColor;

        return (
          <React.Fragment key={stepNumber}>
            <View style={{
              width: size,
              height: size,
              borderRadius: size / 2,
              backgroundColor,
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: 2,
              borderColor: backgroundColor,
            }}>
              <Text style={{
                color: '#ffffff',
                fontSize: size * 0.4,
                fontWeight: '600',
              }}>
                {stepNumber}
              </Text>
            </View>
            {index < totalSteps - 1 && (
              <View style={{
                width: 32,
                height: 2,
                backgroundColor: stepNumber < currentStep ? completedColor : inactiveColor,
                marginHorizontal: 4,
              }} />
            )}
          </React.Fragment>
        );
      })}
    </View>
  );
};