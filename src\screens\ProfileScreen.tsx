import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import * as Clipboard from 'expo-clipboard';
import {
  Crown,
  Edit2,
  Check,
  X,
  Share2,
  Co<PERSON>,
  User,
  Du<PERSON>bell,
  Shield,
  Brain,
  Globe,
  Users,
  Flag,
  Flame,
  BarChart3,
  TrendingUp,
  DollarSign,
  Medal,
  Landmark,
  Trophy,
  Coins,
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { userService } from '../services/userService';
import { stateService } from '../services/stateService';
import { warService } from '../services/warService';
import { calculateTrainingTime } from '../utils/calculateTrainingTime';
import { calculateTrainingCost } from '../utils/calculateTrainingCost';
import { balanceLogService, BalanceLog } from '../services/balanceLogService';
import PhotoUpload from '../components/PhotoUpload';
import { BalanceLogsModal } from '../components/BalanceLogsModal';
import Toast from 'react-native-toast-message';
import { showErrorToast, showSuccessToast } from '../utils/toastUtils';

interface Props {
  navigation: any;
}

export const ProfileScreen: React.FC<Props> = ({ navigation }) => {
  useAuthGuard({ navigation });
  
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [myState, setMyState] = useState<any>(null);
  const [myWars, setMyWars] = useState<any[]>([]);
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [newUsername, setNewUsername] = useState('');
  const [isUpdatingUsername, setIsUpdatingUsername] = useState(false);
  const [countdown, setCountdown] = useState<string | null>(null);
  const [balanceLogsVisible, setBalanceLogsVisible] = useState(false);
  const [balanceLogs, setBalanceLogs] = useState<BalanceLog[]>([]);
  const [balanceLogsLoading, setBalanceLogsLoading] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Calculate level progress
  const xpForCurrentLevel = Math.pow(user?.level || 1, 2) * 100;
  const xpForNextLevel = Math.pow((user?.level || 1) + 1, 2) * 100;
  const xpIntoLevel = (user?.experience || 0) - xpForCurrentLevel;
  const xpNeeded = xpForNextLevel - xpForCurrentLevel;
  const levelBar = Math.floor((xpIntoLevel / xpNeeded) * 100);

  const isTraining = !!countdown;

  useEffect(() => {
    fetchData();
  }, []);

  // Training countdown effect
  useEffect(() => {
    if (!user?.trainingExpiresAt) return;

    const target = new Date(user.trainingExpiresAt).getTime();
    const now = Date.now();

    if (target <= now) return;

    const intervalId = setInterval(() => {
      const currentTime = Date.now();
      const timeRemaining = target - currentTime;

      if (timeRemaining <= 0) {
        setCountdown(null);
        clearInterval(intervalId);
        showSuccessToast(`Training completed! Your ${user?.trainingPerk || 'skill'} has improved!`);
        fetchData();
      } else {
        const hours = Math.floor(timeRemaining / 1000 / 60 / 60);
        const minutes = Math.floor((timeRemaining / 1000 / 60) % 60);
        const seconds = Math.floor((timeRemaining / 1000) % 60);

        if (hours === 0 && minutes === 0) {
          setCountdown(`${seconds}s`);
        } else if (hours === 0) {
          setCountdown(`${minutes}m ${seconds}s`);
        } else {
          setCountdown(`${hours}h ${minutes}m ${seconds}s`);
        }
      }
    }, 1000);

    return () => clearInterval(intervalId);
  }, [user?.trainingExpiresAt]);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Refresh user data from the store to get updated training status
      const { refreshUser } = useAuthStore.getState();
      await refreshUser();

      // Fetch state data
      try {
        const stateRes = await stateService.getMyState();
        setMyState(stateRes);
      } catch (error) {
        setMyState(null);
      }

      // Fetch wars data
      try {
        const myWarsData = await warService.getMyWars();
        setMyWars(myWarsData || []);
      } catch (error) {
        setMyWars([]);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching profile data:', error);
      showErrorToast('Failed to load profile data');
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  // Username editing functions
  const handleEditUsername = () => {
    setNewUsername(user?.username || '');
    setIsEditingUsername(true);
  };

  const handleCancelEdit = () => {
    setIsEditingUsername(false);
    setNewUsername('');
  };

  const handleSaveUsername = async () => {
    if (!newUsername.trim()) {
      showErrorToast('Username cannot be empty');
      return;
    }

    if (newUsername === user?.username) {
      setIsEditingUsername(false);
      return;
    }

    setIsUpdatingUsername(true);
    try {
      await userService.updateProfile({
        username: newUsername.trim(),
        id: user?.id || 0,
      });
      
      showSuccessToast('Username updated successfully!');

      // Force refresh the user data to reflect the change
      fetchData();
      setIsEditingUsername(false);
      setNewUsername('');
    } catch (error) {
      showErrorToast(error || 'Failed to update username');
    } finally {
      setIsUpdatingUsername(false);
    }
  };

  const handleTrain = async (perkType: string, currency: 'gold' | 'money') => {
    try {
      if (!user) return;

      const currentLevel = user[perkType as keyof typeof user] as number || 0;
      const eduIndex = user.region?.educationIndex || 0;
      const isPremium = user.isPremium || false;

      // Calculate dynamic training time (in seconds)
      const duration = calculateTrainingTime({
        perkLevel: currentLevel,
        eduResidency: eduIndex,
        currency,
        isPremium,
      });

      // Calculate dynamic training cost
      const costs = calculateTrainingCost({
        perkLevel: currentLevel,
        durationMinutes: duration / 60, // Convert seconds to minutes
      });

      const amount = currency === 'gold' ? costs.gold : costs.money;

      // Check if user has enough currency
      if (currency === 'gold' && user.gold < amount) {
        Toast.show({
          type: 'error',
          text1: 'Insufficient Gold',
          text2: `You need ${amount} gold but only have ${user.gold}`
        });
        return;
      }

      if (currency === 'money' && user.money < amount) {
        Toast.show({
          type: 'error',
          text1: 'Insufficient Money',
          text2: `You need $${amount} but only have $${user.money}`
        });
        return;
      }

      await userService.trainUser({
        trainingType: perkType as 'strength' | 'intelligence' | 'endurance',
        duration,
        currency,
        amount,
      });

      showSuccessToast(`Started training ${perkType}`);

      // Refresh user data to get updated training status
      const { refreshUser } = useAuthStore.getState();
      await refreshUser();
      fetchData();
    } catch (error: any) {
      console.error('🚨 Training error caught:', error);
      console.error('🔍 Error response:', error?.response?.data);
      console.error('🔍 Error message:', error?.message);
      showErrorToast(error);
    }
  };

  // Format time function like frontend
  const formatTime = (totalSeconds: number) => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    return `${hours}h ${minutes}m ${seconds}s`;
  };

  const getTrainingInfo = (perkType: string) => {
    if (!user) return { goldCost: 0, moneyCost: 0, goldTime: 0, moneyTime: 0 };

    const currentLevel = user[perkType as keyof typeof user] as number || 0;
    const eduIndex = user.region?.educationIndex || 0;
    const isPremium = user.isPremium || false;

    const goldTime = calculateTrainingTime({
      perkLevel: currentLevel,
      eduResidency: eduIndex,
      currency: 'gold',
      isPremium,
    });

    const moneyTime = calculateTrainingTime({
      perkLevel: currentLevel,
      eduResidency: eduIndex,
      currency: 'money',
      isPremium,
    });

    const goldCosts = calculateTrainingCost({
      perkLevel: currentLevel,
      durationMinutes: goldTime / 60, // Convert seconds to minutes
    });

    const moneyCosts = calculateTrainingCost({
      perkLevel: currentLevel,
      durationMinutes: moneyTime / 60, // Convert seconds to minutes
    });

    return {
      goldCost: goldCosts.gold,
      moneyCost: moneyCosts.money,
      goldTime: goldTime,
      moneyTime: moneyTime,
    };
  };

  const loadBalanceLogs = async () => {
    setBalanceLogsLoading(true);
    try {
      const response = await balanceLogService.getBalanceLogs({ limit: 50 });
      setBalanceLogs(response.data);
    } catch (error) {
      console.error('Error loading balance logs:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load transaction history',
      });
    } finally {
      setBalanceLogsLoading(false);
    }
  };

  const handleBalanceClick = () => {
    setBalanceLogsVisible(true);
    if (balanceLogs.length === 0) {
      loadBalanceLogs();
    }
  };

  const handleAvatarUpload = async (formData: FormData) => {
    setIsUploadingAvatar(true);
    try {
      await userService.uploadAvatar(formData);
      // Refresh user data to show the new avatar
      await fetchData();
      showSuccessToast('Avatar updated successfully!');
    } catch (error: any) {
      showErrorToast(error || 'Failed to upload avatar');
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  const getShareableProfileUrl = () => {
    // You might need to adjust this URL based on your web app's domain
    return `https://warfront-nations.com/users/${user?.id}`;
  };

  const handleCopyProfileUrl = async () => {
    try {
      const url = getShareableProfileUrl();
      Clipboard.setString(url);
      setCopySuccess(true);
      Toast.show({
        type: 'success',
        text1: 'Copied!',
        text2: 'Profile URL copied to clipboard!'
      });
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to copy URL to clipboard'
      });
    }
  };

  const handleShareProfile = () => {
    setShowShareModal(true);
  };

  if (!user) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center p-4">
        <Text className="text-gray-300 text-lg text-center mb-4">
          Please login to view your profile
        </Text>
        <TouchableOpacity 
          className="bg-neonBlue px-6 py-3 rounded-lg"
          onPress={() => navigation.navigate('Login')}
        >
          <Text className="text-white font-bold">Login</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (loading) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <ActivityIndicator size="large" color="#00d4ff" />
        <Text className="text-neonBlue text-xl mt-4">Loading profile...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-900">
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View className="p-4">
          {/* Profile Header */}
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <View className="items-center mb-6">
              {/* Avatar */}
              <View className="mb-4">
                <PhotoUpload
                  currentPhotoUrl={user.avatarUrl}
                  placeholder={user.username || 'U'}
                  onUpload={handleAvatarUpload}
                  isUploading={isUploadingAvatar}
                  size="md"
                />
              </View>

              {/* Username with edit functionality */}
              {isEditingUsername ? (
                <View className="flex-row items-center space-x-2">
                  <TextInput
                    value={newUsername}
                    onChangeText={setNewUsername}
                    className="bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600"
                    placeholder="Enter username"
                    maxLength={20}
                    editable={!isUpdatingUsername}
                  />
                  <TouchableOpacity
                    onPress={handleSaveUsername}
                    disabled={isUpdatingUsername}
                    className="bg-green-600 p-2 rounded-full"
                  >
                    <Check width={16} height={16} color="#ffffff" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={handleCancelEdit}
                    disabled={isUpdatingUsername}
                    className="bg-red-600 p-2 rounded-full"
                  >
                    <X width={16} height={16} color="#ffffff" />
                  </TouchableOpacity>
                </View>
              ) : (
                <View className="flex-row items-center space-x-2">
                  <Text className="text-3xl font-bold text-white">
                    {user.username}
                  </Text>
                  <TouchableOpacity
                    onPress={handleEditUsername}
                    className="p-1 ml-2"
                  >
                    <Edit2 width={16} height={16} color="#9ca3af" />
                  </TouchableOpacity>
                </View>
              )}
              
              <Text className="text-gray-400 text-sm mt-1">
                Member since {new Date(user.createdAt || '').toLocaleDateString()}
              </Text>

              {/* Premium Badge and Share Button */}
              <View className="flex-row items-center justify-center gap-3 mt-3">
                {user.isPremium ? (
                  <View className="bg-yellow-900 border border-yellow-500 px-4 py-2 rounded-xl flex-row items-center">
                    <Crown width={16} height={16} color="#fbbf24" />
                    <Text className="text-yellow-400 font-medium ml-2">Premium Active</Text>
                  </View>
                ) : (
                  <TouchableOpacity
                    className="bg-yellow-600 px-4 py-2 rounded-xl flex-row items-center"
                    onPress={() => navigation.navigate('Shop')}
                  >
                    <Crown width={16} height={16} color="#ffffff" />
                    <Text className="text-white font-medium ml-2">Get Premium</Text>
                  </TouchableOpacity>
                )}
                
                {/* Share Profile Button */}
                <TouchableOpacity
                  className="bg-blue-600 px-4 py-2 rounded-xl flex-row items-center"
                  onPress={handleShareProfile}
                >
                  <Share2 width={16} height={16} color="#ffffff" />
                  <Text className="text-white font-medium ml-2">Share</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Stats Cards */}
            <View className="flex-row space-x-4 mb-4">
              {/* Level Card */}
              <View className="flex-1 bg-blue-900 rounded-xl p-4 border border-blue-500">
                <View className="flex-row items-center mb-2">
                  <TrendingUp width={20} height={20} color="#60a5fa" />
                  <Text className="text-gray-400 text-sm ml-2">Level</Text>
                </View>
                <Text className="text-white text-2xl font-bold mb-2">
                  {user.level || 1}
                </Text>
                <View className="bg-gray-600 h-2 rounded-full overflow-hidden">
                  <View 
                    className="bg-blue-500 h-full rounded-full"
                    style={{ width: `${levelBar}%` }}
                  />
                </View>
                <Text className="text-xs text-gray-400 mt-1">
                  {levelBar}% to next level
                </Text>
              </View>

              {/* Money Card */}
              <TouchableOpacity
                className="flex-1 bg-green-900 rounded-xl p-4 border border-green-500"
                onPress={handleBalanceClick}
              >
                <View className="flex-row items-center mb-2">
                  <DollarSign width={20} height={20} color="#34d399" />
                  <Text className="text-gray-400 text-sm ml-2">Money</Text>
                </View>
                <Text className="text-green-400 text-2xl font-bold">
                  {user.money?.toLocaleString() || 0}
                </Text>
                <Text className="text-green-300 text-xs mt-1">Tap to view history</Text>
              </TouchableOpacity>
            </View>

            {/* Gold Card */}
            <TouchableOpacity 
              className="bg-yellow-900 rounded-xl p-4 border border-yellow-500"
              onPress={handleBalanceClick}
            >
              <View className="flex-row items-center mb-2">
                <Coins width={20} height={20} color="#fbbf24" />
                <Text className="text-gray-400 text-sm ml-2">Gold</Text>
              </View>
              <Text className="text-yellow-400 text-2xl font-bold">
                {user.gold?.toLocaleString() || 0}
              </Text>
              <Text className="text-yellow-300 text-xs mt-1">Tap to view history</Text>
            </TouchableOpacity>
          </View>

          {/* Training Section */}
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <View className="flex-row items-center justify-center mb-6">
              <Dumbbell width={24} height={24} color="#60a5fa" />
              <Text className="text-2xl font-bold text-white ml-3">
                TRAINING CENTER
              </Text>
              {user.isPremium && (
                <View className="ml-3 bg-yellow-900 border border-yellow-500 px-3 py-1 rounded-xl flex-row items-center">
                  <Crown width={12} height={12} color="#fbbf24" />
                  <Text className="text-yellow-400 text-sm ml-1">Premium</Text>
                </View>
              )}
            </View>

            {isTraining ? (
              <View className="bg-gray-700 rounded-xl p-6 border border-blue-500 shadow-lg">
                <Text className="text-xl text-white mb-3 text-center">
                  Currently training {' '}
                  <Text className="font-bold text-blue-400 uppercase">
                    {user?.trainingPerk || ''}
                  </Text>
                </Text>
                <View className="mb-5">
                  <Text className="text-gray-400 text-center">Time remaining:</Text>
                  <View 
                    className={`rounded-xl px-4 py-2 mt-2 ${
                      countdown && countdown.includes('s') && !countdown.includes('m') && !countdown.includes('h')
                        ? 'bg-gradient-to-r from-red-900 to-red-800 animate-pulse'
                        : 'bg-gradient-to-r from-blue-900 to-indigo-900'
                    }`}
                  >
                    <Text className={`text-center font-mono font-semibold text-lg ${
                      countdown && countdown.includes('s') && !countdown.includes('m') && !countdown.includes('h')
                        ? 'text-red-400'
                        : 'text-white'
                    }`}>
                      {countdown}
                    </Text>
                  </View>
                </View>
                <View className="bg-gray-800 rounded-full h-3 mb-4 overflow-hidden">
                  <View
                    className="bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full relative"
                    style={{ width: `${user.trainingProgress || 0}%` }}
                  >
                    <View className="absolute inset-0 bg-white opacity-20 animate-pulse" />
                  </View>
                </View>
                <Text className="text-gray-300 text-sm text-center">
                  Your {user?.trainingPerk || 'skills'} will improve when training is complete
                </Text>
              </View>
            ) : (
              <View>
                <View className="bg-green-900 rounded-xl p-4 mb-6 border border-green-500">
                  <View className="flex-row items-center">
                    <View className="w-4 h-4 bg-green-400 rounded-full mr-3" />
                    <Text className="text-green-400 font-medium">
                      Ready to train - improve your skills now!
                    </Text>
                  </View>
                </View>

                <View className="space-y-4">
                  {[
                    { perk: 'strength', icon: Dumbbell, color: 'red' },
                    { perk: 'intelligence', icon: Brain, color: 'blue' },
                    { perk: 'endurance', icon: Shield, color: 'green' },
                  ].map(({ perk, icon: Icon, color }) => (
                    <View key={perk} className="bg-gray-700 rounded-xl overflow-hidden border border-gray-600">
                      <View className={`px-4 py-4 border-b border-gray-600 bg-${color}-900`}>
                        <View className="flex-row items-center justify-center">
                          <Icon width={24} height={24} color={color === 'red' ? '#ef4444' : color === 'blue' ? '#3b82f6' : '#10b981'} />
                          <Text className="text-2xl font-bold text-white ml-3 uppercase">
                            {perk}
                          </Text>
                        </View>
                      </View>

                      <View className="py-4 px-4 border-b border-gray-600">
                        <Text className="text-gray-400 text-sm text-center mb-2">
                          Current Level
                        </Text>
                        <Text className="text-4xl font-bold text-white text-center">
                          {String(user[perk as keyof typeof user] || 0)}
                        </Text>
                      </View>

                      <View className="p-4 space-y-4">
                        {/* Gold Training */}
                        <View className="bg-gray-800 rounded-xl p-4 border border-yellow-700">
                          <View className="flex-row justify-between items-center mb-3">
                            <View className="flex-row items-center">
                              <Coins width={20} height={20} color="#fbbf24" />
                              <Text className="text-yellow-400 font-semibold ml-2">
                                Gold Training
                              </Text>
                            </View>
                            <Text className="text-white font-bold">
                              {getTrainingInfo(perk).goldCost} 🪙
                            </Text>
                          </View>
                          <Text className="text-gray-300 text-sm mb-4">
                            Training time: {formatTime(getTrainingInfo(perk).goldTime)}
                            {user.isPremium && (
                              <Text className="text-yellow-300"> (Premium: 50% faster)</Text>
                            )}
                          </Text>
                          <TouchableOpacity
                            className="bg-yellow-600 py-3 rounded-xl"
                            onPress={() => handleTrain(perk, 'gold')}
                          >
                            <Text className="text-white font-medium text-center">
                              Train with Gold
                            </Text>
                          </TouchableOpacity>
                        </View>

                        {/* Money Training */}
                        <View className="bg-gray-800 rounded-xl p-4 border border-green-700">
                          <View className="flex-row justify-between items-center mb-3">
                            <View className="flex-row items-center">
                              <DollarSign width={20} height={20} color="#34d399" />
                              <Text className="text-green-400 font-semibold ml-2">
                                Money Training
                              </Text>
                            </View>
                            <Text className="text-white font-bold">
                              ${getTrainingInfo(perk).moneyCost.toLocaleString()} 💰
                            </Text>
                          </View>
                          <Text className="text-gray-300 text-sm mb-4">
                            Training time: {formatTime(getTrainingInfo(perk).moneyTime)}
                            {user.isPremium && (
                              <Text className="text-yellow-300"> (Premium: 50% faster)</Text>
                            )}
                          </Text>
                          <TouchableOpacity
                            className="bg-green-600 py-3 rounded-xl"
                            onPress={() => handleTrain(perk, 'money')}
                          >
                            <Text className="text-white font-medium text-center">
                              Train with Money
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>

          {/* State Information */}
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <View className="flex-row items-center mb-4">
              <Flag width={20} height={20} color="#ef4444" />
              <Text className="text-xl font-semibold text-white ml-2">My State</Text>
            </View>

            {myState ? (
              <View>
                <View className="flex-row items-center mb-6">
                  <View className="w-16 h-16 bg-red-900 rounded-xl items-center justify-center border border-red-500">
                    <Text className="text-2xl text-red-400">
                      {myState.name.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                  <View className="ml-4">
                    <Text className="text-lg font-medium text-white">
                      {myState.name}
                    </Text>
                    <Text className="text-gray-400">
                      Leader: {myState.leader?.username || 'Unknown'}
                    </Text>
                  </View>
                </View>

                <View className="space-y-3 mb-6">
                  <View className="flex-row justify-between bg-gray-700 rounded-lg p-3">
                    <View className="flex-row items-center">
                      <Globe width={16} height={16} color="#60a5fa" />
                      <Text className="text-gray-400 ml-2">Regions:</Text>
                    </View>
                    <Text className="text-white">{myState.regions?.length || 0}</Text>
                  </View>
                  <View className="flex-row justify-between bg-gray-700 rounded-lg p-3">
                    <View className="flex-row items-center">
                      <Landmark width={16} height={16} color="#fbbf24" />
                      <Text className="text-gray-400 ml-2">Treasury:</Text>
                    </View>
                    <View className="flex-row items-center">
                      <Text className="text-yellow-400">{myState.treasury?.toLocaleString() || 0}</Text>
                      <Coins width={16} height={16} color="#fbbf24" />
                    </View>
                  </View>
                  <View className="flex-row justify-between bg-gray-700 rounded-lg p-3">
                    <View className="flex-row items-center">
                      <BarChart3 width={16} height={16} color="#34d399" />
                      <Text className="text-gray-400 ml-2">Status:</Text>
                    </View>
                    <Text className={myState.isActive ? 'text-green-400' : 'text-red-400'}>
                      {myState.isActive ? 'Active' : 'Inactive'}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  className="bg-blue-600 py-3 px-4 rounded-xl"
                  onPress={() => navigation.navigate('StateDetail', { stateId: myState.id })}
                >
                  <Text className="text-white text-center font-medium">
                    View State Details
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View className="text-center py-4">
                <Text className="text-gray-400 mb-6">
                  You don't belong to any state yet.
                </Text>
                <TouchableOpacity
                  className="bg-blue-600 px-6 py-3 rounded-xl"
                  onPress={() => navigation.navigate('CreateState')}
                >
                  <Text className="text-white font-medium">Create a State</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>

          {/* Region Information */}
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <View className="flex-row items-center mb-4">
              <Globe width={20} height={20} color="#34d399" />
              <Text className="text-xl font-semibold text-white ml-2">My Region</Text>
            </View>

            {user?.region ? (
              <View>
                <View className="flex-row items-center mb-6">
                  <View className="w-16 h-16 bg-emerald-900 rounded-xl items-center justify-center border border-emerald-500">
                    <Text className="text-2xl text-emerald-400">
                      {user.region.name.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                  <View className="ml-4">
                    <Text className="text-lg font-medium text-white">
                      {user.region.name}
                    </Text>
                    <Text className="text-gray-400">
                      Population: {user.region?.population?.toLocaleString() || 'N/A'}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  className="bg-blue-600 py-3 px-4 rounded-xl"
                  onPress={() => navigation.navigate('RegionDetail', { regionId: user.region.id })}
                >
                  <Text className="text-white text-center font-medium">
                    View Region Details
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View className="text-center py-4">
                <Text className="text-gray-400">
                  You don't have a region assigned yet.
                </Text>
              </View>
            )}
          </View>

          {/* Party Information */}
          <View className="bg-gray-800 rounded-xl p-6 mb-6 border border-gray-700">
            <View className="flex-row items-center mb-4">
              <Users width={20} height={20} color="#8b5cf6" />
              <Text className="text-xl font-semibold text-white ml-2">My Party</Text>
            </View>

            {user?.leadingParty ? (
              <View>
                <View className="flex-row items-center mb-6">
                  <View className="w-16 h-16 bg-violet-900 rounded-xl items-center justify-center border border-violet-500">
                    <Text className="text-2xl text-violet-400">
                      {user.leadingParty.name?.charAt(0).toUpperCase() || 'P'}
                    </Text>
                  </View>
                  <View className="ml-4">
                    <Text className="text-lg font-medium text-white">
                      {user.leadingParty.name}
                    </Text>
                    <Text className="text-gray-400">
                      Leader: {user.username}
                    </Text>
                  </View>
                </View>

                <View className="space-y-3 mb-6">
                  <View className="flex-row justify-between bg-gray-700 rounded-lg p-3">
                    <View className="flex-row items-center">
                      <User width={16} height={16} color="#60a5fa" />
                      <Text className="text-gray-400 ml-2">Members:</Text>
                    </View>
                    <Text className="text-white">
                      {user.leadingParty.membersCount || user.leadingParty.members?.length || 1}
                    </Text>
                  </View>
                  <View className="flex-row justify-between bg-gray-700 rounded-lg p-3">
                    <View className="flex-row items-center">
                      <Globe width={16} height={16} color="#34d399" />
                      <Text className="text-gray-400 ml-2">Region:</Text>
                    </View>
                    <Text className="text-white">
                      {user.leadingParty.region?.name || 'Unknown'}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  className="bg-blue-600 py-3 px-4 rounded-xl"
                  onPress={() => navigation.navigate('PartyDetail', { partyId: user.leadingParty?.id })}
                >
                  <Text className="text-white text-center font-medium">
                    View Party Details
                  </Text>
                </TouchableOpacity>
              </View>
            ) : user?.memberOfParty ? (
              <View>
                <View className="flex-row items-center mb-6">
                  <View className="w-16 h-16 bg-violet-900 rounded-xl items-center justify-center border border-violet-500">
                    <Text className="text-2xl text-violet-400">
                      {user.memberOfParty.name?.charAt(0).toUpperCase() || 'P'}
                    </Text>
                  </View>
                  <View className="ml-4">
                    <Text className="text-lg font-medium text-white">
                      {user.memberOfParty.name}
                    </Text>
                    <Text className="text-gray-400">
                      Leader: {user.memberOfParty.leader?.username || 'Unknown'}
                    </Text>
                  </View>
                </View>

                <View className="space-y-3 mb-6">
                  <View className="flex-row justify-between bg-gray-700 rounded-lg p-3">
                    <View className="flex-row items-center">
                      <User width={16} height={16} color="#60a5fa" />
                      <Text className="text-gray-400 ml-2">Members:</Text>
                    </View>
                    <Text className="text-white">
                      {user.memberOfParty.membersCount || user.memberOfParty.members?.length || 1}
                    </Text>
                  </View>
                  <View className="flex-row justify-between bg-gray-700 rounded-lg p-3">
                    <View className="flex-row items-center">
                      <Globe width={16} height={16} color="#34d399" />
                      <Text className="text-gray-400 ml-2">Region:</Text>
                    </View>
                    <Text className="text-white">
                      {user.memberOfParty.region?.name || 'Unknown'}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  className="bg-blue-600 py-3 px-4 rounded-xl"
                  onPress={() => navigation.navigate('PartyDetail', { partyId: user.memberOfParty?.id })}
                >
                  <Text className="text-white text-center font-medium">
                    View Party Details
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View className="text-center py-4">
                <Text className="text-gray-400 mb-6">
                  You don't belong to any party yet.
                </Text>
                <TouchableOpacity
                  className="bg-blue-600 px-6 py-3 rounded-xl"
                  onPress={() => navigation.navigate('CreateParty')}
                >
                  <Text className="text-white font-medium">Create a Party</Text>
                </TouchableOpacity>
                <View className="flex-row items-center justify-center mt-3">
                  <Coins width={16} height={16} color="#fbbf24" />
                  <Text className="text-gray-400 text-sm ml-1">Cost: 200 gold</Text>
                </View>
              </View>
            )}
          </View>

          {/* My Wars */}
          <View className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <View className="flex-row items-center mb-4">
              <Flame width={20} height={20} color="#f97316" />
              <Text className="text-xl font-semibold text-white ml-2">My Wars</Text>
            </View>

            {myWars.length > 0 ? (
              <View className="space-y-4">
                {myWars.slice(0, 3).map((war) => (
                  <View
                    key={war.id}
                    className="border-b border-gray-700 pb-4 last:border-b-0 last:pb-0"
                  >
                    <View className="flex-row items-center mb-3">
                      <Flame width={16} height={16} color="#f97316" />
                      <Text className="text-lg font-medium text-white ml-2">
                        {war.warType} War
                      </Text>
                    </View>
                    <View className="space-y-2 mb-3">
                      <View className="flex-row justify-between bg-gray-700 rounded-lg p-2">
                        <Text className="text-gray-400">Attacker:</Text>
                        <TouchableOpacity
                          onPress={() => navigation.navigate('RegionDetail', { regionId: war.attackerRegion?.id })}
                        >
                          <Text className="text-blue-400">
                            {war.attackerRegion?.name || 'Unknown'}
                          </Text>
                        </TouchableOpacity>
                      </View>
                      <View className="flex-row justify-between bg-gray-700 rounded-lg p-2">
                        <Text className="text-gray-400">Defender:</Text>
                        <TouchableOpacity
                          onPress={() => navigation.navigate('RegionDetail', { regionId: war.defenderRegion?.id })}
                        >
                          <Text className="text-blue-400">
                            {war.defenderRegion?.name || 'Unknown'}
                          </Text>
                        </TouchableOpacity>
                      </View>
                      <View className="flex-row justify-between bg-gray-700 rounded-lg p-2">
                        <Text className="text-gray-400">Status:</Text>
                        <Text className={war.status === 'active' ? 'text-green-400' : 'text-gray-400'}>
                          {war.status}
                        </Text>
                      </View>
                    </View>
                    <TouchableOpacity
                      onPress={() => navigation.navigate('WarDetail', { warId: war.id })}
                    >
                      <View className="flex-row items-center">
                        <Trophy width={16} height={16} color="#f97316" />
                        <Text className="text-orange-400 text-sm ml-1">
                          View War Details
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            ) : (
              <View className="text-center py-4">
                <Text className="text-gray-400 mb-6">
                  You're not participating in any wars.
                </Text>
                <TouchableOpacity
                  className="bg-red-600 px-6 py-3 rounded-xl flex-row items-center justify-center"
                  onPress={() => navigation.navigate('DeclareWar')}
                >
                  <Flame width={16} height={16} color="#ffffff" />
                  <Text className="text-white font-medium ml-2">Declare War</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Share Profile Modal */}
      <Modal
        visible={showShareModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowShareModal(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.75)',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 16,
        }}>
          <View style={{
            backgroundColor: '#111827',
            borderRadius: 12,
            width: '100%',
            maxWidth: 400,
            padding: 24,
          }}>
            {/* Header */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 20,
            }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Share2 width={24} height={24} color="#60a5fa" />
                <Text style={{
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: '#ffffff',
                  marginLeft: 8,
                }}>
                  Share Your Profile
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => setShowShareModal(false)}
                style={{
                  padding: 8,
                  borderRadius: 8,
                  backgroundColor: '#374151',
                }}
              >
                <X width={24} height={24} color="#9CA3AF" />
              </TouchableOpacity>
            </View>

            {/* Content */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                color: '#D1D5DB',
                marginBottom: 12,
                fontSize: 16,
              }}>
                Share your profile with others using this link:
              </Text>
              <View style={{
                backgroundColor: '#374151',
                borderRadius: 8,
                padding: 12,
                borderWidth: 1,
                borderColor: '#4B5563',
              }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                  <Text style={{
                    color: '#60a5fa',
                    fontSize: 12,
                    fontFamily: 'monospace',
                    flex: 1,
                    marginRight: 12,
                  }} numberOfLines={2}>
                    {getShareableProfileUrl()}
                  </Text>
                  <TouchableOpacity
                    onPress={handleCopyProfileUrl}
                    style={{
                      backgroundColor: copySuccess ? '#059669' : '#3B82F6',
                      paddingHorizontal: 12,
                      paddingVertical: 8,
                      borderRadius: 8,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}
                  >
                    {copySuccess ? (
                      <>
                        <Check width={16} height={16} color="#ffffff" />
                        <Text style={{ color: '#ffffff', fontSize: 12, marginLeft: 4 }}>Copied!</Text>
                      </>
                    ) : (
                      <>
                        <Copy width={16} height={16} color="#ffffff" />
                        <Text style={{ color: '#ffffff', fontSize: 12, marginLeft: 4 }}>Copy</Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {/* Close Button */}
            <TouchableOpacity
              onPress={() => setShowShareModal(false)}
              style={{
                backgroundColor: '#374151',
                paddingVertical: 12,
                borderRadius: 8,
                alignItems: 'center',
              }}
            >
              <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '500' }}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Balance Logs Modal */}
      <Modal
        visible={balanceLogsVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setBalanceLogsVisible(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.75)',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 16,
        }}>
          <View style={{
            backgroundColor: '#111827',
            borderRadius: 12,
            width: '100%',
            maxWidth: 500,
            height: '80%',
            overflow: 'hidden',
          }}>
            {/* Header */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: 24,
              borderBottomWidth: 1,
              borderBottomColor: '#374151',
            }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#ffffff',
              }}>
                Transaction History
              </Text>
              <TouchableOpacity
                onPress={() => setBalanceLogsVisible(false)}
                style={{
                  padding: 8,
                  borderRadius: 8,
                  backgroundColor: '#374151',
                }}
              >
                <X width={24} height={24} color="#9CA3AF" />
              </TouchableOpacity>
            </View>

            {/* Content */}
            <View style={{ flex: 1, padding: 24 }}>
              {balanceLogsLoading ? (
                <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
                  <ActivityIndicator size="large" color="#3f87ff" />
                  <Text style={{ color: '#9CA3AF', marginTop: 8 }}>Loading transactions...</Text>
                </View>
              ) : balanceLogs.length === 0 ? (
                <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 40 }}>
                  <DollarSign width={48} height={48} color="#6B7280" />
                  <Text style={{ color: '#9CA3AF', marginTop: 16, textAlign: 'center' }}>
                    No transactions yet
                  </Text>
                  <Text style={{ color: '#6B7280', marginTop: 8, textAlign: 'center' }}>
                    Your money and gold transactions will appear here
                  </Text>
                </View>
              ) : (
                <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                  {balanceLogs.map((log) => (
                    <View
                      key={log.id}
                      style={{
                        backgroundColor: '#1F2937',
                        borderRadius: 8,
                        padding: 16,
                        marginBottom: 12,
                        borderLeftWidth: 4,
                        borderLeftColor: log.amount >= 0 ? '#10B981' : '#EF4444',
                      }}
                    >
                      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                        <Text style={{
                          fontSize: 16,
                          fontWeight: '600',
                          color: log.amount >= 0 ? '#10B981' : '#EF4444',
                        }}>
                          {log.amount >= 0 ? '+' : ''}{log.balanceType === 'gold' ? log.amount + ' Gold' : '$' + log.amount.toLocaleString()}
                        </Text>
                        <Text style={{ fontSize: 12, color: '#9CA3AF' }}>
                          {new Date(log.createdAt).toLocaleDateString()}
                        </Text>
                      </View>
                      <Text style={{ fontSize: 14, color: '#D1D5DB', marginBottom: 4 }}>
                        {log.description}
                      </Text>
                      <Text style={{ fontSize: 12, color: '#6B7280', textTransform: 'capitalize' }}>
                        Type: {log.transactionType.replace(/_/g, ' ')}
                      </Text>
                    </View>
                  ))}
                </ScrollView>
              )}
            </View>
          </View>
        </View>
      </Modal>

      {/* Balance Logs Modal */}
      <BalanceLogsModal
        visible={balanceLogsVisible}
        onClose={() => setBalanceLogsVisible(false)}
        initialBalanceType="money"
      />
    </View>
  );
};