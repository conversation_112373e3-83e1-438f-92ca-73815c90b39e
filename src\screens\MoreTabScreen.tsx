import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { theme } from '../styles/theme';
import { useAuthStore } from '../store/useAuthStore';

interface MenuOption {
  id: string;
  title: string;
  subtitle: string;
  icon: string;
  screen: string;
  color: string;
}

interface Props {
  navigation: any;
}

export const MoreTabScreen: React.FC<Props> = ({ navigation }) => {
  const { user, logout } = useAuthStore();

  const menuOptions: MenuOption[] = [
    {
      id: 'elections',
      title: 'Elections',
      subtitle: 'Vote for leaders',
      icon: '🗳️',
      screen: 'Elections',
      color: theme.colors.warning,
    },
    {
      id: 'map',
      title: 'World Map',
      subtitle: 'Explore the world',
      icon: '🌍',
      screen: 'WorldMap',
      color: theme.colors.primary,
    },
    {
      id: 'travel',
      title: 'Travel',
      subtitle: 'Manage travel permissions',
      icon: '🧳',
      screen: 'Travel',
      color: theme.colors.error,
    },
    {
      id: 'my-state',
      title: 'My State',
      subtitle: 'Your political state',
      icon: '🏛️',
      screen: 'MyState',
      color: theme.colors.primary,
    },
    {
      id: 'network-debug',
      title: 'Network Debug',
      subtitle: 'Test connectivity issues',
      icon: '🔧',
      screen: 'NetworkDebug',
      color: theme.colors.error,
    },
  ];

  const handleMenuPress = (option: MenuOption) => {
    if (option.screen === 'Profile') {
      // Navigate to Profile tab instead
      navigation.navigate('Home', { screen: 'Profile' });
    } else {
      navigation.navigate(option.screen);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  return (
    <ScrollView style={styles.container}>
      {/* User Info Header */}
      {user && (
        <View style={styles.userHeader}>
          <View style={styles.userInfo}>
            <Text style={styles.username}>{user.username}</Text>
            <Text style={styles.userLevel}>Level {user.level}</Text>
            <View style={styles.userStats}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user.energy}</Text>
                <Text style={styles.statLabel}>Energy</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user.gold}</Text>
                <Text style={styles.statLabel}>Gold</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user.money}</Text>
                <Text style={styles.statLabel}>Money</Text>
              </View>
            </View>
          </View>
          {user.isPremium && (
            <View style={styles.premiumBadge}>
              <Text style={styles.premiumText}>PREMIUM</Text>
            </View>
          )}
        </View>
      )}

      {/* Menu Options */}
      <View style={styles.menuContainer}>
        <Text style={styles.sectionTitle}>Game Features</Text>
        
        <View style={styles.menuGrid}>
          {menuOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={styles.menuItem}
              onPress={() => handleMenuPress(option)}
            >
              <View style={[styles.menuIcon, { backgroundColor: option.color + '20' }]}>
                <Text style={styles.menuIconText}>{option.icon}</Text>
              </View>
              <View style={styles.menuTextContainer}>
                <Text style={styles.menuTitle}>{option.title}</Text>
                <Text style={styles.menuSubtitle}>{option.subtitle}</Text>
              </View>
              <Text style={styles.menuArrow}>›</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Settings Section */}
      <View style={styles.settingsContainer}>
        <Text style={styles.sectionTitle}>Settings</Text>
        
        <TouchableOpacity style={styles.settingItem}>
          <Text style={styles.settingIcon}>⚙️</Text>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingTitle}>Settings</Text>
            <Text style={styles.settingSubtitle}>App preferences</Text>
          </View>
          <Text style={styles.menuArrow}>›</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.settingItem}>
          <Text style={styles.settingIcon}>📊</Text>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingTitle}>Statistics</Text>
            <Text style={styles.settingSubtitle}>View your game stats</Text>
          </View>
          <Text style={styles.menuArrow}>›</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.settingItem}>
          <Text style={styles.settingIcon}>ℹ️</Text>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingTitle}>About</Text>
            <Text style={styles.settingSubtitle}>App version and info</Text>
          </View>
          <Text style={styles.menuArrow}>›</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.settingItem, styles.logoutItem]}
          onPress={handleLogout}
        >
          <Text style={styles.settingIcon}>🚪</Text>
          <View style={styles.settingTextContainer}>
            <Text style={[styles.settingTitle, styles.logoutText]}>Logout</Text>
            <Text style={styles.settingSubtitle}>Sign out of your account</Text>
          </View>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  userHeader: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    flexDirection: 'row',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  username: {
    fontSize: theme.fontSize.title,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  userLevel: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.primary,
    marginBottom: theme.spacing.md,
  },
  userStats: {
    flexDirection: 'row',
  },
  statItem: {
    alignItems: 'center',
    marginRight: theme.spacing.xl,
  },
  statValue: {
    fontSize: theme.fontSize.medium,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
  },
  statLabel: {
    fontSize: theme.fontSize.small,
    color: theme.colors.textSecondary,
  },
  premiumBadge: {
    backgroundColor: theme.colors.premium,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.medium,
  },
  premiumText: {
    color: theme.colors.text,
    fontSize: theme.fontSize.small,
    fontWeight: theme.fontWeight.bold,
  },
  menuContainer: {
    padding: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  menuGrid: {
    gap: theme.spacing.md,
  },
  menuItem: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
    padding: theme.spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    width: 50,
    height: 50,
    borderRadius: theme.borderRadius.medium,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  menuIconText: {
    fontSize: theme.fontSize.title,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  menuSubtitle: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
  },
  menuArrow: {
    fontSize: theme.fontSize.title,
    color: theme.colors.textMuted,
    fontWeight: theme.fontWeight.bold,
  },
  settingsContainer: {
    padding: theme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border + '40',
  },
  logoutItem: {
    borderBottomWidth: 0,
  },
  settingIcon: {
    fontSize: theme.fontSize.title,
    marginRight: theme.spacing.lg,
    width: 30,
    textAlign: 'center',
  },
  settingTextContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: theme.fontSize.large,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  settingSubtitle: {
    fontSize: theme.fontSize.medium,
    color: theme.colors.textSecondary,
  },
  logoutText: {
    color: theme.colors.error,
  },
});