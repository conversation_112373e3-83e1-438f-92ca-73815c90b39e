import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  PanResponder,
  Animated,
} from 'react-native';
import Svg, { Path, G, Circle, Text as SvgText } from 'react-native-svg';
import { MapPin, ZoomIn, ZoomOut, Maximize, Search, Info } from 'lucide-react-native';

interface MapProps {
  onRegionSelect?: (region: any) => void;
  onStateSelect?: (state: any) => void;
  onRegionCheck?: (regionId: string) => void;
  mapMode?: 'political' | 'wars' | 'targets';
  wars?: any[];
  availableTargets?: any[];
  showControls?: boolean;
  interactive?: boolean;
  height?: number;
  states?: any[];
  geoJsonData?: any;
  loading?: boolean;
  mapReady?: boolean;
  mapRegion?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  onMapReady?: () => void;
  onRegionChangeComplete?: (region: any) => void;
  convertGeoJSONToPolygons?: () => React.ReactNode[];
}

// Helper function to convert GeoJSON coordinates to SVG path
const coordinatesToPath = (coordinates: number[][][], width: number, height: number) => {
  if (!coordinates || coordinates.length === 0) return '';

  // Simple projection: convert lat/lng to x/y
  const project = (lng: number, lat: number) => {
    const x = ((lng + 180) / 360) * width;
    const y = ((90 - lat) / 180) * height;
    return [x, y];
  };

  let path = '';
  coordinates.forEach((ring) => {
    if (ring.length === 0) return;

    const [startX, startY] = project(ring[0][0], ring[0][1]);
    path += `M${startX},${startY}`;

    for (let i = 1; i < ring.length; i++) {
      const [x, y] = project(ring[i][0], ring[i][1]);
      path += `L${x},${y}`;
    }
    path += 'Z';
  });

  return path;
};

export const Map: React.FC<MapProps> = ({
  onRegionSelect,
  onStateSelect,
  onRegionCheck,
  mapMode = 'political',
  wars = [],
  availableTargets = [],
  showControls = true,
  interactive = true,
  height = 300,
  states = [],
  geoJsonData,
  loading = false,
  onMapReady,
}) => {
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [selectedRegion, setSelectedRegion] = useState<any>(null);
  const [showRegionModal, setShowRegionModal] = useState(false);

  const screenWidth = Dimensions.get('window').width;
  const mapWidth = screenWidth - 32;
  const mapHeight = height || 300;

  useEffect(() => {
    if (onMapReady) {
      onMapReady();
    }
  }, [onMapReady]);

  const handleCountryClick = (feature: any) => {
    const countryName = feature.properties?.name || feature.id;
    if (onRegionSelect) {
      onRegionSelect({
        id: feature.properties?.ISO_A2 || countryName,
        name: countryName,
        properties: feature.properties
      });
    }
  };

  const isCountryInWar = (countryCode: string) => {
    return wars.some(war =>
      war.attackerRegion?.countryCode === countryCode ||
      war.defenderRegion?.countryCode === countryCode ||
      war.targetRegion?.countryCode === countryCode
    );
  };

  const isCountryAvailableTarget = (countryCode: string) => {
    return availableTargets?.some(target => target.countryCode === countryCode);
  };

  const getCountryColor = (feature: any) => {
    const countryCode = feature.properties?.ISO_A2;
    const countryName = feature.properties?.name;

    // Find if this country belongs to a state
    const countryState = states.find(state =>
      state.regions?.some((region: any) =>
        region.countryCode === countryCode || region.name === countryName
      )
    );

    if (mapMode === 'wars' && isCountryInWar(countryCode)) {
      return '#ef4444'; // Red for war zones
    }
    if (mapMode === 'targets' && isCountryAvailableTarget(countryCode)) {
      return '#f59e0b'; // Orange for available targets
    }

    if (countryState) {
      // Generate consistent color for the state
      const stateColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
      return stateColors[Math.abs(countryState.id.charCodeAt(0)) % stateColors.length];
    }

    return '#2d3748'; // Default unclaimed color
  };

  const renderGeoJSONFeatures = () => {
    if (!geoJsonData?.features) return null;

    return geoJsonData.features.map((feature: any, index: number) => {
      if (feature.geometry?.type !== 'Polygon' && feature.geometry?.type !== 'MultiPolygon') {
        return null;
      }

      const color = getCountryColor(feature);
      let coordinates = feature.geometry.coordinates;

      // Handle MultiPolygon
      if (feature.geometry.type === 'MultiPolygon') {
        coordinates = coordinates[0]; // Take the first polygon for simplicity
      }

      const pathData = coordinatesToPath([coordinates], mapWidth, mapHeight);

      return (
        <Path
          key={`country-${index}`}
          d={pathData}
          fill={color}
          stroke="#4a5568"
          strokeWidth={0.5}
          opacity={0.8}
          onClick={() => handleCountryClick(feature)}
        />
      );
    });
  };

  if (loading) {
    return (
      <View style={[styles.container, { height: mapHeight }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3f87ff" />
          <Text style={styles.loadingText}>Loading world map...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { height: mapHeight }]}>
      {/* Map Controls */}
      {showControls && (
        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => setZoom(Math.min(zoom * 1.2, 3))}
          >
            <ZoomIn width={20} height={20} color="#ffffff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => setZoom(Math.max(zoom / 1.2, 0.5))}
          >
            <ZoomOut width={20} height={20} color="#ffffff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => {
              setZoom(1);
              setPan({ x: 0, y: 0 });
            }}
          >
            <Maximize width={20} height={20} color="#ffffff" />
          </TouchableOpacity>
        </View>
      )}

      {/* SVG Map */}
      <View style={styles.mapContainer}>
        <Svg width={mapWidth} height={mapHeight} viewBox={`0 0 ${mapWidth} ${mapHeight}`}>
          {/* Background */}
          <Path
            d={`M0,0 L${mapWidth},0 L${mapWidth},${mapHeight} L0,${mapHeight} Z`}
            fill="#1e293b"
            stroke="none"
          />
          
          {/* Render GeoJSON countries */}
          {renderGeoJSONFeatures()}
        </Svg>
      </View>

      {/* Legend */}
      {mapMode !== 'political' && (
        <View style={styles.legend}>
          {mapMode === 'wars' && (
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: '#ef4444' }]} />
              <Text style={styles.legendText}>War Zones</Text>
            </View>
          )}
          {mapMode === 'targets' && (
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: '#f59e0b' }]} />
              <Text style={styles.legendText}>Available Targets</Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#1f2937',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    backgroundColor: '#1f2937',
  },
  loadingText: {
    color: '#3f87ff',
    fontSize: 16,
    marginTop: 16,
  },
  controls: {
    position: 'absolute' as const,
    top: 16,
    right: 16,
    zIndex: 1000,
    flexDirection: 'column' as const,
    gap: 8,
  },
  controlButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    padding: 8,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  mapContainer: {
    flex: 1,
    overflow: 'hidden' as const,
  },
  legend: {
    position: 'absolute' as const,
    bottom: 16,
    left: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 8,
    padding: 12,
    zIndex: 1000,
  },
  legendItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 4,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    color: '#ffffff',
    fontSize: 12,
  },
};
