import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import {
  Crown,
  Coins,
  Check,
  Clock,
  XCircle,
  X,
  Star,
  Zap,
  Shield,
} from 'lucide-react-native';
import { useAuthStore } from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { stripeService } from '../services/stripeService';
import Toast from 'react-native-toast-message';

interface Props {
  navigation: any;
}

enum PremiumPlan {
  MONTHLY = 'monthly',
  SEMIANNUAL = 'semiannual',
  YEARLY = 'yearly',
}

enum GoldPackageType {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  EXTRA_LARGE = 'extra_large',
}

interface SubscriptionPlan {
  id: PremiumPlan;
  name: string;
  price: string;
  period: string;
  features: string[];
  popular: boolean;
  savings?: string;
}

interface GoldPackage {
  id: GoldPackageType;
  name: string;
  amount: number;
  price: string;
  bonus: number;
  popular: boolean;
}

export const ShopScreen: React.FC<Props> = ({ navigation }) => {
  useAuthGuard({ navigation });
  
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingSubscription, setLoadingSubscription] = useState(false);
  const [loadingGold, setLoadingGold] = useState(false);
  const [selectedGoldPackage, setSelectedGoldPackage] = useState<string | null>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancellingSubscription, setCancellingSubscription] = useState(false);
  const [subscriptionInfo, setSubscriptionInfo] = useState({
    isActive: false,
    endsAt: null,
    status: null,
    premiumExpiresAt: null,
  });
  const [trialEligible, setTrialEligible] = useState(true);
  const [trialInfo, setTrialInfo] = useState<any>(null);

  const isPremium = user?.isPremium || false;

  const subscriptionPlans: SubscriptionPlan[] = [
    {
      id: PremiumPlan.MONTHLY,
      name: "Monthly Premium",
      price: "€2.00",
      period: "per month",
      features: [
        "50% faster training times",
        "Auto-mode in wars",
        "Premium badge",
        "Priority customer support",
      ],
      popular: false,
    },
    {
      id: PremiumPlan.SEMIANNUAL,
      name: "6-Month Premium",
      price: "€9.00",
      period: "per 6 months",
      features: [
        "50% faster training times",
        "Auto-mode in wars",
        "Premium badge",
        "Priority customer support",
        "25% discount compared to monthly",
      ],
      popular: true,
      savings: "€3.00",
    },
    {
      id: PremiumPlan.YEARLY,
      name: "Yearly Premium",
      price: "€12.00",
      period: "per year",
      features: [
        "50% faster training times",
        "Auto-mode in wars",
        "Premium badge",
        "Priority customer support",
        "50% discount compared to monthly",
      ],
      popular: false,
      savings: "€12.00",
    },
  ];

  const goldPackages: GoldPackage[] = [
    {
      id: GoldPackageType.SMALL,
      name: "Starter Pack",
      amount: 1000,
      price: "€4.99",
      bonus: 100,
      popular: false,
    },
    {
      id: GoldPackageType.MEDIUM,
      name: "Advanced Pack",
      amount: 3000,
      price: "€9.99",
      bonus: 500,
      popular: false,
    },
    {
      id: GoldPackageType.LARGE,
      name: "Elite Pack",
      amount: 10000,
      price: "€24.99",
      bonus: 2000,
      popular: true,
    },
    {
      id: GoldPackageType.EXTRA_LARGE,
      name: "Ultimate Pack",
      amount: 25000,
      price: "€49.99",
      bonus: 5000,
      popular: false,
    },
  ];

  useEffect(() => {
    loadShopData();
  }, []);

  const loadShopData = async () => {
    try {
      setLoading(true);
      
      // Check trial eligibility
      const trialInfo = await stripeService.checkTrialEligibility();
      setTrialEligible(trialInfo.eligible);
      setTrialInfo(trialInfo);
      
      if (isPremium) {
        // Get real subscription info
        const subInfo = await stripeService.verifyPremiumSubscription();
        setSubscriptionInfo({
          isActive: subInfo.isActive,
          endsAt: subInfo.currentPeriodEnd,
          status: subInfo.cancelAtPeriodEnd ? 'cancel_at_period_end' : 'active',
          premiumExpiresAt: subInfo.currentPeriodEnd,
        });
      }
    } catch (error) {
      console.error('Error loading shop data:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load shop data',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadShopData();
  };

  const handleSubscribe = async (planId: PremiumPlan) => {
    if (isPremium) {
      Toast.show({
        type: 'info',
        text1: 'Already Premium',
        text2: 'You already have an active premium subscription',
      });
      return;
    }

    setLoadingSubscription(true);
    try {
      // Create real Stripe checkout session
      const trialDays = trialEligible ? 14 : undefined;
      const session = await stripeService.createPremiumCheckoutSession(planId, trialDays);
      
      // In a real mobile app, you would:
      // 1. Use react-native-webview to open session.url
      // 2. Or use Stripe's mobile SDK
      // For now, we'll navigate to the payment success/cancel screens based on user action
      
      Alert.alert(
        'Payment Redirect',
        'You will be redirected to Stripe to complete your payment.',
        [
          { 
            text: 'Cancel', 
            style: 'cancel',
            onPress: () => {
              navigation.navigate('PaymentCancel', { 
                type: 'subscription', 
                plan: planId 
              });
            }
          },
          {
            text: 'Continue',
            onPress: () => {
              // In production mobile app, open session.url in webview or Stripe mobile SDK
              // For now, navigate to success page (backend webhook handles actual payment)
              navigation.navigate('PaymentSuccess', { 
                type: 'subscription', 
                plan: planId 
              });
            }
          }
        ]
      );
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Subscription Failed',
        text2: error.message || 'Failed to create checkout session',
      });
    } finally {
      setLoadingSubscription(false);
    }
  };

  const handleBuyGold = async (packageId: GoldPackageType) => {
    const goldPackage = goldPackages.find(pkg => pkg.id === packageId);
    if (!goldPackage) return;

    setLoadingGold(true);
    setSelectedGoldPackage(packageId);
    
    try {
      // Create real Stripe checkout session for gold
      const session = await stripeService.createGoldCheckoutSession(packageId);
      
      Alert.alert(
        'Payment Redirect',
        `Purchase ${goldPackage.name} for ${goldPackage.price}?\n\nYou will receive ${goldPackage.amount.toLocaleString()} gold${goldPackage.bonus ? ` + ${goldPackage.bonus} bonus` : ''}.\n\nYou will be redirected to Stripe to complete your payment.`,
        [
          { 
            text: 'Cancel', 
            style: 'cancel',
            onPress: () => {
              navigation.navigate('PaymentCancel', { 
                type: 'gold', 
                package: packageId 
              });
            }
          },
          {
            text: 'Purchase',
            onPress: () => {
              // In production mobile app, open session.url in webview or Stripe mobile SDK
              // For now, navigate to success page (backend webhook handles actual payment)
              navigation.navigate('PaymentSuccess', { 
                type: 'gold', 
                package: packageId,
                amount: goldPackage.amount + goldPackage.bonus
              });
            }
          }
        ]
      );
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Purchase Failed',
        text2: error.message || 'Failed to create checkout session',
      });
    } finally {
      setLoadingGold(false);
      setSelectedGoldPackage(null);
    }
  };

  const handleCancelSubscription = async () => {
    setCancellingSubscription(true);
    try {
      // Use real Stripe service to cancel subscription
      const result = await stripeService.cancelPremiumSubscription();
      
      Toast.show({
        type: 'success',
        text1: 'Subscription Cancelled',
        text2: 'Your subscription has been cancelled successfully.',
      });
      
      setSubscriptionInfo(prev => ({
        ...prev,
        status: 'cancel_at_period_end',
      }));
      
      setShowCancelDialog(false);
      
      // Refresh user data
      await loadShopData();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Cancellation Failed',
        text2: error.message || 'Please try again later',
      });
    } finally {
      setCancellingSubscription(false);
    }
  };

  const renderSubscriptionPlan = (plan: SubscriptionPlan) => (
    <View
      key={plan.id}
      className={`bg-gray-800 rounded-lg overflow-hidden mb-4 ${
        plan.popular ? 'border-2 border-neonBlue' : 'border border-gray-700'
      }`}
    >
      {plan.popular && (
        <View className="bg-neonBlue py-2">
          <Text className="text-white text-center font-bold">MOST POPULAR</Text>
        </View>
      )}
      
      <View className="p-6">
        <Text className="text-xl font-bold text-white mb-2">{plan.name}</Text>
        
        <View className="mb-4">
          {trialEligible && plan.id === PremiumPlan.MONTHLY ? (
            <View>
              <Text className="text-green-400 text-sm mb-1">14-Day Free Trial</Text>
              <Text className="text-3xl font-bold text-white">
                {plan.price}
              </Text>
              <Text className="text-gray-400">{plan.period}</Text>
            </View>
          ) : (
            <View>
              <Text className="text-3xl font-bold text-white">
                {plan.price}
              </Text>
              <Text className="text-gray-400">{plan.period}</Text>
            </View>
          )}
          {plan.savings && (
            <Text className="text-green-400 text-sm mt-1">
              Save {plan.savings} compared to monthly
            </Text>
          )}
        </View>

        <View className="space-y-3 mb-6">
          {trialEligible && plan.id === PremiumPlan.MONTHLY && (
            <View className="flex-row items-start">
              <Check width={16} height={16} color="#22c55e" />
              <Text className="text-green-300 font-semibold ml-2">
                14-day free trial included
              </Text>
            </View>
          )}
          {plan.features.map((feature, index) => (
            <View key={index} className="flex-row items-start">
              <Check width={16} height={16} color="#22c55e" />
              <Text className="text-gray-300 ml-2">{feature}</Text>
            </View>
          ))}
        </View>

        <TouchableOpacity
          onPress={() => handleSubscribe(plan.id)}
          disabled={loadingSubscription || isPremium || !trialEligible}
          className={`w-full py-3 rounded-lg ${
            isPremium || !trialEligible
              ? 'bg-gray-600'
              : plan.popular
              ? 'bg-green-600'
              : 'bg-gray-700'
          }`}
        >
          <Text className="text-white font-medium text-center">
            {isPremium
              ? 'Already Subscribed'
              : loadingSubscription
              ? 'Processing...'
              : 'Subscribe Now'}
          </Text>
        </TouchableOpacity>

        {isPremium && (
          <Text className="text-green-400 text-sm text-center mt-2">
            You already have an active premium subscription
          </Text>
        )}
        {!trialEligible && !isPremium && (
          <Text className="text-red-400 text-sm text-center mt-2">
            You have already used your free trial
          </Text>
        )}
      </View>
    </View>
  );

  const renderGoldPackage = (pkg: GoldPackage) => (
    <View
      key={pkg.id}
      className={`bg-gray-800 rounded-lg overflow-hidden mb-4 ${
        pkg.popular ? 'border-2 border-yellow-500' : 'border border-gray-700'
      }`}
    >
      {pkg.popular && (
        <View className="bg-yellow-500 py-2">
          <Text className="text-white text-center font-bold">BEST VALUE</Text>
        </View>
      )}
      
      <View className="p-5">
        <Text className="text-lg font-bold text-white mb-2">{pkg.name}</Text>
        
        <View className="flex-row items-center justify-center mb-4">
          <Coins width={24} height={24} color="#fbbf24" />
          <Text className="text-2xl font-bold text-white ml-2">
            {pkg.amount.toLocaleString()}
          </Text>
          {pkg.bonus && (
            <View className="bg-green-900 px-2 py-1 rounded ml-2">
              <Text className="text-green-400 text-xs">+{pkg.bonus} BONUS</Text>
            </View>
          )}
        </View>

        <View className="items-center mb-5">
          <Text className="text-xl font-bold text-white">{pkg.price}</Text>
        </View>

        <TouchableOpacity
          onPress={() => handleBuyGold(pkg.id)}
          disabled={loadingGold}
          className={`w-full py-2 rounded-lg ${
            pkg.popular ? 'bg-yellow-600' : 'bg-gray-700'
          }`}
        >
          <Text className="text-white font-medium text-center">
            {loadingGold && selectedGoldPackage === pkg.id
              ? 'Processing...'
              : 'Buy Now'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View className="flex-1 bg-gray-900 justify-center items-center">
        <ActivityIndicator size="large" color="#00d4ff" />
        <Text className="text-neonBlue text-xl mt-4">Loading shop...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-900">
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View className="p-4">
          {/* Header */}
          <View className="text-center mb-8">
            <Text className="text-4xl font-bold text-neonBlue mb-4">Shop</Text>
            <Text className="text-gray-400">
              Purchase premium subscriptions and gold packages
            </Text>
          </View>

          {/* Current User Status */}
          {user && (
            <View className="bg-gray-800 rounded-lg p-4 mb-6">
              <Text className="text-xl font-semibold text-white mb-3">Your Account</Text>
              <View className="flex-row justify-between">
                <View className="items-center">
                  <Text className="text-2xl font-bold text-yellow-400">
                    {user.gold?.toLocaleString() || 0}
                  </Text>
                  <Text className="text-gray-400 text-sm">Gold</Text>
                </View>
                <View className="items-center">
                  <Text className="text-2xl font-bold text-green-400">
                    {user.money?.toLocaleString() || 0}
                  </Text>
                  <Text className="text-gray-400 text-sm">Money</Text>
                </View>
                <View className="items-center">
                  <View className="flex-row items-center">
                    {isPremium ? (
                      <Crown width={20} height={20} color="#fbbf24" />
                    ) : (
                      <Star width={20} height={20} color="#6b7280" />
                    )}
                    <Text className={`ml-1 font-bold ${isPremium ? 'text-yellow-400' : 'text-gray-500'}`}>
                      {isPremium ? 'Premium' : 'Standard'}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          )}

          {/* Premium Subscription Status */}
          {isPremium && (
            <View className="bg-gray-800 rounded-lg p-6 mb-8 border-2 border-yellow-500">
              <View className="items-center mb-6">
                <View className="flex-row items-center">
                  <Crown width={24} height={24} color="#fbbf24" />
                  <Text className="text-2xl font-bold text-white ml-2">
                    Your Premium Subscription
                  </Text>
                </View>
              </View>

              <View className="items-center mb-6">
                <View className={`px-5 py-2 rounded-lg ${
                  subscriptionInfo.status === 'cancel_at_period_end'
                    ? 'bg-yellow-900'
                    : subscriptionInfo.status === 'trialing'
                    ? 'bg-blue-900'
                    : 'bg-green-900'
                }`}>
                  <Text className={`font-medium ${
                    subscriptionInfo.status === 'cancel_at_period_end'
                      ? 'text-yellow-300'
                      : subscriptionInfo.status === 'trialing'
                      ? 'text-blue-400'
                      : 'text-green-400'
                  }`}>
                    Status: {
                      subscriptionInfo.status === 'cancel_at_period_end'
                        ? 'Canceling'
                        : subscriptionInfo.status === 'trialing'
                        ? 'Trial'
                        : 'Active'
                    }
                  </Text>
                </View>

                {subscriptionInfo.premiumExpiresAt && (
                  <View className="bg-gray-700 px-5 py-2 rounded-lg mt-2">
                    <Text className="text-gray-300">
                      Premium expires: {new Date(subscriptionInfo.premiumExpiresAt).toLocaleDateString()}
                    </Text>
                  </View>
                )}
              </View>

              {subscriptionInfo.status === 'cancel_at_period_end' && (
                <View className="bg-yellow-900 border border-yellow-700 p-4 rounded-lg mb-6">
                  <View className="flex-row items-center">
                    <XCircle width={16} height={16} color="#fbbf24" />
                    <Text className="text-yellow-300 ml-2">
                      Your subscription has been canceled but will remain active until{' '}
                      {subscriptionInfo.premiumExpiresAt
                        ? new Date(subscriptionInfo.premiumExpiresAt).toLocaleDateString()
                        : 'the end of the current billing period'}
                    </Text>
                  </View>
                </View>
              )}

              <View className="bg-gray-700 p-6 rounded-lg mb-6">
                <Text className="text-white font-semibold mb-4 text-center">
                  {subscriptionInfo.status === 'trialing' ? 'Active Trial Benefits:' : 'Active Premium Benefits:'}
                </Text>
                <View className="space-y-3">
                  {[
                    '50% faster training times',
                    'Auto-mode in wars',
                    'Premium badge',
                    'Priority customer support'
                  ].map((benefit, index) => (
                    <View key={index} className="flex-row items-start">
                      <Check width={16} height={16} color="#22c55e" />
                      <Text className="text-gray-300 ml-2">{benefit}</Text>
                    </View>
                  ))}
                </View>
              </View>

              {(subscriptionInfo.status === 'active' || subscriptionInfo.status === 'trialing') && (
                <View>
                  <Text className="text-gray-400 mb-6 text-center">
                    {subscriptionInfo.status === 'trialing'
                      ? 'You can cancel your trial at any time. Your trial benefits will end immediately.'
                      : 'You can cancel your subscription at any time. Your premium benefits will remain active until the end of your current billing period.'}
                  </Text>

                  <TouchableOpacity
                    onPress={() => setShowCancelDialog(true)}
                    className="bg-gray-900 border border-red-600 py-3 px-6 rounded-lg"
                  >
                    <Text className="text-white font-medium text-center">
                      {subscriptionInfo.status === 'trialing' ? 'Cancel Trial' : 'Cancel Subscription'}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          )}

          {/* Premium Subscription Section */}
          <View className="mb-12">
            <View className="items-center mb-8">
              <View className="flex-row items-center">
                <Crown width={32} height={32} color="#fbbf24" />
                <Text className="text-3xl font-bold text-white ml-2">
                  Premium Subscription
                </Text>
              </View>
              <Text className="text-gray-400 mt-2 text-center">
                {trialEligible
                  ? 'Start your 2-week free trial today!'
                  : 'Unlock exclusive features and gain competitive advantages'}
              </Text>
              {trialEligible && (
                <View className="bg-green-600 p-3 rounded-lg mt-4">
                  <Text className="text-white font-bold text-center">🎉 Free Trial Available!</Text>
                  <Text className="text-white text-sm text-center mt-1">
                    Try premium features for 14 days, then €2.00/month
                  </Text>
                </View>
              )}
            </View>

            {subscriptionPlans.map(renderSubscriptionPlan)}
          </View>

          {/* Gold Packages Section */}
          <View className="mb-12">
            <View className="items-center mb-8">
              <View className="flex-row items-center">
                <Coins width={32} height={32} color="#fbbf24" />
                <Text className="text-3xl font-bold text-white ml-2">
                  Gold Packages
                </Text>
              </View>
              <Text className="text-gray-400 mt-2 text-center">
                Purchase gold to accelerate your progress and unlock special features
              </Text>
            </View>

            {goldPackages.map(renderGoldPackage)}
          </View>

          {/* Benefits Section */}
          <View className="bg-gray-800 rounded-lg p-6">
            <Text className="text-2xl font-bold text-white mb-6 text-center">
              Premium Benefits
            </Text>
            <View className="space-y-6">
              <View className="bg-gray-700 p-5 rounded-lg">
                <View className="flex-row items-center mb-4">
                  <Clock width={24} height={24} color="#00d4ff" />
                  <Text className="text-lg font-semibold text-white ml-3">
                    Faster Training
                  </Text>
                </View>
                <Text className="text-gray-300">
                  Premium members enjoy 50% faster training times, allowing you to
                  develop your character more quickly.
                </Text>
              </View>

              <View className="bg-gray-700 p-5 rounded-lg">
                <View className="flex-row items-center mb-4">
                  <Shield width={24} height={24} color="#fbbf24" />
                  <Text className="text-lg font-semibold text-white ml-3">
                    Auto-Mode in Wars
                  </Text>
                </View>
                <Text className="text-gray-300">
                  Set up automatic participation in war/work, ensuring you never
                  miss a battle/work even when you're offline.
                </Text>
              </View>

              <View className="bg-gray-700 p-5 rounded-lg">
                <View className="flex-row items-center mb-4">
                  <Coins width={24} height={24} color="#fbbf24" />
                  <Text className="text-lg font-semibold text-white ml-3">
                    Gold Benefits
                  </Text>
                </View>
                <Text className="text-gray-300">
                  Use gold to accelerate training, purchase special items, and
                  gain advantages in various game aspects.
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Cancellation Confirmation Modal */}
      <Modal
        visible={showCancelDialog}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCancelDialog(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
          <View className="bg-gray-800 rounded-lg max-w-md w-full p-6">
            <View className="items-center mb-4">
              <XCircle width={48} height={48} color="#ef4444" />
            </View>
            
            <Text className="text-xl font-bold text-white mb-4 text-center">
              {subscriptionInfo.status === 'trialing' ? 'Cancel Free Trial?' : 'Cancel Premium Subscription?'}
            </Text>
            
            <Text className="text-gray-300 mb-6 text-center">
              {subscriptionInfo.status === 'trialing'
                ? 'Are you sure you want to cancel your free trial? You will lose access to all premium features immediately.'
                : 'Are you sure you want to cancel your premium subscription? You will lose access to all premium features at the end of your current billing period.'}
            </Text>
            
            <View className="flex-row space-x-4">
              <TouchableOpacity
                onPress={() => setShowCancelDialog(false)}
                disabled={cancellingSubscription}
                className="flex-1 bg-gray-700 py-2 px-4 rounded-lg"
              >
                <Text className="text-white font-medium text-center">Keep Subscription</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                onPress={handleCancelSubscription}
                disabled={cancellingSubscription}
                className="flex-1 bg-red-600 py-2 px-4 rounded-lg"
              >
                <Text className="text-white font-medium text-center">
                  {cancellingSubscription
                    ? 'Cancelling...'
                    : subscriptionInfo.status === 'trialing'
                    ? 'Yes, Cancel Trial'
                    : 'Yes, Cancel'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};