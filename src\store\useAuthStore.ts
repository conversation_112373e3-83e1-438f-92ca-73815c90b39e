import { create } from 'zustand';
import { User } from '../types/user';
import { authAPI } from '../services/api';
import { userService } from '../services/userService';
import { OAuthService } from '../services/oauthService';
import { isTokenValid } from '../utils/tokenUtils';
import { secureStorage } from '../utils/secureStorage';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: (accessToken: string) => Promise<void>;
  register: (username: string, email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  loadToken: () => Promise<void>;
  setUser: (user: User) => void;
  refreshUser: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: true, // Start as loading since we need to check stored token
  token: null,

  login: async (email: string, password: string) => {
    try {
      set({ isLoading: true });
      const response = await authAPI.login(email, password);
      
      await secureStorage.setItem('access_token', response.access_token);

      try {
        const userData = await userService.getCurrentUser();
        // Store user data in SecureStore
        await secureStorage.setItem('user', JSON.stringify(userData));
        set({ 
          user: userData,
          token: response.access_token,
          isAuthenticated: true 
        });
      } catch (userError) {
        console.error('Error fetching user data:', userError);
        set({ 
          token: response.access_token,
          isAuthenticated: true 
        });
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  loginWithGoogle: async (accessToken: string) => {
    try {
      set({ isLoading: true });
      
      // Get IP address (optional)
      let ipAddress;
      try {
        const ipResponse = await fetch('https://api.ipify.org?format=json');
        const ipData = await ipResponse.json();
        ipAddress = ipData.ip;
      } catch (ipError) {
        console.warn('Could not get IP address:', ipError);
      }

      const response = await OAuthService.loginWithGoogle(accessToken, ipAddress);
      
      await secureStorage.setItem('access_token', response.access_token);
      await secureStorage.setItem('user', JSON.stringify(response.user));
      
      set({ 
        user: response.user,
        token: response.access_token,
        isAuthenticated: true 
      });
    } catch (error) {
      console.error('Google login error:', error);
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  register: async (username: string, email: string, password: string) => {
    try {
      set({ isLoading: true });
      const response = await authAPI.register(username, email, password);
      
      // Note: Registration usually doesn't auto-login, user needs to verify email first
      // But if your backend does auto-login after registration, uncomment below:
      /*
      await AsyncStorage.setItem('access_token', response.access_token);
      
      try {
        const userData = await userService.getCurrentUser();
        set({ 
          user: userData,
          token: response.access_token,
          isAuthenticated: true 
        });
      } catch (userError) {
        console.error('Error fetching user data:', userError);
        set({ 
          token: response.access_token,
          isAuthenticated: true 
        });
      }
      */
    } catch (error) {
      console.error('Register error:', error);
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  logout: async () => {
    try {
      await secureStorage.deleteItem('access_token');
      await secureStorage.deleteItem('user');
      set({ 
        user: null, 
        isAuthenticated: false, 
        token: null 
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
  },

  loadToken: async () => {
    try {
      console.log('🔄 Starting loadToken...');
      set({ isLoading: true });
      const token = await secureStorage.getItem('access_token');
      const storedUser = await secureStorage.getItem('user');
      console.log('🔑 Token exists:', !!token);
      console.log('👤 Stored user exists:', !!storedUser);
      
      if (token && isTokenValid(token)) {
        console.log('✅ Token is valid');
        // Token is valid, restore authentication state
        let userData = null;
        
        // Try to get user from storage first
        if (storedUser) {
          try {
            userData = JSON.parse(storedUser);
          } catch (parseError) {
            console.warn('Failed to parse stored user data:', parseError);
          }
        }
        
        // If no stored user or parsing failed, fetch from API
        if (!userData) {
          try {
            userData = await userService.getCurrentUser();
            // Store user data for next time
            await secureStorage.setItem('user', JSON.stringify(userData));
          } catch (userError) {
            console.error('Error fetching user data on load:', userError);
            // Clear invalid token
            await secureStorage.deleteItem('access_token');
            await secureStorage.deleteItem('user');
            set({ 
              user: null, 
              isAuthenticated: false, 
              token: null,
              isLoading: false
            });
            return;
          }
        }
        
        set({ 
          token, 
          user: userData,
          isAuthenticated: true 
        });
      } else {
        console.log('❌ Token is invalid or doesn\'t exist');
        // Token is invalid or doesn't exist, clear everything
        await secureStorage.deleteItem('access_token');
        await secureStorage.deleteItem('user');
        set({ 
          user: null, 
          isAuthenticated: false, 
          token: null 
        });
      }
    } catch (error) {
      console.error('Load token error:', error);
      // Clear everything on error
      await secureStorage.deleteItem('access_token');
      await secureStorage.deleteItem('user');
      set({ 
        user: null, 
        isAuthenticated: false, 
        token: null 
      });
    } finally {
      console.log('✅ loadToken completed');
      set({ isLoading: false });
    }
  },

  setUser: (user: User) => {
    set({ user });
  },

  refreshUser: async () => {
    try {
      const userData = await userService.getCurrentUser();
      await secureStorage.setItem('user', JSON.stringify(userData));
      set({ user: userData });
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  },
}));